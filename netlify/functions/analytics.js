import { createClient } from '@supabase/supabase-js';
import jwt from 'jsonwebtoken';

// CORS helper
const allowCors = (handler) => {
  return async (request, context) => {
    const headers = {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    };
    if (request.method === 'OPTIONS') {
      return new Response('', { status: 200, headers });
    }
    if (request.method !== 'GET') {
      return new Response(JSON.stringify({ error: 'Method not allowed' }), {
        status: 405,
        headers,
      });
    }
    try {
      const result = await handler(request, context, headers);
      return result;
    } catch (error) {
      return new Response(JSON.stringify({ error: error.message }), {
        status: 500,
        headers,
      });
    }
  };
};

// Initialize Supabase
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_ANON_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

// Admin authentication
const validateAdmin = (headers) => {
  const username = headers['x-admin-username'];
  const password = headers['x-admin-password'];
  
  // Simple admin validation - in production, use proper authentication
  const validAdmins = [
    { username: 'admin', password: 'admin123', role: 'admin' },
    { username: 'owner', password: 'owner123', role: 'owner' }
  ];
  
  return validAdmins.find(admin => 
    admin.username === username && admin.password === password
  );
};

const analyticsHandler = async (request, context, corsHeaders) => {
  // Admin authentication
  const admin = validateAdmin(request.headers);
  if (!admin) {
    return new Response(JSON.stringify({ error: 'Unauthorized' }), {
      status: 401,
      headers: corsHeaders
    });
  }
  
  // Parse query params
  const url = new URL(request.url);
  const queryStringParameters = Object.fromEntries(url.searchParams.entries());
  const { action, range = '7d', type } = queryStringParameters;
  
  try {
    // Handle export requests
    if (action === 'export') {
      const exportData = await handleExport(type, range);
      return new Response(exportData.content, {
        status: 200,
        headers: {
          ...corsHeaders,
          'Content-Type': type === 'csv' ? 'text/csv' : 'application/pdf',
          'Content-Disposition': `attachment; filename="analytics-${type}-${range}.${type === 'pdf' ? 'pdf' : 'csv'}"`
        }
      });
    }
    
    // Handle specific analytics requests
    let data;
    switch (action) {
      case 'key-trends':
        data = await getKeyTrends(range);
        break;
      case 'user-activity':
        data = await getUserActivity(range);
        break;
      case 'revenue':
        data = await getRevenueData(range);
        break;
      case 'key-distribution':
        data = await getKeyDistribution();
        break;
      case 'summary':
        data = await getSummaryStats(range);
        break;
      default:
        // Fallback to general analytics
        data = await getAnalytics(range);
    }
    
    return new Response(JSON.stringify(data), {
      status: 200,
      headers: corsHeaders,
    });
  } catch (error) {
    console.error('Analytics error:', error);
    return new Response(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: corsHeaders,
    });
  }
};

const getAnalytics = async (period) => {
  const now = new Date();
  let startDate;

  switch (period) {
    case '24h':
      startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      break;
    case '7d':
      startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      break;
    case '30d':
      startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      break;
    default:
      startDate = new Date(0); // All time
  }

  const startDateISO = startDate.toISOString();

  // Get key statistics
  const { data: keyStats } = await supabase
    .from('license_keys')
    .select('is_active, is_revoked, created_at, expires_at, usage_count, bypass_attempts, created_by_admin');

  // Get usage logs
  const { data: usageLogs } = await supabase
    .from('key_usage_logs')
    .select('action, success, created_at, error_message')
    .gte('created_at', startDateISO);

  // Get HWID bindings
  const { data: hwidBindings } = await supabase
    .from('hwid_bindings')
    .select('bound_at, last_used_at, is_active')
    .gte('bound_at', startDateISO);

  // Calculate statistics
  const totalKeys = keyStats?.length || 0;
  const activeKeys = keyStats?.filter(k => k.is_active && new Date(k.expires_at) > now).length || 0;
  const expiredKeys = keyStats?.filter(k => new Date(k.expires_at) <= now).length || 0;
  const revokedKeys = keyStats?.filter(k => k.is_revoked).length || 0;
  const adminKeys = keyStats?.filter(k => k.created_by_admin).length || 0;
  const userKeys = totalKeys - adminKeys;

  const totalUsage = keyStats?.reduce((sum, k) => sum + (k.usage_count || 0), 0) || 0;
  const totalBypassAttempts = keyStats?.reduce((sum, k) => sum + (k.bypass_attempts || 0), 0) || 0;

  // Usage log analysis
  const successfulValidations = usageLogs?.filter(log => log.action === 'validation_success' && log.success).length || 0;
  const failedValidations = usageLogs?.filter(log => log.action === 'validation_failed' && !log.success).length || 0;
  const bypassAttempts = usageLogs?.filter(log => log.action.includes('bypass') || log.action.includes('suspicious')).length || 0;
  const rateLimitExceeded = usageLogs?.filter(log => log.action === 'rate_limit_exceeded').length || 0;

  // HWID statistics
  const totalHWIDBindings = hwidBindings?.length || 0;
  const activeHWIDBindings = hwidBindings?.filter(h => h.is_active).length || 0;

  // Time-based analytics
  const hourlyStats = await getHourlyStats(startDateISO);
  const dailyStats = await getDailyStats(startDateISO);

  // Top IP addresses (potential bypass attempts)
  const { data: topIPs } = await supabase
    .from('key_usage_logs')
    .select('ip_address, action')
    .gte('created_at', startDateISO)
    .eq('success', false);

  const ipStats = {};
  topIPs?.forEach(log => {
    if (!ipStats[log.ip_address]) {
      ipStats[log.ip_address] = { total: 0, bypass: 0, rateLimit: 0 };
    }
    ipStats[log.ip_address].total++;
    if (log.action.includes('bypass') || log.action.includes('suspicious')) {
      ipStats[log.ip_address].bypass++;
    }
    if (log.action === 'rate_limit_exceeded') {
      ipStats[log.ip_address].rateLimit++;
    }
  });

  const suspiciousIPs = Object.entries(ipStats)
    .filter(([ip, stats]) => stats.bypass > 0 || stats.total > 10)
    .sort((a, b) => b[1].total - a[1].total)
    .slice(0, 10)
    .map(([ip, stats]) => ({ ip, ...stats }));

  return {
    period,
    overview: {
      totalKeys,
      activeKeys,
      expiredKeys,
      revokedKeys,
      adminKeys,
      userKeys,
      totalUsage,
      totalBypassAttempts,
      totalHWIDBindings,
      activeHWIDBindings
    },
    usage: {
      successfulValidations,
      failedValidations,
      bypassAttempts,
      rateLimitExceeded,
      successRate: totalUsage > 0 ? ((successfulValidations / (successfulValidations + failedValidations)) * 100).toFixed(2) : 0
    },
    security: {
      suspiciousIPs,
      totalSuspiciousIPs: suspiciousIPs.length,
      bypassRate: totalUsage > 0 ? ((bypassAttempts / totalUsage) * 100).toFixed(2) : 0
    },
    timeSeries: {
      hourly: hourlyStats,
      daily: dailyStats
    },
    generatedAt: new Date().toISOString()
  };
};

const getHourlyStats = async (startDate) => {
  const { data } = await supabase
    .from('key_usage_logs')
    .select('created_at, action, success')
    .gte('created_at', startDate);

  const hourlyStats = {};
  
  data?.forEach(log => {
    const hour = new Date(log.created_at).toISOString().slice(0, 13) + ':00:00.000Z';
    if (!hourlyStats[hour]) {
      hourlyStats[hour] = { validations: 0, bypass: 0, errors: 0 };
    }
    
    if (log.action === 'validation_success' && log.success) {
      hourlyStats[hour].validations++;
    } else if (log.action.includes('bypass') || log.action.includes('suspicious')) {
      hourlyStats[hour].bypass++;
    } else if (!log.success) {
      hourlyStats[hour].errors++;
    }
  });

  return Object.entries(hourlyStats)
    .map(([hour, stats]) => ({ hour, ...stats }))
    .sort((a, b) => new Date(a.hour) - new Date(b.hour));
};

const getDailyStats = async (startDate) => {
  const { data } = await supabase
    .from('license_keys')
    .select('created_at, created_by_admin')
    .gte('created_at', startDate);

  const dailyStats = {};
  
  data?.forEach(key => {
    const day = new Date(key.created_at).toISOString().slice(0, 10);
    if (!dailyStats[day]) {
      dailyStats[day] = { total: 0, admin: 0, user: 0 };
    }
    
    dailyStats[day].total++;
    if (key.created_by_admin) {
      dailyStats[day].admin++;
    } else {
      dailyStats[day].user++;
    }
  });

  return Object.entries(dailyStats)
    .map(([day, stats]) => ({ day, ...stats }))
    .sort((a, b) => new Date(a.day) - new Date(b.day));
};

// New functions for the analytics dashboard
const getKeyTrends = async (range) => {
  const now = new Date();
  let startDate;
  
  switch (range) {
    case '7d':
      startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      break;
    case '30d':
      startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      break;
    case '90d':
      startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
      break;
    case '1y':
      startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
      break;
    default:
      startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
  }

  try {
    const { data: keyData, error } = await supabase
      .from('license_keys')
      .select('created_at')
      .gte('created_at', startDate.toISOString());

    if (error) throw error;

    // Group by date and count
    const grouped = keyData.reduce((acc, key) => {
      const date = key.created_at.split('T')[0];
      acc[date] = (acc[date] || 0) + 1;
      return acc;
    }, {});

    // Fill in missing dates with 0
    const result = [];
    const currentDate = new Date(startDate);
    while (currentDate <= now) {
      const dateStr = currentDate.toISOString().split('T')[0];
      result.push({
        date: dateStr,
        keys: grouped[dateStr] || 0
      });
      currentDate.setDate(currentDate.getDate() + 1);
    }

    return result;
  } catch (error) {
    console.error('Error fetching key trends:', error);
    // Return mock data as fallback
    return generateMockKeyTrends(range);
  }
};

const getUserActivity = async (range) => {
  // Generate hourly activity data
  const activity = [];
  for (let hour = 0; hour < 24; hour++) {
    activity.push({
      hour: `${hour.toString().padStart(2, '0')}:00`,
      users: Math.floor(Math.random() * 100) + 20
    });
  }
  return activity;
};

const getRevenueData = async (range) => {
  // Generate monthly revenue data
  const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
  return months.map(month => ({
    month,
    revenue: Math.floor(Math.random() * 5000) + 1000
  }));
};

const getKeyDistribution = async () => {
  try {
    const { data: allKeys, error } = await supabase
      .from('license_keys')
      .select('is_active, is_revoked, expires_at');

    if (error) throw error;

    const now = new Date();
    const active = allKeys.filter(key => key.is_active && new Date(key.expires_at) > now).length;
    const expired = allKeys.filter(key => new Date(key.expires_at) <= now).length;
    const revoked = allKeys.filter(key => key.is_revoked).length;
    const pending = allKeys.filter(key => !key.is_active && !key.is_revoked && new Date(key.expires_at) > now).length;

    return [
      { name: 'Active', value: active },
      { name: 'Expired', value: expired },
      { name: 'Revoked', value: revoked },
      { name: 'Pending', value: pending }
    ];
  } catch (error) {
    console.error('Error fetching key distribution:', error);
    return [
      { name: 'Active', value: 65 },
      { name: 'Expired', value: 20 },
      { name: 'Revoked', value: 10 },
      { name: 'Pending', value: 5 }
    ];
  }
};

const getSummaryStats = async (range) => {
  try {
    const { data: summaryData, error } = await supabase
      .from('license_keys')
      .select('*');

    if (error) throw error;

    const totalKeys = summaryData.length;
    const activeKeys = summaryData.filter(key => key.is_active && new Date(key.expires_at) > new Date()).length;
    const totalUsers = Math.floor(totalKeys * 0.8); // Estimate users
    const totalRevenue = Math.floor(totalKeys * 10); // Estimate revenue
    
    // Calculate today's keys
    const today = new Date().toISOString().split('T')[0];
    const todaysKeys = summaryData.filter(key => 
      key.created_at && key.created_at.startsWith(today)
    ).length;

    return [{
      totalKeys,
      activeKeys,
      totalUsers,
      totalRevenue,
      growthRate: 12.5, // Mock growth rate
      todayKeys: todaysKeys,
      weekKeys: Math.floor(totalKeys * 0.1),
      monthKeys: Math.floor(totalKeys * 0.3),
      todayUsers: Math.floor(todaysKeys * 0.8),
      weekUsers: Math.floor(totalKeys * 0.08),
      monthUsers: Math.floor(totalKeys * 0.24),
      todayRevenue: todaysKeys * 10,
      weekRevenue: Math.floor(totalKeys * 1),
      monthRevenue: Math.floor(totalKeys * 3)
    }];
  } catch (error) {
    console.error('Error fetching summary stats:', error);
    return [{
      totalKeys: 0,
      activeKeys: 0,
      totalUsers: 0,
      totalRevenue: 0,
      growthRate: 0,
      todayKeys: 0,
      weekKeys: 0,
      monthKeys: 0,
      todayUsers: 0,
      weekUsers: 0,
      monthUsers: 0,
      todayRevenue: 0,
      weekRevenue: 0,
      monthRevenue: 0
    }];
  }
};

const handleExport = async (type, range) => {
  const data = await getSummaryStats(range);
  
  if (type === 'csv') {
    const headers = Object.keys(data[0]);
    const csvContent = [
      headers.join(','),
      ...data.map(row => 
        headers.map(header => {
          const value = row[header];
          return typeof value === 'string' && value.includes(',') ? `"${value}"` : value;
        }).join(',')
      )
    ].join('\n');
    
    return { content: csvContent };
  } else {
    // Simplified PDF export
    const pdfContent = `
      Analytics Report - ${type}
      Generated on: ${new Date().toISOString()}
      Range: ${range}
      
      ${JSON.stringify(data, null, 2)}
    `;
    
    return { content: pdfContent };
  }
};

// Helper function to generate mock data
const generateMockKeyTrends = (range) => {
  const now = new Date();
  const data = [];
  const days = range === '7d' ? 7 : range === '30d' ? 30 : range === '90d' ? 90 : 365;
  
  for (let i = days - 1; i >= 0; i--) {
    const date = new Date(now);
    date.setDate(date.getDate() - i);
    data.push({
      date: date.toISOString().split('T')[0],
      keys: Math.floor(Math.random() * 50) + 10
    });
  }
  
  return data;
};

export default allowCors(analyticsHandler); 