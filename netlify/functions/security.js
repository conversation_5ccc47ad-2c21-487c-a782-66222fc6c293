import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_ANON_KEY
);

export const handler = async (event, context) => {
  // CORS headers
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, x-admin-username, x-admin-password',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS'
  };

  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: ''
    };
  }

  // Admin authentication using DB
  const adminUsername = event.headers['x-admin-username'];
  const adminPassword = event.headers['x-admin-password'];

  if (!adminUsername || !adminPassword) {
    return {
      statusCode: 401,
      headers,
      body: JSON.stringify({ error: 'Admin credentials required' })
    };
  }

  // Fetch admin user from DB
  const { data: adminUser, error: adminError } = await supabase
    .from('admin_users')
    .select('*')
    .eq('username', adminUsername)
    .single();

  if (adminError || !adminUser) {
    return {
      statusCode: 401,
      headers,
      body: JSON.stringify({ error: 'Invalid admin credentials' })
    };
  }

  // Check password (PLAIN TEXT, NOT SECURE)
  if (adminPassword !== adminUser.password_hash) {
    return {
      statusCode: 401,
      headers,
      body: JSON.stringify({ error: 'Invalid admin credentials' })
    };
  }

  // Attach role for access control
  const adminRole = adminUser.role;

  const { action } = event.queryStringParameters || {};

  try {
    switch (action) {
      case 'ip-bans':
        return await handleIpBans(event, headers, adminRole);
      case 'ban-ip':
        if (adminRole !== 'owner') {
          return { statusCode: 403, headers, body: JSON.stringify({ error: 'Owner role required' }) };
        }
        return await handleBanIp(event, headers, adminUsername);
      case 'unban-ip':
        if (adminRole !== 'owner') {
          return { statusCode: 403, headers, body: JSON.stringify({ error: 'Owner role required' }) };
        }
        return await handleUnbanIp(event, headers, adminUsername);
      case 'events':
        return await handleSecurityEvents(event, headers);
      case 'settings':
        return await handleSecuritySettings(event, headers);
      case 'update-setting':
        if (adminRole !== 'owner') {
          return { statusCode: 403, headers, body: JSON.stringify({ error: 'Owner role required' }) };
        }
        return await handleUpdateSetting(event, headers, adminUsername);
      case 'admin-actions':
        if (adminRole !== 'owner') {
          return { statusCode: 403, headers, body: JSON.stringify({ error: 'Owner role required' }) };
        }
        return await handleAdminActions(event, headers);
      // --- Script Requests ---
      case 'script-requests':
        return await handleScriptRequests(event, headers);
      case 'update-script-request':
        return await handleUpdateScriptRequest(event, headers, adminUsername);
      case 'delete-script-request':
        return await handleDeleteScriptRequest(event, headers, adminUsername);
      // --- Scripts ---
      case 'scripts':
        return await handleScripts(event, headers);
      case 'create-script':
        return await handleCreateScript(event, headers, adminUsername);
      case 'update-script':
        return await handleUpdateScript(event, headers, adminUsername);
      case 'delete-script':
        return await handleDeleteScript(event, headers, adminUsername);
      case 'update-log':
        return await handleUpdateLog(event, headers, adminUsername);
      // --- Key Management ---
      case 'keys':
        return await handleKeys(event, headers);
      case 'create-key':
        return await handleCreateKey(event, headers, adminUsername);
      case 'update-key':
        return await handleUpdateKey(event, headers, adminUsername);
      case 'delete-key':
        return await handleDeleteKey(event, headers, adminUsername);
      default:
        return {
          statusCode: 400,
          headers,
          body: JSON.stringify({ error: 'Invalid action' })
        };
    }
  } catch (error) {
    console.error('Security function error:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Internal server error' })
    };
  }
};

async function handleIpBans(event, headers, adminRole) {
  const { data, error } = await supabase
    .from('ip_bans')
    .select('*')
    .order('banned_at', { ascending: false });

  if (error) {
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: error.message })
    };
  }

  return {
    statusCode: 200,
    headers,
    body: JSON.stringify({ data, role: adminRole })
  };
}

async function handleBanIp(event, headers, adminUsername) {
  const body = JSON.parse(event.body);
  const { ip_address, reason, duration_hours, notes } = body;

  if (!ip_address || !reason) {
    return {
      statusCode: 400,
      headers,
      body: JSON.stringify({ error: 'IP address and reason are required' })
    };
  }

  const expiresAt = duration_hours > 0 
    ? new Date(Date.now() + (duration_hours * 60 * 60 * 1000)).toISOString()
    : null;

  const { data, error } = await supabase
    .from('ip_bans')
    .upsert({
      ip_address,
      reason,
      banned_by: adminUsername,
      expires_at: expiresAt,
      notes,
      is_active: true
    })
    .select();

  if (error) {
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: error.message })
    };
  }

  // Log admin action
  await supabase
    .from('admin_security_actions')
    .insert({
      admin_username: adminUsername,
      action_type: 'ip_ban',
      target_identifier: ip_address,
      details: { reason, duration_hours, notes },
      ip_address: event.headers['client-ip'] || event.headers['x-forwarded-for'] || 'unknown'
    });

  return {
    statusCode: 200,
    headers,
    body: JSON.stringify(data[0])
  };
}

async function handleUnbanIp(event, headers, adminUsername) {
  const body = JSON.parse(event.body);
  const { ip_address } = body;

  if (!ip_address) {
    return {
      statusCode: 400,
      headers,
      body: JSON.stringify({ error: 'IP address is required' })
    };
  }

  const { data, error } = await supabase
    .from('ip_bans')
    .update({ is_active: false })
    .eq('ip_address', ip_address)
    .select();

  if (error) {
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: error.message })
    };
  }

  // Log admin action
  await supabase
    .from('admin_security_actions')
    .insert({
      admin_username: adminUsername,
      action_type: 'ip_unban',
      target_identifier: ip_address,
      details: {},
      ip_address: event.headers['client-ip'] || event.headers['x-forwarded-for'] || 'unknown'
    });

  return {
    statusCode: 200,
    headers,
    body: JSON.stringify({ message: 'IP unbanned successfully' })
  };
}

async function handleSecurityEvents(event, headers) {
  const { data, error } = await supabase
    .from('security_events')
    .select('*')
    .order('created_at', { ascending: false })
    .limit(100);

  if (error) {
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: error.message })
    };
  }

  return {
    statusCode: 200,
    headers,
    body: JSON.stringify(data)
  };
}

async function handleSecuritySettings(event, headers) {
  const { data, error } = await supabase
    .from('security_settings')
    .select('*')
    .order('setting_key');

  if (error) {
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: error.message })
    };
  }

  return {
    statusCode: 200,
    headers,
    body: JSON.stringify(data)
  };
}

async function handleUpdateSetting(event, headers, adminUsername) {
  const body = JSON.parse(event.body);
  const { setting_key, setting_value } = body;

  if (!setting_key || setting_value === undefined) {
    return {
      statusCode: 400,
      headers,
      body: JSON.stringify({ error: 'Setting key and value are required' })
    };
  }

  const { data, error } = await supabase
    .from('security_settings')
    .update({ 
      setting_value: setting_value.toString(),
      updated_by: adminUsername,
      updated_at: new Date().toISOString()
    })
    .eq('setting_key', setting_key)
    .select();

  if (error) {
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: error.message })
    };
  }

  // Log admin action
  await supabase
    .from('admin_security_actions')
    .insert({
      admin_username: adminUsername,
      action_type: 'security_setting_change',
      target_identifier: setting_key,
      details: { old_value: data[0]?.setting_value, new_value: setting_value },
      ip_address: event.headers['client-ip'] || event.headers['x-forwarded-for'] || 'unknown'
    });

  return {
    statusCode: 200,
    headers,
    body: JSON.stringify(data[0])
  };
}

async function handleAdminActions(event, headers) {
  const { data, error } = await supabase
    .from('admin_security_actions')
    .select('*')
    .order('performed_at', { ascending: false })
    .limit(100);

  if (error) {
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: error.message })
    };
  }

  return {
    statusCode: 200,
    headers,
    body: JSON.stringify(data)
  };
}

// --- Script Requests Handlers ---
async function handleScriptRequests(event, headers) {
  const { data, error } = await supabase
    .from('script_requests')
    .select('*')
    .order('requested_at', { ascending: false });
  if (error) {
    return { statusCode: 500, headers, body: JSON.stringify({ error: error.message }) };
  }
  return { statusCode: 200, headers, body: JSON.stringify(data) };
}

async function handleUpdateScriptRequest(event, headers, adminUsername) {
  const body = JSON.parse(event.body);
  const { id, status, admin_notes } = body;
  if (!id || !status) {
    return { statusCode: 400, headers, body: JSON.stringify({ error: 'ID and status required' }) };
  }
  const { data, error } = await supabase
    .from('script_requests')
    .update({ status, admin_notes, reviewed_by: adminUsername, reviewed_at: new Date().toISOString() })
    .eq('id', id)
    .select();
  if (error) {
    return { statusCode: 500, headers, body: JSON.stringify({ error: error.message }) };
  }
  return { statusCode: 200, headers, body: JSON.stringify(data[0]) };
}

async function handleDeleteScriptRequest(event, headers, adminUsername) {
  const id = event.queryStringParameters?.id;
  if (!id) {
    return { statusCode: 400, headers, body: JSON.stringify({ error: 'ID required' }) };
  }
  const { error } = await supabase
    .from('script_requests')
    .delete()
    .eq('id', id);
  if (error) {
    return { statusCode: 500, headers, body: JSON.stringify({ error: error.message }) };
  }
  return { statusCode: 200, headers, body: JSON.stringify({ message: 'Deleted' }) };
}

// --- Scripts Handlers ---
async function handleScripts(event, headers) {
  const { data, error } = await supabase
    .from('scripts')
    .select('*')
    .order('created_at', { ascending: false });
  if (error) {
    return { statusCode: 500, headers, body: JSON.stringify({ error: error.message }) };
  }
  return { statusCode: 200, headers, body: JSON.stringify(data) };
}

async function handleCreateScript(event, headers, adminUsername) {
  const body = JSON.parse(event.body);
  if (!body.name) {
    return { statusCode: 400, headers, body: JSON.stringify({ error: 'Script name required' }) };
  }
  body.uploaded_by = adminUsername;
  const { data, error } = await supabase
    .from('scripts')
    .insert(body)
    .select();
  if (error) {
    return { statusCode: 500, headers, body: JSON.stringify({ error: error.message }) };
  }
  return { statusCode: 200, headers, body: JSON.stringify(data[0]) };
}

async function handleUpdateScript(event, headers, adminUsername) {
  const body = JSON.parse(event.body);
  if (!body.id) {
    return { statusCode: 400, headers, body: JSON.stringify({ error: 'Script ID required' }) };
  }
  body.updated_by = adminUsername;
  body.updated_at = new Date().toISOString();
  const { data, error } = await supabase
    .from('scripts')
    .update(body)
    .eq('id', body.id)
    .select();
  if (error) {
    return { statusCode: 500, headers, body: JSON.stringify({ error: error.message }) };
  }
  return { statusCode: 200, headers, body: JSON.stringify(data[0]) };
}

async function handleDeleteScript(event, headers, adminUsername) {
  const id = event.queryStringParameters?.id;
  if (!id) {
    return { statusCode: 400, headers, body: JSON.stringify({ error: 'ID required' }) };
  }
  const { error } = await supabase
    .from('scripts')
    .delete()
    .eq('id', id);
  if (error) {
    return { statusCode: 500, headers, body: JSON.stringify({ error: error.message }) };
  }
  return { statusCode: 200, headers, body: JSON.stringify({ message: 'Deleted' }) };
}

async function handleUpdateLog(event, headers, adminUsername) {
  const body = JSON.parse(event.body);
  if (!body.script_id || !body.version || !body.changes) {
    return { statusCode: 400, headers, body: JSON.stringify({ error: 'script_id, version, and changes required' }) };
  }
  const { data, error } = await supabase
    .from('script_update_logs')
    .insert({
      script_id: body.script_id,
      version: body.version,
      changes: body.changes,
      updated_by: adminUsername,
      updated_at: new Date().toISOString()
    })
    .select();
  if (error) {
    return { statusCode: 500, headers, body: JSON.stringify({ error: error.message }) };
  }
  return { statusCode: 200, headers, body: JSON.stringify(data[0]) };
}

// --- Key Management Handlers ---
async function handleKeys(event, headers) {
  const params = event.queryStringParameters || {};
  let query = supabase
    .from('license_keys')
    .select('*')
    .order('created_at', { ascending: false });

  if (params.status && params.status !== 'all') {
    if (params.status === 'active') query = query.eq('is_active', true).eq('is_revoked', false);
    if (params.status === 'expired') query = query.lt('expires_at', new Date().toISOString());
    if (params.status === 'revoked') query = query.eq('is_revoked', true);
  }
  if (params.search) {
    query = query.ilike('key_code', `%${params.search}%`);
  }
  // Pagination
  const page = parseInt(params.page) || 1;
  const limit = parseInt(params.limit) || 50;
  const from = (page - 1) * limit;
  const to = from + limit - 1;
  query = query.range(from, to);

  const { data, error, count } = await query;
  if (error) {
    return { statusCode: 500, headers, body: JSON.stringify({ error: error.message }) };
  }
  // For pagination, count is not always returned by Supabase free tier, so just return data
  return { statusCode: 200, headers, body: JSON.stringify({ keys: data, pagination: { page, limit, count: data.length, pages: 1 } }) };
}

async function handleCreateKey(event, headers, adminUsername) {
  const body = JSON.parse(event.body);
  const expiresInHours = parseInt(body.expiresInHours) || 24;
  const expiresAt = new Date(Date.now() + expiresInHours * 60 * 60 * 1000).toISOString();
  // Generate a random key code (simple example)
  const keyCode = 'MADARA-' + Math.random().toString(36).substr(2, 4).toUpperCase() + '-' + Math.random().toString(36).substr(2, 4).toUpperCase() + '-' + Math.random().toString(36).substr(2, 4).toUpperCase();
  const { data, error } = await supabase
    .from('license_keys')
    .insert({
      key_code: keyCode,
      is_active: true,
      is_revoked: false,
      created_by_admin: true,
      created_at: new Date().toISOString(),
      expires_at: expiresAt
    })
    .select();
  if (error) {
    return { statusCode: 500, headers, body: JSON.stringify({ error: error.message }) };
  }
  return { statusCode: 200, headers, body: JSON.stringify(data[0]) };
}

async function handleUpdateKey(event, headers, adminUsername) {
  const body = JSON.parse(event.body);
  if (!body.id) {
    return { statusCode: 400, headers, body: JSON.stringify({ error: 'Key ID required' }) };
  }
  const updateFields = { ...body };
  delete updateFields.id;
  const { data, error } = await supabase
    .from('license_keys')
    .update(updateFields)
    .eq('id', body.id)
    .select();
  if (error) {
    return { statusCode: 500, headers, body: JSON.stringify({ error: error.message }) };
  }
  return { statusCode: 200, headers, body: JSON.stringify(data[0]) };
}

async function handleDeleteKey(event, headers, adminUsername) {
  const id = event.queryStringParameters?.id;
  if (!id) {
    return { statusCode: 400, headers, body: JSON.stringify({ error: 'ID required' }) };
  }
  const { error } = await supabase
    .from('license_keys')
    .delete()
    .eq('id', id);
  if (error) {
    return { statusCode: 500, headers, body: JSON.stringify({ error: error.message }) };
  }
  return { statusCode: 200, headers, body: JSON.stringify({ message: 'Deleted' }) };
} 