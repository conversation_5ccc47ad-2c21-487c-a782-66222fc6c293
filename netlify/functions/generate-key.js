import { createClient } from '@supabase/supabase-js';
import jwt from 'jsonwebtoken';
import crypto from 'crypto';

// CORS helper
const allowCors = (handler) => {
  return async (request, context) => {
    const headers = {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    };

    if (request.method === 'OPTIONS') {
      return new Response('', { status: 200, headers });
    }

    try {
      const result = await handler(request, context, headers);
      // result should be a Response
      return result;
    } catch (error) {
      return new Response(JSON.stringify({ error: error.message }), {
        status: 500,
        headers,
      });
    }
  };
};

// Initialize Supabase
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

// Anti-bypass measures
const antiBypassChecks = {
  // Limit key generation to 1 key per 24 hours per IP
  checkRateLimit: async (ipAddress) => {
    const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
    const { data: recentKeys } = await supabase
      .from('license_keys')
      .select('key_code, expires_at')
      .eq('ip_address', ipAddress)
      .eq('is_revoked', false) // Only consider non-revoked keys
      .gte('created_at', twentyFourHoursAgo.toISOString())
      .order('created_at', { ascending: false })
      .limit(1);

    if (Array.isArray(recentKeys) && recentKeys.length > 0) {
      return { exists: true, key: recentKeys[0].key_code, expires_at: recentKeys[0].expires_at };
    }
    return { exists: false, key: null, expires_at: null };
  },
  
  // Enhanced fingerprint validation (also 1 key per 24 hours per fingerprint)
  checkFingerprintRateLimit: async (fingerprint) => {
    const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
    const { data: recentKeys } = await supabase
      .from('license_keys')
      .select('key_code, expires_at')
      .eq('fingerprint_hash', fingerprint)
      .eq('is_revoked', false) // Only consider non-revoked keys
      .gte('created_at', twentyFourHoursAgo.toISOString())
      .order('created_at', { ascending: false })
      .limit(1);

    if (Array.isArray(recentKeys) && recentKeys.length > 0) {
      return { exists: true, key: recentKeys[0].key_code, expires_at: recentKeys[0].expires_at };
    }
    return { exists: false, key: null, expires_at: null };
  },
  // Check for suspicious user agents
  checkUserAgent: (userAgent) => {
    const suspiciousPatterns = [
      'bypass', 'bot', 'crawler', 'scraper', 'automation', 'headless', 'phantom', 'selenium', 'puppeteer'
    ];
    const lowerUA = userAgent.toLowerCase();
    return !suspiciousPatterns.some(pattern => lowerUA.includes(pattern));
  },
  // Generate device fingerprint
  generateFingerprint: (userAgent, ipAddress) => {
    const fingerprint = `${userAgent}|${ipAddress}|${Date.now()}`;
    return crypto.createHash('sha256').update(fingerprint).digest('hex');
  },
  // Check for VPN/proxy
  checkProxy: (headers) => {
    const proxyHeaders = [
      'x-forwarded-for', 'x-real-ip', 'x-forwarded-proto', 'cf-connecting-ip', 'x-forwarded-host'
    ];
    return proxyHeaders.some(header => headers.get(header));
  }
};

// Linkvertise verification (mock for now - you'll need to implement real API calls)
const linkvertiseVerification = {
  verifyStep1: async (campaignId, verificationData) => {
    // TODO: Implement real Linkvertise API verification
    // For now, we'll simulate verification
    console.log('Verifying Linkvertise step 1 for campaign:', campaignId);
    return { success: true, verified: true };
  },
  verifyStep2: async (campaignId, verificationData) => {
    // TODO: Implement real Linkvertise API verification
    console.log('Verifying Linkvertise step 2 for campaign:', campaignId);
    return { success: true, verified: true };
  }
};

// --- ADVANCED ANTI-BYPASS LOGIC ---
const advancedAntiBypass = {
  checkAdvancedVM: (deviceData) => {
    const vmDetection = deviceData?.vmDetection;
    if (vmDetection && vmDetection.confidence > 0.75) {
      return { blocked: true, reason: `VM detected with ${Math.round(vmDetection.confidence * 100)}% confidence` };
    }
    return { blocked: false };
  },
  validateFingerprintIntegrity: (deviceData) => {
    if (deviceData?.integrity < 0.7) {
      return { blocked: true, reason: 'Device fingerprint integrity too low' };
    }
    if (deviceData?.confidence < 0.8) {
      return { blocked: true, reason: 'Device fingerprint confidence too low' };
    }
    return { blocked: false };
  },
  validateBehavior: (behaviorData) => {
    if (!behaviorData || behaviorData.anomalyScore > 0.7) {
      return { blocked: true, reason: 'Suspicious behavioral patterns detected' };
    }
    const botFlags = Object.values(behaviorData.suspiciousFlags || {}).filter(Boolean).length;
    if (botFlags >= 3) {
      return { blocked: true, reason: 'Multiple automation indicators detected' };
    }
    return { blocked: false };
  },
  generateSecureToken: (keyCode, deviceData, behaviorData) => {
    const tokenData = {
      keyId: keyCode,
      deviceFingerprint: deviceData.fingerprint,
      timestamp: Date.now(),
      behaviorHash: crypto.createHash('sha256').update(JSON.stringify(behaviorData)).digest('hex'),
      integrity: deviceData.integrity
    };
    const secret = process.env.KEY_ENCRYPTION_SECRET || 'your-secret-key-here';
    // Use AES-256-GCM with random IV
    const algorithm = 'aes-256-gcm';
    const iv = crypto.randomBytes(12); // 12 bytes for GCM
    const key = crypto.createHash('sha256').update(secret).digest(); // 32 bytes for aes-256
    const cipher = crypto.createCipheriv(algorithm, key, iv);
    let encrypted = cipher.update(JSON.stringify(tokenData), 'utf8', 'hex');
    encrypted += cipher.final('hex');
    const authTag = cipher.getAuthTag().toString('hex');
    // Return as JSON string
    return JSON.stringify({
      iv: iv.toString('hex'),
      authTag,
      data: encrypted
    });
  }
};

const generateKeyHandler = async (request, context, corsHeaders) => {
  // TEMP: Allow GET for testing endpoint availability
  if (request.method === 'GET') {
    return new Response(JSON.stringify({
      message: 'generate-key endpoint is live. Use POST for key generation.'
    }), {
      status: 200,
      headers: corsHeaders,
    });
  }
  if (request.method !== 'POST') {
    return new Response(JSON.stringify({ error: 'Method not allowed' }), {
      status: 405,
      headers: corsHeaders,
    });
  }
  try {
    const body = await request.json();
    let { campaignId, step1Data, step2Data, fingerprint, sessionId, deviceData, behaviorData, mlAnalysis, securityStatus, ipAddress } = body;
    const headers = request.headers;
    let ipAddr = headers.get('x-forwarded-for') || headers.get('x-real-ip') || 'unknown';
    if (ipAddr && ipAddr.includes(',')) {
      ipAddr = ipAddr.split(',')[0].trim();
    }
    if (ipAddr === 'unknown' && ipAddress) {
      ipAddr = ipAddress;
    }
    const userAgent = headers.get('user-agent') || 'unknown';

    // Check if IP is temporarily banned
    const { data: bannedIPs, error: banError } = await supabase
      .from('ip_bans')
      .select('*')
      .eq('ip_address', ipAddr)
      .eq('is_active', true)
      .gt('expires_at', new Date().toISOString());

    if (banError) {
      console.error('Error checking IP ban status:', banError);
    }

    if (bannedIPs && bannedIPs.length > 0) {
      const activeBan = bannedIPs[0];
      const timeRemaining = Math.ceil((new Date(activeBan.expires_at) - new Date()) / 60000); // minutes
      await logUsage(null, ipAddr, userAgent, 'banned_ip_attempt', false, `Attempt from temporarily banned IP (${timeRemaining} minutes remaining)`);

      return new Response(JSON.stringify({
        error: `Access temporarily restricted due to security violation: ${activeBan.violation_type}. Please try again in ${timeRemaining} minute${timeRemaining !== 1 ? 's' : ''}.`,
        banInfo: {
          violation_type: activeBan.violation_type,
          expires_in_minutes: timeRemaining,
          violation_count: activeBan.violation_count
        }
      }), {
        status: 403,
        headers: corsHeaders,
      });
    }

    // LOGGING: Log all incoming data for debugging
    console.log('[generate-key] Incoming body:', body);
    console.log('[generate-key] Using ipAddr:', ipAddr);
    console.log('[generate-key] sessionId:', sessionId);
    console.log('[generate-key] campaignId:', campaignId);
    console.log('[generate-key] deviceData:', deviceData);
    console.log('[generate-key] behaviorData:', behaviorData);
    console.log('[generate-key] mlAnalysis:', mlAnalysis);
    console.log('[generate-key] securityStatus:', securityStatus);

    // Enforce session step completion
    if (!sessionId) {
      return new Response(JSON.stringify({ error: 'Session missing' }), {
        status: 400,
        headers: corsHeaders,
      });
    }
    const { data: session, error: sessionError } = await supabase
      .from('key_sessions')
      .select('*')
      .eq('session_id', sessionId)
      .single();
    if (sessionError || !session) {
      return new Response(JSON.stringify({ error: 'Session not found' }), {
        status: 400,
        headers: corsHeaders,
      });
    }
    if (!session.step1 || !session.step2) {
      return new Response(JSON.stringify({ error: 'All steps not completed' }), {
        status: 403,
        headers: corsHeaders,
      });
    }
    // Enhanced anti-bypass checks
    
    // 1. Rate limiting per IP (1 key per 24 hours)
    const rateLimitCheckResult = await antiBypassChecks.checkRateLimit(ipAddr);
    if (rateLimitCheckResult.exists) {
      await logUsage(null, ipAddr, userAgent, 'rate_limit_existing_key_provided', true, 'Key already generated within 24 hours');
      return new Response(JSON.stringify({
        success: true,
        key_exists: true,
        key: rateLimitCheckResult.key,
        expires_at: rateLimitCheckResult.expires_at,
        message: 'You have already generated a key within the last 24 hours. Here is your existing key.'
      }), {
        status: 200,
        headers: corsHeaders,
      });
    }
    
    // 2. VM Detection from frontend
    if (deviceData && deviceData.isVM) {
      await logUsage(null, ipAddr, userAgent, 'vm_detected', false, 'Virtual machine detected');
      return new Response(JSON.stringify({ error: 'Virtual machines are not supported for key generation.' }), {
        status: 403,
        headers: corsHeaders,
      });
    }
    
    // 3. Suspicious environment check
    if (deviceData && deviceData.vmScore > 2) {
      await logUsage(null, ipAddr, userAgent, 'suspicious_environment', false, `VM score: ${deviceData.vmScore}`);
      return new Response(JSON.stringify({ error: 'Suspicious environment detected. Please use a standard device.' }), {
        status: 403,
        headers: corsHeaders,
      });
    }
    
    // 4. Behavioral analysis validation
    if (behaviorData) {
      // Check for bot-like behavior
      if (behaviorData.anomalyScore > 0.7) {
        await logUsage(null, ipAddr, userAgent, 'behavioral_anomaly', false, `Anomaly score: ${behaviorData.anomalyScore}`);
        return new Response(JSON.stringify({ error: 'Automated behavior detected. Please complete the process manually.' }), {
          status: 403,
          headers: corsHeaders,
        });
      }
      
      // Check for suspicious flags
      const suspiciousFlags = behaviorData.suspiciousFlags || {};
      if (suspiciousFlags.straightLineMovement && suspiciousFlags.regularClicks) {
        await logUsage(null, ipAddr, userAgent, 'bot_behavior', false, 'Multiple bot indicators detected');
        return new Response(JSON.stringify({ error: 'Automated script detected. Please use manual interaction.' }), {
          status: 403,
          headers: corsHeaders,
        });
      }
    }
    const userAgentOk = antiBypassChecks.checkUserAgent(userAgent);
    if (!userAgentOk) {
      await logUsage(null, ipAddr, userAgent, 'suspicious_user_agent', false, 'Suspicious user agent detected');
      return new Response(JSON.stringify({ error: 'Invalid request' }), {
        status: 400,
        headers: corsHeaders,
      });
    }
    const isProxy = antiBypassChecks.checkProxy(headers);
    if (isProxy) {
      console.log('Proxy detected for IP:', ipAddr);
      // Don't block, but log for monitoring
    }
    // Enhanced fingerprint rate limiting (1 key per 24 hours)
    const fingerprintRateLimitCheckResult = await antiBypassChecks.checkFingerprintRateLimit(fingerprint);
    if (fingerprintRateLimitCheckResult.exists) {
      await logUsage(null, ipAddr, userAgent, 'fingerprint_limit_existing_key_provided', true, 'Key already generated within 24 hours for this device');
      return new Response(JSON.stringify({
        success: true,
        key_exists: true,
        key: fingerprintRateLimitCheckResult.key,
        expires_at: fingerprintRateLimitCheckResult.expires_at,
        message: 'You have already generated a key with this device within the last 24 hours. Here is your existing key.'
      }), {
        status: 200,
        headers: corsHeaders,
      });
    }
    
    // Additional anti-bypass: Check for rapid sequential requests
    const lastMinute = new Date(Date.now() - 60 * 1000);
    const { data: veryRecentKeys } = await supabase
      .from('license_keys')
      .select('created_at')
      .eq('ip_address', ipAddr)
      .gte('created_at', lastMinute.toISOString());
    
    if (veryRecentKeys && veryRecentKeys.length > 0) {
      await logUsage(null, ipAddr, userAgent, 'rapid_requests', false, 'Rapid sequential requests detected');
      return new Response(JSON.stringify({ error: 'Please wait at least 1 minute between key generation attempts.' }), {
        status: 429,
        headers: corsHeaders,
      });
    }
    // Generate unique key code
    const { data: keyCode, error: keyError } = await supabase.rpc('generate_key_code');
    if (keyError) {
      console.error('Error generating key code:', keyError);
      return new Response(JSON.stringify({ error: 'Failed to generate key' }), {
        status: 500,
        headers: corsHeaders,
      });
    }
    // Ensure campaignId is a valid UUID, otherwise fetch default campaign
    if (!campaignId || typeof campaignId !== 'string' || !/^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12})$/.test(campaignId)) {
      const { data: defaultCampaign, error: campaignError } = await supabase
        .from('linkvertise_campaigns')
        .select('id')
        .eq('name', 'Default Campaign')
        .single();
      if (campaignError || !defaultCampaign) {
        console.error('Error fetching default campaign:', campaignError);
        return new Response(JSON.stringify({ error: 'Failed to get campaign' }), {
          status: 500,
          headers: corsHeaders,
        });
      }
      campaignId = defaultCampaign.id;
    }
    // Create license key with enhanced security data
    const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours from now
    const fingerprintHash = fingerprint || antiBypassChecks.generateFingerprint(userAgent, ipAddr);
    
    // Advanced security checks (replace/add after behavioral analysis validation)
    // Advanced VM check
    const vmCheck = advancedAntiBypass.checkAdvancedVM(deviceData);
    if (vmCheck.blocked) {
      await logUsage(null, ipAddr, userAgent, 'advanced_vm_detected', false, vmCheck.reason);
      return new Response(JSON.stringify({ error: vmCheck.reason }), {
        status: 403,
        headers: corsHeaders,
      });
    }
    // Fingerprint integrity check
    const integrityCheck = advancedAntiBypass.validateFingerprintIntegrity(deviceData);
    if (integrityCheck.blocked) {
      await logUsage(null, ipAddr, userAgent, 'fingerprint_integrity_failed', false, integrityCheck.reason);
      return new Response(JSON.stringify({ error: integrityCheck.reason }), {
        status: 403,
        headers: corsHeaders,
      });
    }
    // Enhanced behavioral analysis
    const behaviorCheck = advancedAntiBypass.validateBehavior(behaviorData);
    if (behaviorCheck.blocked) {
      await logUsage(null, ipAddr, userAgent, 'behavioral_blocked', false, behaviorCheck.reason);
      return new Response(JSON.stringify({ error: behaviorCheck.reason }), {
        status: 403,
        headers: corsHeaders,
      });
    }
    // Add ML analysis check after behavioral analysis validation
    if (mlAnalysis && mlAnalysis.isBot) {
      await logUsage(null, ipAddr, userAgent, 'ml_bot_detected', false, `ML detected bot with ${Math.round(mlAnalysis.confidence * 100)}% confidence`);
      // Add security status check after ML analysis check
      if (securityStatus && (securityStatus.debuggingDetected || securityStatus.tamperingDetected)) {
        await logUsage(null, ipAddr, userAgent, 'security_violation', false, `Security violation: debugging=${securityStatus.debuggingDetected}, tampering=${securityStatus.tamperingDetected}`);
        return new Response(JSON.stringify({ error: 'Security violation detected. Please use a standard browser.' }), {
          status: 403,
          headers: corsHeaders,
        });
      }
      return new Response(JSON.stringify({ error: `Automated behavior detected with ${Math.round(mlAnalysis.confidence * 100)}% confidence` }), {
        status: 403,
        headers: corsHeaders,
      });
    }
    // Generate secure validation token
    const validationToken = advancedAntiBypass.generateSecureToken(keyCode, deviceData, behaviorData);
    // Store enhanced security data
    const securityData = {
      fingerprint_confidence: deviceData.confidence,
      integrity_score: deviceData.integrity,
      vm_confidence: deviceData.vmDetection?.confidence,
      behavior_anomaly_score: behaviorData.anomalyScore,
      ml_analysis: mlAnalysis || {},
      security_status: securityStatus || {},
      validation_token: validationToken
    };
    
    const { data: key, error: insertError } = await supabase
      .from('license_keys')
      .insert({
        key_code: keyCode,
        campaign_id: campaignId,
        ip_address: ipAddr,
        user_agent: userAgent,
        fingerprint_hash: fingerprintHash,
        expires_at: expiresAt.toISOString(),
        bypass_attempts: 0,
        security_data: securityData,
        session_id: sessionId
      })
      .select()
      .single();
    if (insertError) {
      console.error('[generate-key] Insert error:', insertError);
      return new Response(JSON.stringify({ error: 'Insert failed', details: insertError }), {
        status: 500,
        headers: corsHeaders,
      });
    }
    // Log successful key generation
    await logUsage(key.id, ipAddr, userAgent, 'key_generated', true);
    return new Response(JSON.stringify({
      success: true,
      key: key.key_code,
      expires_at: key.expires_at,
      message: 'Key generated successfully! Valid for 24 hours.'
    }), {
      status: 200,
      headers: corsHeaders,
    });
  } catch (err) {
    console.error('[generate-key] General error:', err);
    return new Response(JSON.stringify({ error: err.message, details: err }), {
      status: 500,
      headers: corsHeaders,
    });
  }
};

// Helper function to log usage
const logUsage = async (keyId, ipAddress, userAgent, action, success, errorMessage = null) => {
  try {
    await supabase
      .from('key_usage_logs')
      .insert({
        key_id: keyId,
        ip_address: ipAddress,
        user_agent: userAgent,
        action,
        success,
        error_message: errorMessage
      });
  } catch (error) {
    console.error('Error logging usage:', error);
  }
};

export default allowCors(generateKeyHandler); 