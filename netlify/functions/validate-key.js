import { createClient } from '@supabase/supabase-js';

// CORS helper
const allowCors = (handler) => {
  return async (request, context) => {
    const headers = {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    };

    if (request.method === 'OPTIONS') {
      return new Response('', { status: 200, headers });
    }

    try {
      const result = await handler(request, context, headers);
      return result;
    } catch (error) {
      return new Response(JSON.stringify({ error: error.message }), {
        status: 500,
        headers,
      });
    }
  };
};

// Initialize Supabase
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

const validate<PERSON><PERSON><PERSON>andler = async (request, context, corsHeaders) => {
  // New: GET handler for IP check
  if (request.method === 'GET') {
    const url = new URL(request.url);
    const action = url.searchParams.get('action');
    const ip = url.searchParams.get('ip');
    if (action === 'check-ip' && ip) {
      // Find the most recent key for this IP in the last 24 hours
      const since = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString();
      const { data, error } = await supabase
        .from('license_keys')
        .select('key_code, created_at')
        .eq('ip_address', ip)
        .gte('created_at', since)
        .order('created_at', { ascending: false })
        .limit(1);
      if (error) {
        return new Response(JSON.stringify({ key: null, error: error.message }), {
          status: 200,
          headers: corsHeaders,
        });
      }
      if (data && data.length > 0) {
        return new Response(JSON.stringify({ key: data[0].key_code }), {
          status: 200,
          headers: corsHeaders,
        });
      }
      return new Response(JSON.stringify({ key: null }), {
        status: 200,
        headers: corsHeaders,
      });
    }
  }
  if (request.method !== 'POST') {
    return new Response(JSON.stringify({ error: 'Method not allowed' }), {
      status: 405,
      headers: corsHeaders,
    });
  }
  try {
    const body = await request.json();
    const { keyCode, hwidHash, robloxUsername, fingerprint, heartbeat = false } = body;
    const headers = request.headers;
    let ipAddress = headers.get('x-forwarded-for') || headers.get('x-real-ip') || 'unknown';
    if (ipAddress && ipAddress.includes(',')) {
      ipAddress = ipAddress.split(',')[0].trim();
    }
    const userAgent = headers.get('user-agent') || 'unknown';
    
    // Check if IP is banned
    const { data: ipBan, error: banError } = await supabase
      .from('ip_bans')
      .select('*')
      .eq('ip_address', ipAddress)
      .eq('is_active', true)
      .single();
      
    if (!banError && ipBan) {
      const isExpired = ipBan.expires_at && new Date(ipBan.expires_at) < new Date();
      if (!isExpired) {
        await logSecurityEvent('ip_ban_access_attempt', 'high', ipAddress, userAgent, null, {
          ban_reason: ipBan.reason,
          ban_expires: ipBan.expires_at
        });
        return new Response(JSON.stringify({ 
          error: 'Access denied. Your IP address has been banned.',
          code: 'IP_BANNED'
        }), {
          status: 403,
          headers: corsHeaders,
        });
      } else {
        // Ban has expired, deactivate it
        await supabase
          .from('ip_bans')
          .update({ is_active: false })
          .eq('id', ipBan.id);
      }
    }
    
    // Validate input
    if (!keyCode || !hwidHash) {
      await logUsage(null, ipAddress, userAgent, 'validation_failed', false, 'Missing key code or HWID');
      return new Response(JSON.stringify({ error: 'Key code and HWID are required' }), {
        status: 400,
        headers: corsHeaders,
      });
    }
    // Check if key exists and is valid
    const { data: key, error: keyError } = await supabase
      .from('license_keys')
      .select(`*, hwid_bindings(*)`)
      .eq('key_code', keyCode)
      .single();
      
    // Heartbeat validation for existing keys
    if (!keyError && key && heartbeat) {
      const heartbeatInterval = 5 * 60 * 1000; // 5 minutes
      const now = new Date();
      
      if (key.last_heartbeat) {
        const timeSinceLastHeartbeat = now - new Date(key.last_heartbeat);
        if (timeSinceLastHeartbeat > heartbeatInterval * 2) {
          // Key potentially compromised - require re-authentication
          await logUsage(key.id, ipAddress, userAgent, 'heartbeat_timeout', false, 'Heartbeat timeout detected');
          return new Response(JSON.stringify({ 
            error: 'Session timeout. Please restart your script.',
            code: 'HEARTBEAT_TIMEOUT'
          }), {
            status: 403,
            headers: corsHeaders,
          });
        }
      }
      
      // Update heartbeat
      await supabase
        .from('license_keys')
        .update({ 
          last_heartbeat: now.toISOString(),
          last_used_at: now.toISOString()
        })
        .eq('id', key.id);
        
      return new Response(JSON.stringify({
        success: true,
        valid: true,
        heartbeat: true,
        message: 'Heartbeat updated',
        expires_at: key.expires_at
      }), {
        status: 200,
        headers: corsHeaders,
      });
    }
    if (keyError || !key) {
      await logUsage(null, ipAddress, userAgent, 'validation_failed', false, 'Invalid key code');
      return new Response(JSON.stringify({ error: 'Invalid key code' }), {
        status: 404,
        headers: corsHeaders,
      });
    }
    
    // Anti-bypass: Check for multiple concurrent sessions
    const recentValidations = await supabase
      .from('key_usage_logs')
      .select('ip_address, created_at')
      .eq('key_id', key.id)
      .eq('action', 'validation_success')
      .gte('created_at', new Date(Date.now() - 5 * 60 * 1000).toISOString()) // Last 5 minutes
      .order('created_at', { ascending: false });
      
    if (recentValidations.data && recentValidations.data.length > 0) {
      const uniqueIPs = new Set(recentValidations.data.map(log => log.ip_address));
      if (uniqueIPs.size > 1) {
        // Multiple IPs using same key recently
        await logUsage(key.id, ipAddress, userAgent, 'concurrent_sessions', false, 'Multiple concurrent sessions detected');
        await logSecurityEvent('concurrent_sessions_detected', 'critical', ipAddress, userAgent, key.id, {
          unique_ips: Array.from(uniqueIPs),
          key_code: key.key_code
        });
        await supabase
          .from('license_keys')
          .update({ 
            bypass_attempts: (key.bypass_attempts || 0) + 1,
            is_revoked: true
          })
          .eq('id', key.id);
          
        return new Response(JSON.stringify({ 
          error: 'Key revoked due to suspicious activity. Multiple concurrent sessions detected.',
          code: 'KEY_REVOKED'
        }), {
          status: 403,
          headers: corsHeaders,
        });
      }
    }
    
    // Anti-bypass: Rate limiting per key
    const recentUsage = await supabase
      .from('key_usage_logs')
      .select('created_at')
      .eq('key_id', key.id)
      .eq('action', 'validation_success')
      .gte('created_at', new Date(Date.now() - 60 * 1000).toISOString()); // Last minute
      
    if (recentUsage.data && recentUsage.data.length > 10) {
      await logUsage(key.id, ipAddress, userAgent, 'key_rate_limit', false, 'Key validation rate limit exceeded');
      await logSecurityEvent('rate_limit_exceeded', 'high', ipAddress, userAgent, key.id, {
        request_count: recentUsage.data.length,
        key_code: key.key_code
      });
      return new Response(JSON.stringify({ 
        error: 'Too many validation requests. Please slow down.',
        code: 'RATE_LIMITED'
      }), {
        status: 429,
        headers: corsHeaders,
      });
    }
    // Enforce fingerprint validation
    if (key.fingerprint_hash && fingerprint && key.fingerprint_hash !== fingerprint) {
      await logUsage(key.id, ipAddress, userAgent, 'validation_failed', false, 'Fingerprint mismatch');
      await logSecurityEvent('fingerprint_mismatch', 'high', ipAddress, userAgent, key.id, {
        expected_fingerprint: key.fingerprint_hash,
        provided_fingerprint: fingerprint,
        key_code: key.key_code
      });
      return new Response(JSON.stringify({ error: 'Device fingerprint does not match. Key cannot be used on this device.' }), {
        status: 403,
        headers: corsHeaders,
      });
    }
    // Check if key is active and not revoked
    if (!key.is_active || key.is_revoked) {
      await logUsage(key.id, ipAddress, userAgent, 'validation_failed', false, 'Key is inactive or revoked');
      return new Response(JSON.stringify({ error: 'Key is inactive or revoked' }), {
        status: 403,
        headers: corsHeaders,
      });
    }
    // Check if key has expired
    if (new Date(key.expires_at) < new Date()) {
      await logUsage(key.id, ipAddress, userAgent, 'validation_failed', false, 'Key has expired');
      return new Response(JSON.stringify({ error: 'Key has expired' }), {
        status: 403,
        headers: corsHeaders,
      });
    }
    // Check existing HWID bindings
    const existingBinding = key.hwid_bindings && key.hwid_bindings.length > 0 ? key.hwid_bindings[0] : null;
    if (existingBinding) {
      // Key is already bound to an HWID
      if (existingBinding.hwid_hash === hwidHash) {
        // Same HWID - update last used and return success
        await supabase
          .from('hwid_bindings')
          .update({ last_used_at: new Date().toISOString() })
          .eq('id', existingBinding.id);
        await supabase
          .from('license_keys')
          .update({ 
            last_used_at: new Date().toISOString(),
            usage_count: key.usage_count + 1
          })
          .eq('id', key.id);
        await logUsage(key.id, ipAddress, userAgent, 'validation_success', true);
        return new Response(JSON.stringify({
          success: true,
          valid: true,
          message: 'Key is valid',
          expires_at: key.expires_at,
          usage_count: key.usage_count + 1
        }), {
          status: 200,
          headers: corsHeaders,
        });
      } else {
        // Different HWID - key is already in use by another device
        await logUsage(key.id, ipAddress, userAgent, 'validation_failed', false, 'Key already bound to different HWID');
        return new Response(JSON.stringify({ 
          error: 'Key is already in use by another device',
          code: 'KEY_ALREADY_BOUND'
        }), {
          status: 403,
          headers: corsHeaders,
        });
      }
    } else {
      // No existing binding - create new HWID binding
      const { data: binding, error: bindingError } = await supabase
        .from('hwid_bindings')
        .insert({
          key_id: key.id,
          hwid_hash: hwidHash,
          roblox_username: robloxUsername || null
        })
        .select()
        .single();
      if (bindingError) {
        console.error('Error creating HWID binding:', bindingError);
        await logUsage(key.id, ipAddress, userAgent, 'validation_failed', false, 'Failed to bind HWID');
        return new Response(JSON.stringify({ error: 'Failed to bind device' }), {
          status: 500,
          headers: corsHeaders,
        });
      }
      // Update key usage
      await supabase
        .from('license_keys')
        .update({ 
          last_used_at: new Date().toISOString(),
          usage_count: key.usage_count + 1
        })
        .eq('id', key.id);
      await logUsage(key.id, ipAddress, userAgent, 'validation_success', true);
      return new Response(JSON.stringify({
        success: true,
        valid: true,
        message: 'Key is valid and device has been bound',
        expires_at: key.expires_at,
        usage_count: key.usage_count + 1,
        newly_bound: true
      }), {
        status: 200,
        headers: corsHeaders,
      });
    }
  } catch (error) {
    console.error('Key validation error:', error);
    return new Response(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: corsHeaders,
    });
  }
};

// Helper function to log usage
const logUsage = async (keyId, ipAddress, userAgent, action, success, errorMessage = null) => {
  try {
    await supabase
      .from('key_usage_logs')
      .insert({
        key_id: keyId,
        ip_address: ipAddress,
        user_agent: userAgent,
        action,
        success,
        error_message: errorMessage
      });
  } catch (error) {
    console.error('Error logging usage:', error);
  }
};

// Helper function to log security events
const logSecurityEvent = async (eventType, severity, ipAddress, userAgent, keyId, details = {}) => {
  try {
    await supabase
      .from('security_events')
      .insert({
        event_type: eventType,
        severity: severity,
        ip_address: ipAddress,
        user_agent: userAgent,
        key_id: keyId,
        details: details,
        created_at: new Date().toISOString()
      });
  } catch (error) {
    console.error('Error logging security event:', error);
  }
};

export default allowCors(validateKeyHandler); 