import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;
const supabase = createClient(supabaseUrl, supabaseAnonKey);

const allowCors = (fn) => async (request, context) => {
    const origin = request.headers.get('origin') || '*';
    const headers = {
        'Access-Control-Allow-Origin': origin,
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type',
        'Access-Control-Allow-Credentials': 'true',
    };
    if (request.method === 'OPTIONS') {
        return new Response('', { status: 204, headers });
    }
    const result = await fn(request, context, headers);
    return result;
};

const handler = async (request, context, corsHeaders) => {
    if (request.method !== 'POST') {
        return new Response(JSON.stringify({ error: 'Method Not Allowed' }), {
            status: 405,
            headers: corsHeaders
        });
    }
    try {
        const { script_name, user_name, description } = await request.json();
        if (!script_name || !user_name || !description) {
            return new Response(JSON.stringify({ error: 'Script name, user name, and description are required.' }), {
                status: 400,
                headers: corsHeaders
            });
        }
        const { data, error } = await supabase
            .from('script_requests')
            .insert([
                {
                    script_name: script_name,
                    user_name: user_name,
                    description: description,
                    status: 'pending',
                    requested_at: new Date().toISOString(),
                },
            ])
            .select();
        if (error) {
            return new Response(JSON.stringify({ error: error.message }), {
                status: 500,
                headers: corsHeaders
            });
        }
        const insertedRequest = data && data.length > 0 ? data[0] : null;
        return new Response(JSON.stringify({ message: 'Script request submitted successfully!', request: insertedRequest }), {
            status: 201,
            headers: corsHeaders
        });
    } catch (error) {
        return new Response(JSON.stringify({ error: 'Internal Server Error: ' + error.message }), {
            status: 500,
            headers: corsHeaders
        });
    }
};

export default allowCors(handler); 