import { createClient } from '@supabase/supabase-js';
import jwt from 'jsonwebtoken';

// CORS helper
const allowCors = (handler) => {
  return async (request, context) => {
    const headers = {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    };
    if (request.method === 'OPTIONS') {
      return new Response('', { status: 200, headers });
    }
    try {
      const result = await handler(request, context, headers);
      return result;
    } catch (error) {
      return new Response(JSON.stringify({ error: error.message }), {
        status: 500,
        headers,
      });
    }
  };
};

// Initialize Supabase
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

// Simple admin/owner authentication using environment variables
const ADMIN_USER = process.env.ADMIN_USER;
const ADMIN_PASSWORD = process.env.ADMIN_PASSWORD;
const OWNER_USER = process.env.OWNER_USER;
const OWNER_PASSWORD = process.env.OWNER_PASSWORD;

const verifySimpleAdmin = (request) => {
  const username = request.headers.get('x-admin-username');
  const password = request.headers.get('x-admin-password');
  if (username === OWNER_USER && password === OWNER_PASSWORD) {
    return { admin: { username, role: 'owner' } };
  }
  if (username === ADMIN_USER && password === ADMIN_PASSWORD) {
    return { admin: { username, role: 'admin' } };
  }
  if (username === process.env.ML_SECURITY_USER && password === process.env.ML_SECURITY_PASSWORD) {
    return { admin: { username, role: 'ml_security' } };
  }
  return { error: 'Unauthorized' };
};

const adminKeysHandler = async (request, context, corsHeaders) => {
  // Parse query params and body
  const url = new URL(request.url);
  const queryStringParameters = Object.fromEntries(url.searchParams.entries());
  let body = null;
  if (request.method !== 'GET' && request.method !== 'OPTIONS') {
    body = await request.json();
  }
  // Verify admin authentication
  const authResult = verifySimpleAdmin(request);
  if (authResult.error) {
    return new Response(JSON.stringify({ error: authResult.error }), {
      status: 401,
      headers: corsHeaders,
    });
  }
  try {
    switch (request.method) {
      case 'GET':
        if (queryStringParameters?.stats === 'true') {
          return await handleGetKeyStats(corsHeaders);
        }
        if (queryStringParameters?.activity === 'true') {
          return await handleGetRecentActivity(corsHeaders);
        }
        if (queryStringParameters?.health === 'true') {
          return await handleGetSystemHealth(corsHeaders);
        }
        if (queryStringParameters?.action === 'security-events') {
          return await handleSecurityEvents(corsHeaders);
        }
        if (queryStringParameters?.action === 'behavior-profiles') {
          return await handleBehaviorProfiles(corsHeaders);
        }
        return await handleGetKeys(queryStringParameters, corsHeaders);
      case 'POST':
        return await handleCreateKey(body, corsHeaders, authResult.admin);
      case 'PUT':
        return await handleUpdateKey(body, corsHeaders, authResult.admin);
      case 'DELETE':
        return await handleDeleteKey(queryStringParameters?.id, corsHeaders, authResult.admin);
      default:
        return new Response(JSON.stringify({ error: 'Method not allowed' }), {
          status: 405,
          headers: corsHeaders,
        });
    }
  } catch (error) {
    console.error('Admin keys error:', error);
    return new Response(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: corsHeaders,
    });
  }
};

// Get all keys with filters and pagination
const handleGetKeys = async (queryParams, corsHeaders) => {
  const page = parseInt(queryParams?.page) || 1;
  const limit = parseInt(queryParams?.limit) || 50;
  const offset = (page - 1) * limit;
  const status = queryParams?.status; // 'active', 'expired', 'revoked', 'all'
  const search = queryParams?.search;
  let query = supabase
    .from('license_keys')
    .select(`*, linkvertise_campaigns(name), hwid_bindings(hwid_hash, roblox_username, bound_at)`)
    .order('created_at', { ascending: false });
  // Apply filters
  if (status && status !== 'all') {
    switch (status) {
      case 'active':
        query = query.eq('is_active', true).gt('expires_at', new Date().toISOString());
        break;
      case 'expired':
        query = query.lt('expires_at', new Date().toISOString());
        break;
      case 'revoked':
        query = query.eq('is_revoked', true);
        break;
    }
  }
  if (search) {
    query = query.or(`key_code.ilike.%${search}%,ip_address.ilike.%${search}%`);
  }
  // Apply pagination
  query = query.range(offset, offset + limit - 1);
  const { data: keys, error } = await query;
  if (error) {
    console.error('Error fetching keys:', error);
    return new Response(JSON.stringify({ error: 'Failed to fetch keys' }), {
      status: 500,
      headers: corsHeaders,
    });
  }
  // Get total count for pagination
  const { count: totalCount } = await supabase
    .from('license_keys')
    .select('*', { count: 'exact', head: true });
  return new Response(JSON.stringify({
    keys,
    pagination: {
      page,
      limit,
      total: totalCount,
      pages: Math.ceil(totalCount / limit)
    }
  }), {
    status: 200,
    headers: corsHeaders,
  });
};

// Log admin/key actions
const logKeyAction = async (keyId, action, adminUsername, details = null) => {
  if (!keyId || !action || !adminUsername) return;
  await supabase.from('key_usage_logs').insert({
    key_id: keyId,
    action,
    admin_username: adminUsername,
    details,
    created_at: new Date().toISOString(),
  });
};

// Create admin-generated key
const handleCreateKey = async (body, corsHeaders, admin) => {
  const { expiresInHours = 24, note } = body;
  // Generate unique key code
  const { data: keyCode, error: keyError } = await supabase.rpc('generate_key_code');
  if (keyError) {
    console.error('Error generating key code:', keyError);
    return new Response(JSON.stringify({ error: 'Failed to generate key' }), {
      status: 500,
      headers: corsHeaders,
    });
  }
  // Create license key
  const expiresAt = new Date(Date.now() + expiresInHours * 60 * 60 * 1000);
  const { data: key, error: insertError } = await supabase
    .from('license_keys')
    .insert({
      key_code: keyCode,
      expires_at: expiresAt.toISOString(),
      created_by_admin: true,
      is_active: true
    })
    .select()
    .single();
  if (insertError) {
    console.error('Error inserting key:', insertError);
    return new Response(JSON.stringify({ error: 'Failed to create key' }), {
      status: 500,
      headers: corsHeaders,
    });
  }
  // Log action
  await logKeyAction(key.id, 'created', admin.username);
  return new Response(JSON.stringify({
    success: true,
    key: key.key_code,
    expires_at: key.expires_at,
    message: 'Key created successfully!'
  }), {
    status: 201,
    headers: corsHeaders,
  });
};

// Update key (revoke, activate, etc.)
const handleUpdateKey = async (body, corsHeaders, admin) => {
  const { id, is_active, is_revoked, note } = body;
  if (!id) {
    return new Response(JSON.stringify({ error: 'Key ID is required' }), {
      status: 400,
      headers: corsHeaders,
    });
  }
  const updateFields = {};
  if (is_active !== undefined) updateFields.is_active = is_active;
  if (is_revoked !== undefined) updateFields.is_revoked = is_revoked;
  if (note !== undefined) updateFields.note = note;
  const { data, error } = await supabase
    .from('license_keys')
    .update(updateFields)
    .eq('id', id)
    .select();
  if (error) {
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: corsHeaders,
    });
  }
  // Log action
  let action = 'updated';
  if (is_revoked) action = 'revoked';
  await logKeyAction(id, action, admin.username);
  return new Response(JSON.stringify({
    success: true,
    key: data[0],
    message: 'Key updated successfully!'
  }), {
    status: 200,
    headers: corsHeaders,
  });
};

// Delete key
const handleDeleteKey = async (keyId, corsHeaders, admin) => {
  if (!keyId) {
    return new Response(JSON.stringify({ error: 'Key ID is required' }), {
      status: 400,
      headers: corsHeaders,
    });
  }
  const { error } = await supabase
    .from('license_keys')
    .delete()
    .eq('id', keyId);
  if (error) {
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: corsHeaders,
    });
  }
  // Log action
  await logKeyAction(keyId, 'deleted', admin.username);
  return new Response(JSON.stringify({
    success: true,
    message: 'Key deleted successfully!'
  }), {
    status: 200,
    headers: corsHeaders,
  });
};

// Stats endpoint for dashboard
const handleGetKeyStats = async (corsHeaders) => {
  const now = new Date();
  const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const todayISO = todayStart.toISOString();
  // Total keys
  const { count: totalKeys } = await supabase
    .from('license_keys')
    .select('*', { count: 'exact', head: true });
  // Active keys
  const { count: activeKeys } = await supabase
    .from('license_keys')
    .select('*', { count: 'exact', head: true })
    .eq('is_active', true)
    .gt('expires_at', now.toISOString());
  // Expired keys
  const { count: expiredKeys } = await supabase
    .from('license_keys')
    .select('*', { count: 'exact', head: true })
    .lt('expires_at', now.toISOString());
  // Revoked keys
  const { count: revokedKeys } = await supabase
    .from('license_keys')
    .select('*', { count: 'exact', head: true })
    .eq('is_revoked', true);
  // Keys generated today
  const { count: todaysKeys } = await supabase
    .from('license_keys')
    .select('*', { count: 'exact', head: true })
    .gte('created_at', todayISO);
  return new Response(JSON.stringify({
    totalKeys,
    activeKeys,
    expiredKeys,
    revokedKeys,
    todaysKeys
  }), {
    status: 200,
    headers: corsHeaders,
  });
};

// Recent activity endpoint
const handleGetRecentActivity = async (corsHeaders) => {
  // Get the latest 20 log entries, joined with key info and admin username
  const { data, error } = await supabase
    .from('key_usage_logs')
    .select('id, action, admin_username, details, created_at, key_id, license_keys(key_code)')
    .order('created_at', { ascending: false })
    .limit(20);
  if (error) {
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: corsHeaders,
    });
  }
  return new Response(JSON.stringify({ activity: data }), {
    status: 200,
    headers: corsHeaders,
  });
};

// System Health endpoint
const serverStartTime = Date.now();
const handleGetSystemHealth = async (corsHeaders) => {
  // Uptime in hours:minutes
  const uptimeMs = Date.now() - serverStartTime;
  const hours = Math.floor(uptimeMs / (1000 * 60 * 60));
  const minutes = Math.floor((uptimeMs % (1000 * 60 * 60)) / (1000 * 60));
  const uptime = `${hours}h ${minutes}m`;

  // Total admins
  const { count: totalAdmins } = await supabase
    .from('admin_users')
    .select('*', { count: 'exact', head: true });

  // Expired keys
  const now = new Date();
  const { count: expiredKeys } = await supabase
    .from('license_keys')
    .select('*', { count: 'exact', head: true })
    .lt('expires_at', now.toISOString());

  // Keys generated today
  const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const todayISO = todayStart.toISOString();
  const { count: todaysKeys } = await supabase
    .from('license_keys')
    .select('*', { count: 'exact', head: true })
    .gte('created_at', todayISO);

  return new Response(JSON.stringify({
    uptime,
    totalAdmins,
    expiredKeys,
    todaysKeys
  }), {
    status: 200,
    headers: corsHeaders,
  });
};

const handleSecurityEvents = async (corsHeaders) => {
  try {
    // Get security events and behavior data for ML Security dashboard
    const { data: securityEvents, error: eventsError } = await supabase
      .from('security_events')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(1000);

    const { data: behaviorProfiles, error: behaviorError } = await supabase
      .from('user_behavior_profiles')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(1000);

    if (eventsError || behaviorError) {
      console.error('Error fetching security data:', eventsError || behaviorError);
    }

    return new Response(JSON.stringify({
      securityEvents: securityEvents || [],
      behaviorProfiles: behaviorProfiles || []
    }), {
      status: 200,
      headers: corsHeaders,
    });
  } catch (error) {
    console.error('Error in handleSecurityEvents:', error);
    return new Response(JSON.stringify({ error: 'Failed to fetch security events' }), {
      status: 500,
      headers: corsHeaders,
    });
  }
};

const handleBehaviorProfiles = async (corsHeaders) => {
  try {
    const { data: profiles, error } = await supabase
      .from('user_behavior_profiles')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(50);

    if (error) {
      console.error('Error fetching behavior profiles:', error);
      return new Response(JSON.stringify({ error: 'Failed to fetch behavior profiles' }), {
        status: 500,
        headers: corsHeaders,
      });
    }

    // Process and enhance the profiles data for ML Security dashboard
    const enhancedProfiles = (profiles || []).map(profile => ({
      id: profile.id,
      session_duration: profile.session_duration || 0,
      mouse_movement_count: profile.mouse_movements?.length || 0,
      click_count: profile.click_patterns?.length || 0,
      typing_speed_wpm: profile.typing_metrics?.wpm || 0,
      anomaly_score: profile.anomaly_score || 0,
      created_at: profile.created_at,
      fingerprint_id: profile.fingerprint_id,
      risk_level: (profile.anomaly_score || 0) > 0.7 ? 'high' :
                 (profile.anomaly_score || 0) > 0.4 ? 'medium' : 'low'
    }));

    return new Response(JSON.stringify(enhancedProfiles), {
      status: 200,
      headers: corsHeaders,
    });
  } catch (error) {
    console.error('Error in handleBehaviorProfiles:', error);
    return new Response(JSON.stringify({ error: 'Failed to fetch behavior profiles' }), {
      status: 500,
      headers: corsHeaders,
    });
  }
};

export default allowCors(adminKeysHandler);