import { createClient } from '@supabase/supabase-js';
import jwt from 'jsonwebtoken';

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseAnonKey = process.env.SUPABASE_ANON_KEY;
const supabase = createClient(supabaseUrl, supabaseAnonKey);

const JWT_SECRET = process.env.ADMIN_JWT_SECRET;
if (!JWT_SECRET) {
  throw new Error('ADMIN_JWT_SECRET environment variable is required');
}

const allowCors = (fn) => async (request, context) => {
    const origin = request.headers.get('origin') || '*';
    const headers = {
        'Access-Control-Allow-Origin': origin,
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type',
        'Access-Control-Allow-Credentials': 'true',
    };
    if (request.method === 'OPTIONS') {
        return new Response('', { status: 204, headers });
    }
    const result = await fn(request, context, headers);
    return result;
};

const loginHandler = async (request, context, corsHeaders) => {
    // JWT secret validation handled at startup
    if (request.method !== 'POST') {
        return new Response(JSON.stringify({ error: 'Method Not Allowed' }), {
            status: 405,
            headers: corsHeaders
        });
    }
    const { username, password } = await request.json();
    if (!username || !password) {
        return new Response(JSON.stringify({ error: 'Username and password are required.' }), {
            status: 400,
            headers: corsHeaders
        });
    }
    // Check database for admin users first
    const { data: adminUser, error: dbError } = await supabase
      .from('admin_users')
      .select('*')
      .eq('username', username)
      .eq('is_active', true)
      .single();

    let role = null;
    let permissions = {};

    if (!dbError && adminUser) {
      // For now, we'll still use plain text comparison (will be updated to bcrypt later)
      if (adminUser.password_hash === password) {
        role = adminUser.role;
        permissions = adminUser.permissions || {};
      }
    } else {
      // Fallback to environment variables for backward compatibility
      if (username === process.env.OWNER_USER && password === process.env.OWNER_PASSWORD) {
          role = 'owner';
      } else if (username === process.env.ADMIN_USER && password === process.env.ADMIN_PASSWORD) {
          role = 'admin';
      }
    }

    if (!role) {
        return new Response(JSON.stringify({ error: 'Invalid username or password.' }), {
            status: 401,
            headers: corsHeaders
        });
    }

    // Update last login time if user exists in database
    if (adminUser) {
      await supabase
        .from('admin_users')
        .update({ last_login: new Date().toISOString() })
        .eq('id', adminUser.id);
    }

    // Issue JWT
    const token = jwt.sign({ username, role, permissions }, JWT_SECRET, { expiresIn: '2h' });
    return new Response(JSON.stringify({
        token,
        admin: { username, role, permissions },
    }), {
        status: 200,
        headers: corsHeaders
    });
};

export default allowCors(loginHandler); 