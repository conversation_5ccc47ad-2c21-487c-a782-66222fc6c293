import { createClient } from '@supabase/supabase-js';

// CORS helper
const allowCors = (handler) => {
  return async (request, context) => {
    const headers = {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    };

    if (request.method === 'OPTIONS') {
      return new Response('', { status: 200, headers });
    }

    try {
      const result = await handler(request, context, headers);
      return result;
    } catch (error) {
      return new Response(JSON.stringify({ error: error.message }), {
        status: 500,
        headers,
      });
    }
  };
};

// Initialize Supabase
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

// Security violation patterns with temporary ban durations (in minutes)
const SECURITY_PATTERNS = {
  // Critical violations - 3 hour bans
  CRITICAL: {
    violations: [
      'suspicious_script_injection',
      'function_modification',
      'script_injection',
      'eval_usage'
    ],
    banDuration: 180 // 3 hours
  },

  // High-risk violations - 30 minute bans
  HIGH: {
    violations: [
      'script_tampering',
      'timing_manipulation',
      'honeypot_access'
    ],
    banDuration: 30 // 30 minutes
  },

  // Medium-risk violations - 5 minute bans
  MEDIUM: {
    violations: [
      'honeypot_endpoint',
      'honeypot_vulnerability',
      'step_through_detected'
    ],
    banDuration: 5 // 5 minutes
  },

  // Low-risk violations - 1 minute warning bans
  LOW: {
    violations: [
      'console_access',
      'console_object_access',
      'timer_manipulation'
    ],
    banDuration: 1 // 1 minute warning
  }
};

// Violation response actions - now using temporary bans
const VIOLATION_RESPONSES = {
  script_tampering: 'TEMP_BAN', // 30 min ban instead of permanent
  suspicious_script_injection: 'TEMP_BAN', // 3 hour ban for injection
  function_modification: 'TEMP_BAN', // 3 hour ban for modification
  dev_tools_detected: 'LOG_ONLY',
  debug_props_detected: 'LOG_ONLY',
  debug_functions_detected: 'LOG_ONLY',
  breakpoint_detected: 'LOG_ONLY',
  console_access: 'TEMP_BAN', // 1 minute warning ban
  eval_usage: 'TEMP_BAN', // 3 hour ban for eval usage
  script_injection: 'TEMP_BAN', // 3 hour ban for injection
  timing_manipulation: 'TEMP_BAN', // 30 min ban for timing attacks
  honeypot_access: 'TEMP_BAN', // 30 min ban for honeypot access
  honeypot_endpoint: 'TEMP_BAN', // 5 min ban for endpoint probing
  honeypot_vulnerability: 'TEMP_BAN' // 5 min ban for vuln scanning
};

const securityViolationHandler = async (request, context, corsHeaders) => {
  if (request.method !== 'POST') {
    return new Response(JSON.stringify({ error: 'Method not allowed' }), {
      status: 405,
      headers: corsHeaders,
    });
  }

  try {
    const violation = await request.json();
    const headers = request.headers;
    const ipAddress = headers.get('x-forwarded-for') || headers.get('x-real-ip') || 'unknown';
    const userAgent = headers.get('user-agent') || 'unknown';

    // Validate violation data
    if (!violation.type || !violation.details) {
      return new Response(JSON.stringify({ error: 'Invalid violation data' }), {
        status: 400,
        headers: corsHeaders,
      });
    }

    // Determine violation severity
    const severity = getViolationSeverity(violation.type);
    const responseAction = VIOLATION_RESPONSES[violation.type] || 'LOG_ONLY';

    // Log the violation
    await logSecurityViolation(violation, ipAddress, userAgent, severity);

    // Take action based on violation type
    switch (responseAction) {
      case 'TEMP_BAN':
        await applyTemporaryBan(ipAddress, violation);
        break;
      case 'TRACK_ATTACKER':
        await trackAttacker(ipAddress, violation);
        break;
      case 'LOG_ONLY':
      default:
        // Just log the violation
        break;
    }

    // Check for attack patterns
    const attackPattern = await detectAttackPattern(ipAddress, violation.type);
    if (attackPattern) {
      await handleAttackPattern(attackPattern, ipAddress, violation);
    }

    return new Response(JSON.stringify({ 
      success: true, 
      action: responseAction,
      severity: severity 
    }), {
      status: 200,
      headers: corsHeaders,
    });

  } catch (error) {
    console.error('Security violation handler error:', error);
    return new Response(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: corsHeaders,
    });
  }
};

// Helper functions
const getViolationSeverity = (violationType) => {
  if (SECURITY_PATTERNS.CRITICAL.violations.includes(violationType)) return 'CRITICAL';
  if (SECURITY_PATTERNS.HIGH.violations.includes(violationType)) return 'HIGH';
  if (SECURITY_PATTERNS.MEDIUM.violations.includes(violationType)) return 'MEDIUM';
  if (SECURITY_PATTERNS.LOW.violations.includes(violationType)) return 'LOW';
  return 'UNKNOWN';
};

const getBanDuration = (violationType) => {
  if (SECURITY_PATTERNS.CRITICAL.violations.includes(violationType)) return SECURITY_PATTERNS.CRITICAL.banDuration;
  if (SECURITY_PATTERNS.HIGH.violations.includes(violationType)) return SECURITY_PATTERNS.HIGH.banDuration;
  if (SECURITY_PATTERNS.MEDIUM.violations.includes(violationType)) return SECURITY_PATTERNS.MEDIUM.banDuration;
  if (SECURITY_PATTERNS.LOW.violations.includes(violationType)) return SECURITY_PATTERNS.LOW.banDuration;
  return 5; // Default 5 minute ban
};

const logSecurityViolation = async (violation, ipAddress, userAgent, severity) => {
  try {
    await supabase
      .from('security_violations')
      .insert({
        violation_type: violation.type,
        violation_details: violation.details,
        severity: severity,
        ip_address: ipAddress,
        user_agent: userAgent,
        url: violation.url,
        timestamp: new Date(violation.timestamp).toISOString(),
        violation_data: violation
      });
  } catch (error) {
    console.error('Error logging security violation:', error);
  }
};

const applyTemporaryBan = async (ipAddress, violation) => {
  try {
    const banDuration = getBanDuration(violation.type);

    // Use the database function to apply temporary ban with escalation
    const { data, error } = await supabase
      .rpc('apply_temporary_ban', {
        ip: ipAddress,
        violation_type_param: violation.type,
        base_duration_minutes: banDuration,
        banned_by_param: 'system'
      });

    if (error) {
      console.error('Error applying temporary ban:', error);
    } else if (data && data.length > 0) {
      const banInfo = data[0];
      const actualDuration = Math.round((new Date(banInfo.expires_at_result) - new Date()) / 60000);
      console.log(`IP ${ipAddress} temporarily banned for ${actualDuration} minutes for violation: ${violation.type}. Violation count: ${banInfo.violation_count_result}`);

      // Log the ban details
      await logSecurityViolation({
        type: 'temporary_ban_applied',
        details: {
          original_violation: violation.type,
          ban_duration_minutes: banDuration,
          actual_duration_minutes: actualDuration,
          violation_count: banInfo.violation_count_result,
          expires_at: banInfo.expires_at_result
        }
      }, ipAddress, '', 'HIGH');
    }
  } catch (error) {
    console.error('Error in applyTemporaryBan:', error);
  }
};

const trackAttacker = async (ipAddress, violation) => {
  try {
    // Track honeypot interactions
    await supabase
      .from('honeypot_interactions')
      .insert({
        ip_address: ipAddress,
        violation_type: violation.type,
        violation_details: violation.details,
        user_agent: violation.userAgent,
        url: violation.url,
        timestamp: new Date(violation.timestamp).toISOString(),
        interaction_data: violation
      });
    
    console.log(`Honeypot interaction tracked from ${ipAddress}: ${violation.type}`);
  } catch (error) {
    console.error('Error tracking attacker:', error);
  }
};

const detectAttackPattern = async (ipAddress, violationType) => {
  try {
    // Check for multiple violations from same IP in short time
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000).toISOString();
    
    const { data: recentViolations } = await supabase
      .from('security_violations')
      .select('violation_type, timestamp')
      .eq('ip_address', ipAddress)
      .gte('timestamp', oneHourAgo);

    if (recentViolations && recentViolations.length >= 5) {
      return {
        type: 'MULTIPLE_VIOLATIONS',
        count: recentViolations.length,
        violations: recentViolations
      };
    }

    // Check for specific attack patterns
    if (violationType.startsWith('honeypot_')) {
      const { data: honeypotInteractions } = await supabase
        .from('honeypot_interactions')
        .select('violation_type, timestamp')
        .eq('ip_address', ipAddress)
        .gte('timestamp', oneHourAgo);

      if (honeypotInteractions && honeypotInteractions.length >= 3) {
        return {
          type: 'HONEYPOT_ATTACK',
          count: honeypotInteractions.length,
          interactions: honeypotInteractions
        };
      }
    }

    return null;
  } catch (error) {
    console.error('Error detecting attack pattern:', error);
    return null;
  }
};

const handleAttackPattern = async (attackPattern, ipAddress, violation) => {
  try {
    // Log the attack pattern
    await supabase
      .from('attack_patterns')
      .insert({
        ip_address: ipAddress,
        pattern_type: attackPattern.type,
        violation_count: attackPattern.count,
        first_violation: violation.timestamp,
        pattern_data: attackPattern
      });

    // Take additional action for attack patterns
    if (attackPattern.type === 'MULTIPLE_VIOLATIONS' && attackPattern.count >= 10) {
      await blockIPAddress(ipAddress, {
        type: 'attack_pattern',
        details: `Multiple violations detected: ${attackPattern.count} in 1 hour`
      });
    }

    console.log(`Attack pattern detected from ${ipAddress}: ${attackPattern.type}`);
  } catch (error) {
    console.error('Error handling attack pattern:', error);
  }
};

export default allowCors(securityViolationHandler); 