import { createClient } from '@supabase/supabase-js';
import jwt from 'jsonwebtoken';

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;
const supabase = createClient(supabaseUrl, supabaseAnonKey);

const JWT_SECRET = process.env.ADMIN_JWT_SECRET || process.env.JWT_SECRET;
if (!JWT_SECRET) {
  console.error('Missing JWT_SECRET or ADMIN_JWT_SECRET environment variable');
}

const allowCors = (fn) => async (request, context) => {
    const origin = request.headers.get('origin') || '*';
    const headers = {
        'Access-Control-Allow-Origin': origin,
        'Access-Control-Allow-Methods': 'GET, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Allow-Credentials': 'true',
    };
    if (request.method === 'OPTIONS') {
        return new Response('', { status: 204, headers });
    }
    const result = await fn(request, context, headers);
    return result;
};

const verifySimpleAdmin = (request) => {
  const username = request.headers.get('x-admin-username');
  const password = request.headers.get('x-admin-password');
  if (username === process.env.OWNER_USER && password === process.env.OWNER_PASSWORD) {
    return { admin: { username, role: 'owner' } };
  }
  if (username === process.env.ADMIN_USER && password === process.env.ADMIN_PASSWORD) {
    return { admin: { username, role: 'admin' } };
  }
  if (username === process.env.ML_SECURITY_USER && password === process.env.ML_SECURITY_PASSWORD) {
    return { admin: { username, role: 'ml_security' } };
  }
  return { error: 'Unauthorized' };
};

const originalHandler = async (request, context, corsHeaders) => {
    const url = new URL(request.url);
    const queryStringParameters = Object.fromEntries(url.searchParams.entries());
    const headers = request.headers;
    let body = null;
    if (request.method === 'POST' || request.method === 'PUT') {
        body = await request.json();
    }
    // Admin authentication
    const authResult = verifySimpleAdmin(request);
    if (authResult.error) {
      return new Response(JSON.stringify({ error: authResult.error }), {
        status: 401,
        headers: corsHeaders
      });
    }

    if (request.method === 'GET') {
        const { id } = queryStringParameters;
        if (id) {
            const { data, error } = await supabase.from('script_requests').select('*').eq('id', id).single();
            if (error) {
                return new Response(JSON.stringify({ error: error.message }), {
                    status: 500,
                    headers: corsHeaders
                });
            }
            if (!data) {
                return new Response(JSON.stringify({ error: 'Script request not found.' }), {
                    status: 404,
                    headers: corsHeaders
                });
            }
            return new Response(JSON.stringify(data), {
                status: 200,
                headers: corsHeaders
            });
        } else {
            const { data, error } = await supabase.from('script_requests').select('*');
            if (error) {
                return new Response(JSON.stringify({ error: error.message }), {
                    status: 500,
                    headers: corsHeaders
                });
            }
            return new Response(JSON.stringify(data), {
                status: 200,
                headers: corsHeaders
            });
        }
    }

    if (request.method === 'POST') {
        const { user_name, script_name, description } = body;
        if (!user_name || !script_name || !description) {
            return new Response(JSON.stringify({ error: 'Missing required fields.' }), {
                status: 400,
                headers: corsHeaders
            });
        }
        const { data, error } = await supabase.from('script_requests').insert([
            { user_name, script_name, description, status: 'pending', requested_at: new Date().toISOString() }
        ]);
        if (error) {
            return new Response(JSON.stringify({ error: error.message }), {
                status: 500,
                headers: corsHeaders
            });
        }
        return new Response(JSON.stringify(data[0]), {
            status: 201,
            headers: corsHeaders
        });
    }

    if (request.method === 'PUT') {
        const { id, status, admin_notes } = body;
        const updateFields = {
            status,
            reviewed_by: authResult.admin.username,
            reviewed_at: new Date().toISOString()
        };
        if (admin_notes !== undefined) updateFields.admin_notes = admin_notes;
        const { data, error } = await supabase
            .from('script_requests')
            .update(updateFields)
            .eq('id', id)
            .select();
        if (error) {
            return new Response(JSON.stringify({ error: error.message }), {
                status: 500,
                headers: corsHeaders
            });
        }
        return new Response(JSON.stringify(data), {
            status: 200,
            headers: corsHeaders
        });
    }

    if (request.method === 'DELETE') {
        const { id } = queryStringParameters;
        if (!id) {
            return new Response(JSON.stringify({ error: 'Script request ID is required for deletion.' }), {
                status: 400,
                headers: corsHeaders
            });
        }
        const { error } = await supabase.from('script_requests').delete().eq('id', id);
        if (error) {
            return new Response(JSON.stringify({ error: error.message }), {
                status: 500,
                headers: corsHeaders
            });
        }
        return new Response('', { status: 204, headers: corsHeaders });
    }

    return new Response(JSON.stringify({ error: 'Method Not Allowed' }), {
        status: 405,
        headers: corsHeaders
    });
};

export default allowCors(originalHandler); 