import { v4 as uuidv4 } from 'uuid';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_SERVICE_ROLE_KEY);
const LINKVERTISE_ANTI_BYPASS_TOKEN = process.env.LINKVERTISE_ANTI_BYPASS_TOKEN;

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'Content-Type',
  'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
};

export const handler = async (event) => {
  if (event.httpMethod === 'OPTIONS') {
    return { statusCode: 200, headers: corsHeaders };
  }

  try {
    if (event.httpMethod === 'POST') {
      const body = JSON.parse(event.body || '{}');
      if (body.action === 'start') {
        // Extract the first IP address from x-forwarded-for
        const rawIpAddress = event.headers['x-forwarded-for'] || '';
        const ipAddress = rawIpAddress.split(',')[0].trim();
        const userAgent = event.headers['user-agent'] || '';
        const expiresAt = new Date(Date.now() + 30 * 60 * 1000).toISOString(); // 30 min expiry

        // Try to find an existing session for this IP and User-Agent that is not expired
        const { data: existingSession, error: fetchError } = await supabase
          .from('key_sessions')
          .select('session_id, expires_at')
          .eq('ip_address', ipAddress)
          .eq('user_agent', userAgent)
          .gt('expires_at', new Date().toISOString())
          .single();

        if (fetchError && fetchError.code !== 'PGRST116') { // PGRST116 means no rows found
          console.error('Error fetching existing session:', fetchError);
          return {
            statusCode: 500,
            headers: corsHeaders,
            body: JSON.stringify({ error: 'Failed to check for existing session', details: fetchError.message })
          };
        }

        let sessionId;
        if (existingSession) {
          sessionId = existingSession.session_id;
          // Extend session expiry if it exists
          const { error: updateError } = await supabase.from('key_sessions')
            .update({ expires_at: expiresAt })
            .eq('session_id', sessionId);

          if (updateError) {
            console.error('Error updating session expiry:', updateError);
            return {
              statusCode: 500,
              headers: corsHeaders,
              body: JSON.stringify({ error: 'Failed to extend session expiry', details: updateError.message })
            };
          }
        } else {
          // Create new session if no active session found
          sessionId = uuidv4();
          const { error: insertError } = await supabase.from('key_sessions').insert({
            session_id: sessionId,
            step1: false,
            step2: false,
            expires_at: expiresAt,
            ip_address: ipAddress,
            user_agent: userAgent
          });
          if (insertError) {
            console.error('Error creating new session:', insertError);
            return {
              statusCode: 500,
              headers: corsHeaders,
              body: JSON.stringify({ error: 'Failed to create new session', details: insertError.message })
            };
          }
        }
        
        return {
          statusCode: 200,
          headers: {
            ...corsHeaders,
            'Set-Cookie': `keysession=${sessionId}; HttpOnly; Path=/; Max-Age=1800; SameSite=Strict`,
          },
          body: JSON.stringify({ sessionId })
        };
      }
      if (body.action === 'complete' && body.sessionId && (body.step === 1 || body.step === 2)) {
        // Linkvertise Anti-Bypass API verification
        const hash = body.token; // Linkvertise documentation refers to this as 'hash'
        const userAgent = event.headers['user-agent'] || '';
        const rawIpAddressForLog = event.headers['x-forwarded-for'] || '';
        const ipAddressForLog = rawIpAddressForLog.split(',')[0].trim();

        if (!LINKVERTISE_ANTI_BYPASS_TOKEN) {
          console.error('LINKVERTISE_ANTI_BYPASS_TOKEN is not set.');
          await supabase.from('key_usage_logs').insert({
            key_id: null, hwid_hash: null, ip_address: ipAddressForLog, user_agent: userAgent,
            action: 'linkvertise_verification_error', success: false,
            error_message: 'Linkvertise anti-bypass token not configured on server.',
            details: JSON.stringify({ sessionId: body.sessionId, hash })
          });
          return {
            statusCode: 500,
            headers: corsHeaders,
            body: JSON.stringify({ error: 'Server configuration error. Please contact support.' })
          };
        }

        // Validate hash format before sending to Linkvertise
        if (!hash || typeof hash !== 'string' || hash.length !== 64 || !/^[a-zA-Z0-9]+$/.test(hash)) {
          await supabase.from('key_usage_logs').insert({
            key_id: null, hwid_hash: null, ip_address: ipAddressForLog, user_agent: userAgent,
            action: 'invalid_hash_format', success: false,
            error_message: 'Invalid Linkvertise verification hash format.',
            details: JSON.stringify({ sessionId: body.sessionId, hash })
          });
          return {
            statusCode: 400,
            headers: corsHeaders,
            body: JSON.stringify({ error: 'Invalid verification hash provided. Please try again.' })
          };
        }

        let linkvertiseVerificationSuccess = false;
        let linkvertiseErrorMessage = 'Unknown Linkvertise verification error.';

        try {
          const lvResponse = await fetch('https://publisher.linkvertise.com/api/v1/anti_bypassing', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ token: LINKVERTISE_ANTI_BYPASS_TOKEN, hash: hash })
          });

          const lvResult = await lvResponse.json();

          if (lvResponse.ok) {
            if (lvResult === true || lvResult.status === true) {
              linkvertiseVerificationSuccess = true;
            } else if (lvResult === false || lvResult.status === false) {
              linkvertiseErrorMessage = 'Linkvertise hash not found or already used.';
            } else {
              linkvertiseErrorMessage = `Unexpected Linkvertise response: ${JSON.stringify(lvResult)}`;
            }
          } else {
            // Handle HTTP errors from Linkvertise API
            linkvertiseErrorMessage = `Linkvertise API error: ${lvResponse.status} - ${JSON.stringify(lvResult)}`;
          }
        } catch (fetchErr) {
          console.error('Error fetching from Linkvertise Anti-Bypass API:', fetchErr);
          linkvertiseErrorMessage = `Network error during Linkvertise verification: ${fetchErr.message}`;
        }

        if (!linkvertiseVerificationSuccess) {
          await supabase.from('key_usage_logs').insert({
            key_id: null, hwid_hash: null, ip_address: ipAddressForLog, user_agent: userAgent,
            action: 'linkvertise_verification_failed', success: false,
            error_message: linkvertiseErrorMessage,
            details: JSON.stringify({ sessionId: body.sessionId, hash })
          });
          return {
            statusCode: 400,
            headers: corsHeaders,
            body: JSON.stringify({ error: `Linkvertise verification failed: ${linkvertiseErrorMessage}` })
          };
        }

        // Store used token (hash) to prevent replay on our side for extra logging/security
        await supabase
          .from('used_tokens')
          .insert({
            token: hash, // Store the hash as the token
            session_id: body.sessionId,
            step: body.step,
            user_agent: userAgent,
            created_at: new Date().toISOString()
          });

        const field = body.step === 1 ? 'step1' : 'step2';
        const { error } = await supabase.from('key_sessions').update({ [field]: true }).eq('session_id', body.sessionId);
        if (error) {
          await supabase.from('key_usage_logs').insert({
            key_id: null, hwid_hash: null, ip_address: ipAddressForLog, user_agent: userAgent,
            action: 'session_update_failed', success: false,
            error_message: 'Failed to update session status after Linkvertise verification.',
            details: JSON.stringify({ sessionId: body.sessionId, hash, step: body.step })
          });
          return {
            statusCode: 500,
            headers: corsHeaders,
            body: JSON.stringify({ error: 'Failed to update session status. Please try again.' })
          };
        }
        return { statusCode: 200, headers: corsHeaders, body: JSON.stringify({ success: true }) };
      }
      // New: Handle status check
      if (body.action === 'status' && body.sessionId) {
        const { data, error } = await supabase
          .from('key_sessions')
          .select('step1, step2')
          .eq('session_id', body.sessionId)
          .single();

        if (error || !data) {
          console.error('Error fetching session status:', error);
          return {
            statusCode: 404,
            headers: corsHeaders,
            body: JSON.stringify({ error: 'Session not found or error fetching status' })
          };
        }

        return {
          statusCode: 200,
          headers: corsHeaders,
          body: JSON.stringify({ status: { step1Completed: data.step1, step2Completed: data.step2 } })
        };
      }
      return { statusCode: 400, headers: corsHeaders, body: JSON.stringify({ error: 'Invalid request' }) };
    }

    if (event.httpMethod === 'GET') {
      const sessionId = event.queryStringParameters.sessionId;
      if (!sessionId) {
        return { statusCode: 400, headers: corsHeaders, body: JSON.stringify({ error: 'Missing sessionId' }) };
      }
      const { data, error } = await supabase.from('key_sessions').select('*').eq('session_id', sessionId).single();
      if (error || !data) {
        return { statusCode: 404, headers: corsHeaders, body: JSON.stringify({ error: 'Session not found' }) };
      }
      return { statusCode: 200, headers: corsHeaders, body: JSON.stringify(data) };
    }

    return { statusCode: 405, headers: corsHeaders, body: 'Method Not Allowed' };
  } catch (err) {
    console.error('Unhandled error in step-session:', err);
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({ error: 'Internal server error in step session.', details: err.message })
    };
  }
}; 