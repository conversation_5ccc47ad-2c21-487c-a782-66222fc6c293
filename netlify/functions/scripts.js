import { createClient } from '@supabase/supabase-js';
import jwt from 'jsonwebtoken';

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;
const supabase = createClient(supabaseUrl, supabaseAnonKey);

const JWT_SECRET = process.env.ADMIN_JWT_SECRET || process.env.JWT_SECRET;
if (!JWT_SECRET) {
  console.error('Missing JWT_SECRET or ADMIN_JWT_SECRET environment variable');
}

const allowCors = (fn) => async (request, context) => {
    const origin = request.headers.get('origin') || '*';
    const headers = {
        'Access-Control-Allow-Origin': origin,
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Allow-Credentials': 'true',
    };

    if (request.method === 'OPTIONS') {
        return new Response('', { status: 204, headers });
    }

    const result = await fn(request, context, headers);
    return result;
};

const verifySimpleAdmin = (request) => {
  const username = request.headers.get('x-admin-username');
  const password = request.headers.get('x-admin-password');

  // Check if admin credentials are configured
  if (!process.env.OWNER_USER || !process.env.OWNER_PASSWORD) {
    console.warn('Owner credentials not configured');
  }
  if (!process.env.ADMIN_USER || !process.env.ADMIN_PASSWORD) {
    console.warn('Admin credentials not configured');
  }

  if (username === process.env.OWNER_USER && password === process.env.OWNER_PASSWORD) {
    return { admin: { username, role: 'owner' } };
  }
  if (username === process.env.ADMIN_USER && password === process.env.ADMIN_PASSWORD) {
    return { admin: { username, role: 'admin' } };
  }
  return { error: 'Unauthorized' };
};

const originalHandler = async (request, context, corsHeaders) => {
    const url = new URL(request.url);
    const queryStringParameters = Object.fromEntries(url.searchParams.entries());
    const headers = request.headers;
    let body = null;
    if (request.method === 'POST' || request.method === 'PUT') {
        body = await request.json();
    }

    // Only require admin for non-GET methods
    if (request.method !== 'GET') {
        const authResult = verifySimpleAdmin(request);
        if (authResult.error) {
            return new Response(JSON.stringify({ error: authResult.error }), {
                status: 401,
                headers: corsHeaders
            });
        }
    }

    // Robust GET handler for public scripts
    if (request.method === 'GET') {
        const { id } = queryStringParameters;
        if (id) {
            // Return a single script
            const { data, error } = await supabase
                .from('scripts')
                .select('*')
                .eq('id', id)
                .single();
            if (error || !data) {
                return new Response(JSON.stringify({ error: error?.message || 'Script not found' }), {
                    status: 404,
                    headers: corsHeaders
                });
            }
            return new Response(JSON.stringify(data), {
                status: 200,
                headers: corsHeaders
            });
        } else {
            // Return all active scripts
            const { data, error } = await supabase
                .from('scripts')
                .select('*')
                .eq('is_active', true)
                .order('created_at', { ascending: false });
            if (error) {
                return new Response(JSON.stringify([]), {
                    status: 200,
                    headers: corsHeaders
                });
            }
            return new Response(JSON.stringify(data || []), {
                status: 200,
                headers: corsHeaders
            });
        }
    }

    if (request.method === 'POST') {
        // Validate required fields
        if (!body.name || !body.content) {
            return new Response(JSON.stringify({ error: 'Name and content are required.' }), {
                status: 400,
                headers: corsHeaders
            });
        }
        // Insert into scripts table
        const { data, error } = await supabase
            .from('scripts')
            .insert([{ ...body }])
            .select()
            .single();
        if (error) {
            return new Response(JSON.stringify({ error: error.message }), {
                status: 500,
                headers: corsHeaders
            });
        }
        return new Response(JSON.stringify(data), {
            status: 201,
            headers: corsHeaders
        });
    }

    if (request.method === 'PUT' && queryStringParameters.action === 'update-log') {
        // Validate required fields
        const { script_id, version, changes, updated_by } = body || {};
        if (!script_id || !version || !changes || !updated_by) {
            return new Response(JSON.stringify({ error: 'Missing required fields.' }), {
                status: 400,
                headers: corsHeaders
            });
        }

        // Insert into script_update_logs table
        const { data, error } = await supabase
            .from('script_update_logs')
            .insert([{
                script_id,
                version,
                changes,
                updated_by
            }])
            .select()
            .single();

        if (error) {
            return new Response(JSON.stringify({ error: error.message }), {
                status: 500,
                headers: corsHeaders
            });
        }

        return new Response(JSON.stringify({ success: true, log: data }), {
            status: 201,
            headers: corsHeaders
        });
    }

    if (request.method === 'PUT' && !queryStringParameters.action) {
        // Regular script update
        const { id, ...updateData } = body || {};
        if (!id) {
            return new Response(JSON.stringify({ error: 'Script ID is required.' }), {
                status: 400,
                headers: corsHeaders
            });
        }

        const { data, error } = await supabase
            .from('scripts')
            .update(updateData)
            .eq('id', id)
            .select()
            .single();

        if (error) {
            return new Response(JSON.stringify({ error: error.message }), {
                status: 500,
                headers: corsHeaders
            });
        }

        return new Response(JSON.stringify(data), {
            status: 200,
            headers: corsHeaders
        });
    }

    if (request.method === 'DELETE') {
        const { id } = queryStringParameters;
        if (!id) {
            return new Response(JSON.stringify({ error: 'Script ID is required.' }), {
                status: 400,
                headers: corsHeaders
            });
        }

        const { error } = await supabase
            .from('scripts')
            .delete()
            .eq('id', id);

        if (error) {
            return new Response(JSON.stringify({ error: error.message }), {
                status: 500,
                headers: corsHeaders
            });
        }

        return new Response(JSON.stringify({ success: true }), {
            status: 200,
            headers: corsHeaders
        });
    }

    // ... handle PUT, DELETE as needed
    return new Response(JSON.stringify({ error: 'Method Not Allowed' }), {
        status: 405,
        headers: corsHeaders
    });
};

export default allowCors(originalHandler); 