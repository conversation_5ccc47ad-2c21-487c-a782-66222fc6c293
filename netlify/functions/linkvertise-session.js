import { createClient } from '@supabase/supabase-js';
import { v4 as uuidv4 } from 'uuid';

const allowCors = (handler) => {
  return async (request, context) => {
    const headers = {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
    };
    if (request.method === 'OPTIONS') {
      return new Response('', { status: 200, headers });
    }
    try {
      const result = await handler(request, context, headers);
      return result;
    } catch (error) {
      return new Response(JSON.stringify({ error: error.message }), {
        status: 500,
        headers,
      });
    }
  };
};

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

const SESSION_EXPIRY_MINUTES = 30;

const handler = async (request, context, corsHeaders) => {
  if (request.method === 'POST') {
    // Generate a new session token
    const headers = request.headers;
    const ipAddress = headers.get('x-forwarded-for') || headers.get('x-real-ip') || 'unknown';
    const userAgent = headers.get('user-agent') || 'unknown';
    const token = uuidv4();
    const now = new Date();
    const expiresAt = new Date(now.getTime() + SESSION_EXPIRY_MINUTES * 60 * 1000);
    const { error } = await supabase.from('linkvertise_sessions').insert({
      token,
      ip_address: ipAddress,
      user_agent: userAgent,
      created_at: now.toISOString(),
      expires_at: expiresAt.toISOString(),
      used: false
    });
    if (error) {
      return new Response(JSON.stringify({ error: 'Failed to create session token' }), {
        status: 500,
        headers: corsHeaders,
      });
    }
    return new Response(JSON.stringify({ token, expires_at: expiresAt.toISOString() }), {
      status: 201,
      headers: corsHeaders,
    });
  }
  if (request.method === 'GET') {
    // Validate a session token
    const url = new URL(request.url);
    const token = url.searchParams.get('token');
    if (!token) {
      return new Response(JSON.stringify({ error: 'Token required' }), {
        status: 400,
        headers: corsHeaders,
      });
    }
    const { data: session, error } = await supabase
      .from('linkvertise_sessions')
      .select('*')
      .eq('token', token)
      .single();
    if (error || !session) {
      return new Response(JSON.stringify({ error: 'Invalid or expired session token' }), {
        status: 400,
        headers: corsHeaders,
      });
    }
    if (session.used) {
      return new Response(JSON.stringify({ error: 'Session token already used' }), {
        status: 400,
        headers: corsHeaders,
      });
    }
    if (new Date(session.expires_at) < new Date()) {
      return new Response(JSON.stringify({ error: 'Session token expired' }), {
        status: 400,
        headers: corsHeaders,
      });
    }
    return new Response(JSON.stringify({ valid: true, session }), {
      status: 200,
      headers: corsHeaders,
    });
  }
  return new Response(JSON.stringify({ error: 'Method not allowed' }), {
    status: 405,
    headers: corsHeaders,
  });
};

export default allowCors(handler); 