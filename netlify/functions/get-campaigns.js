import { createClient } from '@supabase/supabase-js';

// CORS helper
const allowCors = (handler) => {
  return async (request, context) => {
    const headers = {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
    };

    if (request.method === 'OPTIONS') {
      return new Response('', { status: 200, headers });
    }

    try {
      const result = await handler(request, context, headers);
      return result;
    } catch (error) {
      return new Response(JSON.stringify({ error: error.message }), {
        status: 500,
        headers,
      });
    }
  };
};

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY; // Use anon key for public read

const supabase = createClient(supabaseUrl, supabaseAnonKey);

const handler = async (request, context, corsHeaders) => {
  if (request.method !== 'GET') {
    return new Response(JSON.stringify({ error: 'Method not allowed' }), {
      status: 405,
      headers: corsHeaders,
    });
  }

  try {
    const { data: campaigns, error } = await supabase
      .from('linkvertise_campaigns')
      .select('id, name, step1_url, step2_url') // Select necessary fields
      .eq('is_active', true);

    if (error) {
      console.error('Error fetching campaigns:', error);
      return new Response(JSON.stringify({ error: 'Failed to fetch campaigns' }), {
        status: 500,
        headers: corsHeaders,
      });
    }

    return new Response(JSON.stringify(campaigns), {
      status: 200,
      headers: corsHeaders,
    });
  } catch (error) {
    console.error('Unhandled error in get-campaigns function:', error);
    return new Response(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: corsHeaders,
    });
  }
};

export default allowCors(handler); 