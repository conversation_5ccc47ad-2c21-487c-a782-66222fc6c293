import { defineConfig, loadEnv } from "vite";
import react from "@vitejs/plugin-react";
import { resolve } from "path";

export default defineConfig(({ mode }) => {
  // Load env file based on `mode` in the current working directory
  const env = loadEnv(mode, process.cwd(), '');
  
  // Get and validate Supabase configuration
  const supabaseUrl = env.VITE_SUPABASE_URL || env.SUPABASE_URL;
  const supabaseAnonKey = env.VITE_SUPABASE_ANON_KEY || env.SUPABASE_ANON_KEY;

  if (!supabaseUrl) {
    console.error('❌ Error: Missing Supabase URL. Please set VITE_SUPABASE_URL or SUPABASE_URL in your environment variables.');
    process.exit(1);
  }

  if (!supabaseAnonKey) {
    console.error('❌ Error: Missing Supabase Anon Key. Please set VITE_SUPABASE_ANON_KEY or SUPABASE_ANON_KEY in your environment variables.');
    process.exit(1);
  }

  console.log('✅ Using Supabase URL:', supabaseUrl.replace(/\/auth\/v1$/, ''));
  
  return {
    define: {
      // Hardcode the values to ensure they're properly inlined
      'import.meta.env.VITE_SUPABASE_URL': JSON.stringify(supabaseUrl),
      'import.meta.env.VITE_SUPABASE_ANON_KEY': JSON.stringify(supabaseAnonKey),
      'process.env': {}
    },
    plugins: [react()],
    resolve: {
      alias: {
        "@": resolve("./src"),
      },
    },
    build: {
      outDir: "dist",
      minify: "terser",
      sourcemap: true,
      rollupOptions: {
        output: {
          manualChunks: {
            // Split vendor modules into separate chunks
            react: ['react', 'react-dom', 'react-router-dom'],
            radix: ['@radix-ui/themes', '@radix-ui/react-slot'],
            antd: ['antd', '@ant-design/icons'],
            // Split large dependencies into separate chunks
            framer: ['framer-motion'],
            // Group other node modules
            vendor: [
              'axios',
              'dayjs',
              'react-hook-form',
              'react-intersection-observer',
              'react-icons',
            ],
          },
        },
      },
      chunkSizeWarningLimit: 1000, // Revert to original limit
      assetsInlineLimit: 0, // Don't inline assets to prevent TensorFlow.js issues
    },
    server: {
      port: 3000,
      strictPort: true,
      open: true,
    },
  };
});