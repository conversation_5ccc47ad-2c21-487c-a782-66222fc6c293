import { defineConfig, loadEnv } from "vite";
import react from "@vitejs/plugin-react";
import { resolve } from "path";
import { cjsInterop } from "vite-plugin-cjs-interop";

export default defineConfig(({ mode }) => {
  // Load env file based on `mode` in the current working directory
  const env = loadEnv(mode, process.cwd(), '');
  
  // Get and validate Supabase configuration
  const supabaseUrl = env.VITE_SUPABASE_URL || env.SUPABASE_URL;
  const supabaseAnonKey = env.VITE_SUPABASE_ANON_KEY || env.SUPABASE_ANON_KEY;

  if (!supabaseUrl) {
    console.error('❌ Error: Missing Supabase URL. Please set VITE_SUPABASE_URL or SUPABASE_URL in your environment variables.');
    process.exit(1);
  }

  if (!supabaseAnonKey) {
    console.error('❌ Error: Missing Supabase Anon Key. Please set VITE_SUPABASE_ANON_KEY or SUPABASE_ANON_KEY in your environment variables.');
    process.exit(1);
  }

  // Supabase URL configured successfully
  
  return {
    define: {
      // Hardcode the values to ensure they're properly inlined
      'import.meta.env.VITE_SUPABASE_URL': JSON.stringify(supabaseUrl),
      'import.meta.env.VITE_SUPABASE_ANON_KEY': JSON.stringify(supabaseAnonKey),
      'process.env': {}
    },
    plugins: [react({
      jsxInject: 'import React from "react";',
    }),
    cjsInterop({
      dependencies: [
        'sonner',
      ],
    }),
    ],
    resolve: {
      alias: {
        "@": resolve("./src"),
      },
    },
    optimizeDeps: {
      commonjsOptions: {
        transformMixedEsModules: true,
      },
    },
    build: {
      outDir: "dist",
      minify: mode === 'production' ? 'terser' : false,
      sourcemap: true,
      terserOptions: mode === 'production' ? {
        compress: {
          drop_console: false, // Keep console.log for debugging production issues
          drop_debugger: true,
          pure_funcs: ['console.debug'],
        },
        mangle: {
          safari10: true,
        },
      } : undefined,
      rollupOptions: {
        output: {
          manualChunks: (id) => {
            // Core React libraries
            if (id.includes('react') || id.includes('react-dom') || id.includes('react-router')) {
              return 'react';
            }

            // UI Libraries
            if (id.includes('@radix-ui')) {
              return 'radix';
            }
            if (id.includes('antd') || id.includes('@ant-design')) {
              return 'antd';
            }
            if (id.includes('framer-motion')) {
              return 'framer';
            }
            if (id.includes('lucide-react') || id.includes('react-icons')) {
              return 'icons';
            }

            // Charts and visualization
            if (id.includes('recharts') || id.includes('canvas-confetti')) {
              return 'charts';
            }

            // Security and crypto
            if (id.includes('crypto') || id.includes('bcrypt') || id.includes('fingerprint') || id.includes('@tensorflow')) {
              return 'security';
            }

            // Form and validation
            if (id.includes('react-hook-form') || id.includes('zod') || id.includes('@hookform')) {
              return 'forms';
            }

            // Utilities
            if (id.includes('axios') || id.includes('dayjs') || id.includes('date-fns') || id.includes('uuid') || id.includes('clsx') || id.includes('tailwind-merge')) {
              return 'utils';
            }

            // Supabase and auth
            if (id.includes('@supabase') || id.includes('jsonwebtoken')) {
              return 'auth';
            }

            // Admin components
            if (id.includes('/admin/') || id.includes('AdminDashboard') || id.includes('SecurityManagement') || id.includes('MLSecurity')) {
              return 'admin';
            }

            // Key system
            if (id.includes('KeySystem') || id.includes('KeyGenerator') || id.includes('KeyManagement')) {
              return 'keysystem';
            }

            // Pages (lazy loaded)
            if (id.includes('/pages/') && !id.includes('Home.jsx')) {
              return 'pages';
            }

            // Other vendor modules
            if (id.includes('node_modules')) {
              return 'vendor';
            }
          },
        },
      },
      chunkSizeWarningLimit: 500, // Lower threshold for better optimization
      assetsInlineLimit: 0, // Don't inline assets to prevent TensorFlow.js issues
    },
    server: {
      port: 3000,
      strictPort: true,
      open: true,
    },
  };
});
