What is TheKeySystem?

TheKeySystem is a Roblox-integrated key authentication system. Players who want to use your Roblox script must first go through a two-step Linkvertise process to obtain a 24-hour access key. Each key is locked to their hardware ID (HWID), and only one device (one HWID) can use a key. The system is built with a backend-first approach, focused on security and anti-bypass measures, then layered with a user-friendly website interface.

🧱 Project Structure and Organization

You’ll build the system in a structured way:

The root will contain a KeySystem folder, which holds everything.
Inside that, you’ll have:
api/ – all logic for generating, verifying, and managing keys.
web/ – the frontend that users interact with (link pages, key claim page).
admin/ – the admin dashboard with analytics, key list, revoke options, etc.
utils/ – useful helper scripts like random key generation, HWID checking, etc.
The database and models will live in a db/ directory, or as part of a models/ module.
The API will be separate from the main site logic, but still part of the same web application — just organized in its own folder.

🔑 Key Generation and Usage Flow

A user visits your website to get a key.
They complete Step 1 Linkvertise. After that, they are redirected to Step 2 Linkvertise.
Once both are completed, they are given a key that looks like:
MADARA-XXXX-XXXX-XXXX
(where X is a random alphanumeric character).
This key is valid for 24 hours from the time it's created.
The key is not tied to a user account — anonymous usage is allowed.
However, once the key is used in the Roblox script, it is locked to that user's HWID (a unique hardware ID generated on the Roblox side).
If someone else tries to use the same key with a different HWID, it will fail.
⏲️ Key Expiry

Keys will expire exactly 24 hours after they’re generated. This means:

If someone gets a key at 3:00 PM today, it will expire at 3:00 PM tomorrow.
After expiry, the key is no longer valid and the user must go through the Linkvertise steps again to get a new one.
🧠 Anti-Bypass Strategy

Since users might try to skip Linkvertise (e.g. using bypass tools like bypass-city.com), the plan includes:

Only issuing the key after both Linkvertise steps are completed.
Integrating Linkvertise’s API or campaign callback (if available) to verify completion.
Implementing rate limits, IP/device fingerprinting, or using tokens tied to the session during Linkvertise flow to ensure they’re not faking it.
If Linkvertise doesn’t offer a reliable API, we may have to implement token passing:
When a user starts Step 1, a session token is generated.
That token must persist through both steps and be returned to validate the flow.
If someone skips with a bypass tool, the required session token would be missing or invalid.
🧾 What Gets Tracked?

Each key will store:

When it was created.
When it expires.
Whether it is still active.
Which Linkvertise campaigns were used.
Whether it’s been redeemed yet (i.e., if the HWID has been locked in).
Each HWID entry will be tied to a key and store:

The actual HWID value.
When it was recorded (by the Roblox script).
Which key it was tied to.
🖥️ Roblox Script Behavior

When a user runs your Roblox script:

The script checks if the user has a valid key (via your KeySystem API).
If the key is valid and not expired, it checks the HWID field:
If it’s blank, it fills it in with the user’s HWID.
If it’s already filled:
If the HWID matches, allow access.
If it doesn't match, deny access (someone else is using that key).
This way, a key is locked to one device only, and others can’t reuse it — even if they know the key.

🧑‍💼 Admin Dashboard Features

Admins will be able to:

View all generated keys.
Search/filter keys by status (active, expired, redeemed).
See usage stats (e.g., how many people completed Linkvertise today).
Generate new keys manually (e.g., for giveaways or dev testing).
Revoke any key, deactivating it immediately.
See which HWIDs are tied to which keys.
Optional features could include:

Exporting data.
Viewing abuse patterns or logs.
Viewing failed validation attempts.
🎯 Goals of the Design

Security-first: Prevent script bypassing and fake key generation.
Fast backend: API-focused, lightweight, with minimal exposure.
Clean frontend: Simple, mobile-friendly interface for users to follow instructions.
Admin control: Powerful dashboard to monitor and manage usage.
Anonymous but protected: No accounts needed, but HWID-based locking provides control.
✅ Development Order

Set up backend and database models:
Keys and HWID logic.
API routes: generate, validate, expire.
Build anti-bypass logic and handle Linkvertise flow.
Create the Roblox script interface to contact the backend.
Develop the frontend website (2-step process and key display).
Finish with the admin dashboard (stats and management).

Is the key format strictly enforced as MADARA-XXXX-XXXX-XXXX? yes
Are keys being generated only after both Linkvertise steps are completed and verified? yes
Is the 24-hour expiry being tracked and enforced automatically? database can track i think.. created at and expires at
Is the HWID only set on first use, and does it prevent reuse on other devices? yes
Is there a way for admins to view which HWID is tied to which key? you can add a way too 
re you integrating with Linkvertise’s API or using a session token to prevent bypassing? on you to find that out
Is there rate limiting or device fingerprinting in place to prevent abuse? nope since the user will get 1 KEY per day
Can admins:
View all keys and their statuses (active, expired, redeemed)?
Search/filter keys by status or other criteria?
See usage stats (e.g., completions per day)?
Generate/revoke keys manually?
View HWID-key mappings?
Export data or view logs (optional/advanced)?
yes Admin can do all that

Is there a way to revoke keys instantly? yes, delete it from the database or add something called Status where we can report active, expired or revoked

is the frontend clean, mobile-friendly, and easy to follow for both users and admins? yes
