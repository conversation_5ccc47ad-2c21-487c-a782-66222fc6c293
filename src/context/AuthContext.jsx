import { createContext, useContext, useState, useCallback, useEffect } from 'react';

const AuthContext = createContext({});

export const AuthProvider = ({ children }) => {
  const [admin, setAdmin] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Validate credentials with backend on mount
  useEffect(() => {
    const validateAdmin = async () => {
      const username = localStorage.getItem('adminUsername');
      const password = localStorage.getItem('adminPassword');
      if (!username || !password) {
        setAdmin(null);
        setLoading(false);
        return;
      }
      try {
        // Make a harmless request to validate credentials
        const response = await fetch('/.netlify/functions/security?action=ip-bans', {
          method: 'GET',
          headers: {
            'x-admin-username': username,
            'x-admin-password': password,
            'Content-Type': 'application/json',
          },
        });
        if (response.ok) {
          // Try to get role from response
          let role = 'admin';
          try {
            const data = await response.json();
            if (data && data.role) role = data.role;
          } catch {}
          setAdmin({ username, role });
        } else {
          localStorage.removeItem('adminUsername');
          localStorage.removeItem('adminPassword');
          setAdmin(null);
        }
      } catch (err) {
        setAdmin(null);
      } finally {
        setLoading(false);
      }
    };
    validateAdmin();
  }, []);

  // Admin login with username and password
  const login = async (username, password) => {
    localStorage.setItem('adminUsername', username);
    localStorage.setItem('adminPassword', password);
    // Validate and get role
    try {
      const response = await fetch('/.netlify/functions/security?action=ip-bans', {
        method: 'GET',
        headers: {
          'x-admin-username': username,
          'x-admin-password': password,
          'Content-Type': 'application/json',
        },
      });
      if (response.ok) {
        let role = 'admin';
        try {
          const data = await response.json();
          if (data && data.role) role = data.role;
        } catch {}
        setAdmin({ username, role });
      } else {
        setAdmin(null);
      }
    } catch {
      setAdmin(null);
    }
  };

  // Logout admin
  const logout = () => {
    localStorage.removeItem('adminUsername');
    localStorage.removeItem('adminPassword');
    setAdmin(null);
    setError(null);
  };

  // Check if admin has specific permission
  const hasPermission = useCallback((requiredPermission) => {
    if (!admin) return false;
    if (admin.role === 'owner') return true; // Owner has all permissions

    // Check role-based permissions
    if (admin.role === 'ml_security') {
      const mlPermissions = ['ml_analysis', 'behavior_monitoring', 'security_events', 'user_behavior_profiles'];
      return mlPermissions.includes(requiredPermission);
    }

    if (admin.role === 'admin') {
      const adminPermissions = ['scripts', 'keys', 'users', 'basic_security'];
      return adminPermissions.includes(requiredPermission);
    }

    // Check explicit permissions
    return admin.permissions?.[requiredPermission] === true;
  }, [admin]);

  // Check if user is authenticated (now relies on admin state)
  const isAuthenticated = !!admin;

  // Get current admin
  const getCurrentAdmin = useCallback(() => {
    return admin;
  }, [admin]);

  return (
    <AuthContext.Provider
      value={{
        admin,
        loading,
        error,
        login,
        logout,
        isAuthenticated,
        isAdmin: !!admin,
        isOwner: admin?.role === 'owner',
        isMLSecurity: admin?.role === 'ml_security',
        isSuperAdmin: admin?.role === 'super_admin',
        hasPermission,
        getCurrentAdmin
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
