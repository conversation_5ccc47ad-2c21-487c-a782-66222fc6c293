import React, { createContext, useState, useEffect, useCallback, useMemo, useContext } from 'react';
import { message } from 'antd';
import clientProtection from '../utils/clientProtection';
import { getUnbreakableFingerprint } from '../utils/deviceFingerprint';
import securityService from '../services/securityService';

const SecurityContext = createContext();

export const SecurityProvider = ({ children }) => {
  const [isSecure, setIsSecure] = useState(false);
  const [fingerprint, setFingerprint] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const runChecks = useCallback(async () => {
    try {
      setLoading(true);
      const fp = await getUnbreakableFingerprint();
      setFingerprint(fp);

            const checks = await clientProtection.runChecks();
            const failedChecks = Object.values(checks).filter(c => c && c.valid === false);

      if (failedChecks.length > 0) {
        console.warn('Security checks failed:', failedChecks);
        message.error('Security checks failed. Some functionality may be limited.');
        setIsSecure(false);
        // Optionally report to backend
        // securityService.reportViolation({ type: 'client-tampering', details: failedChecks });
      } else {
        setIsSecure(true);
      }
    } catch (err) {
      console.error('Error during security checks:', err);
      setError('Failed to initialize security context.');
      setIsSecure(false);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    runChecks();
  }, []);

  const value = useMemo(() => ({
    isSecure,
    fingerprint,
    loading,
    error,
    securityService, // Add securityService to context
    refreshChecks: runChecks,
  }), [isSecure, fingerprint, loading, error, securityService, runChecks]);

  return (
    <SecurityContext.Provider value={value}>
      {children}
    </SecurityContext.Provider>
  );
};

export const useSecurity = () => {
  const context = useContext(SecurityContext);
  if (context === undefined) {
    throw new Error('useSecurity must be used within a SecurityProvider');
  }
  return context;
};

export default SecurityContext;
