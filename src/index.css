@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Light Theme Colors */
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    
    /* Primary Colors */
    --primary: 199 89% 48%;
    --primary-foreground: 0 0% 98%;
    --primary-hover: 199 89% 40%;
    
    /* Secondary Colors */
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    
    /* Muted Colors */
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    
    /* Accent Colors */
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    
    /* Destructive Colors */
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    
    /* Border Colors */
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 199 89% 48%;
    
    /* Border Radius */
    --radius: 0.5rem;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  }
 
  .dark {
    /* Dark Theme Colors */
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    
    /* Primary Colors - Dark */
    --primary: 199 89% 52%;
    --primary-foreground: 0 0% 100%;
    --primary-hover: 199 89% 44%;
    
    /* Secondary Colors - Dark */
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    
    /* Muted Colors - Dark */
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 199 89% 48%;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 199 89% 48%;
    --primary-foreground: 210 40% 98%;
    --secondary: 262 83% 63%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 142 76% 36%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 199 89% 48%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 199 89% 48%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 262 83% 63%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 142 76% 36%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 199 89% 48%;
  }
}

@layer base {
  * {
    border-color: hsl(var(--border));
  }
  
  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-feature-settings: 'rlig' 1, 'calt' 1;
  }
  
  h1, h2, h3, h4, h5, h6 {
    font-family: 'Poppins', sans-serif;
    font-weight: 700;
    letter-spacing: -0.025em;
  }
  
  h1 {
    font-size: 3rem;
    line-height: 1;
  }
  
  h2 {
    font-size: 2.25rem;
    line-height: 2.5rem;
  }
  
  h3 {
    font-size: 1.5rem;
    line-height: 2rem;
  }
  
  @media (min-width: 768px) {
    h1 {
      font-size: 3.75rem;
    }
    
    h2 {
      font-size: 3rem;
      line-height: 1;
    }
    
    h3 {
      font-size: 2.25rem;
      line-height: 2.5rem;
    }
  }
  
  @media (min-width: 1024px) {
    h1 {
      font-size: 4.5rem;
    }
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background-color: hsl(var(--background));
}

::-webkit-scrollbar-thumb {
  background-color: hsla(var(--muted-foreground) / 0.2);
  border-radius: 9999px;
  transition: background-color 0.2s;
}

::-webkit-scrollbar-thumb:hover {
  background-color: hsla(var(--muted-foreground) / 0.3);
}

/* Glass effect */
.glass {
  background: hsla(0, 0%, 100%, 0.05);
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  border: 1px solid hsla(0, 0%, 100%, 0.1);
  box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
}

/* Animations */
.animate-in {
  animation: fadeIn 0.5s ease-out forwards;
}

.animate-up {
  animation: slideUp 0.5s ease-out forwards;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}
@layer base {
    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
        @apply font-bold;
    }

    h1 {
        @apply text-4xl md:text-5xl lg:text-6xl;
    }

    h2 {
        @apply text-3xl md:text-4xl;
    }

    h3 {
        @apply text-2xl md:text-3xl;
    }

    a {
        @apply text-primary hover:text-primary-hover transition-colors;
    }

    button {
        @apply transition-all duration-200;
    }
}

@layer components {
    .container {
        @apply max-w-7xl mx-auto px-4;
    }

    .gradient-text {
        @apply bg-clip-text text-transparent bg-gradient-to-r from-[var(--gradient-start)] to-[var(--gradient-end)];
    }

    .card {
        @apply bg-card rounded-xl p-6 shadow-lg border border-border;
    }

    .btn {
        @apply px-4 py-2 rounded-lg font-medium;
    }

    .btn-primary {
        @apply btn bg-primary hover:bg-primary-hover text-white transition-colors;
    }

    .btn-outline {
        @apply btn border border-primary text-primary hover:bg-primary hover:text-white transition-colors;
    }

    .btn-gradient {
        @apply btn bg-gradient-to-r from-[var(--gradient-start)] to-[var(--gradient-end)] text-white hover:brightness-110;
    }
}
