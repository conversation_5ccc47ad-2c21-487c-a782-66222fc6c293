import React from 'react';
import { motion } from 'framer-motion';
import { FiGithub } from 'react-icons/fi';
import { FaDiscord } from 'react-icons/fa';
import { Button } from './ui/Button';
import { Card } from './ui/Card';
import { cn } from '../lib/utils';
import LazyImage from './ui/LazyImage';

const teamMembers = [
  {
    name: '<PERSON><PERSON>',
    role: 'Founder & Lead Developer',
    description: 'Lead developer and maintainer of Project Madara. Responsible for core script development, maintenance, and project direction. Dedicated to creating high-quality, reliable scripts for the gaming community.',
    avatar: 'https://avatars.githubusercontent.com/u/150212080?v=4',
    socials: [
      { icon: <FiGithub aria-hidden="true" />, url: 'https://github.com/IsThisMe01', label: 'GitHub profile' },
      { icon: <FaDiscord aria-hidden="true" />, url: 'https://discord.gg/your-discord-invite', label: 'Discord profile' }
    ]
  },
  {
    name: '<PERSON><PERSON>',
    role: 'Lead Developer',
    description: 'One of the developers focused on script suggestions and advanced features. Prefers to stay behind the scenes while supporting Project Madara.',
    avatar: null, // No avatar as per preference
    socials: [
      { icon: <FaDiscord aria-hidden="true" />, url: 'https://discord.gg/your-discord-invite', label: 'Discord profile' }
    ]
  },
];

const Team = () => {
  const handleSocialLinkKeyDown = (event, url) => {
    if (event.key === 'Enter' || event.key === 'Space') {
      event.preventDefault();
      window.open(url, '_blank', 'noopener noreferrer');
    }
  };

  return (
    <div className="relative">
      {/* Background effects */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5 dark:opacity-10"></div>
      <motion.div
        className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-r from-blue-400/20 to-purple-400/20 rounded-full blur-xl"
        animate={{
          y: [0, -20, 0],
          x: [0, 10, 0],
        }}
        transition={{
          duration: 6,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />
      <motion.div
        className="absolute bottom-20 right-10 w-24 h-24 bg-gradient-to-r from-purple-400/20 to-pink-400/20 rounded-full blur-xl"
        animate={{
          y: [0, 15, 0],
          x: [0, -15, 0],
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 1
        }}
      />

      <div className="relative z-10 max-w-6xl mx-auto">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
        >
          <motion.div
            className="inline-block px-6 py-3 mb-6 rounded-full bg-gradient-to-r from-blue-500/10 to-purple-500/10 backdrop-blur-sm border border-blue-500/20 text-primary text-sm font-semibold shadow-lg"
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <span className="flex items-center">
              <FiGithub className="mr-2 h-4 w-4" />
              Our Team
            </span>
          </motion.div>

          <motion.h2
            className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 bg-clip-text text-transparent leading-tight"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            Meet the Team
          </motion.h2>
          <motion.p
            className="text-lg text-foreground/80 max-w-2xl mx-auto leading-relaxed"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            Behind Project Madara is a passionate team dedicated to delivering the best scripting solutions for the gaming community.
          </motion.p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
          {teamMembers.map((member, index) => (
            <motion.div
              key={member.name}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.5 + index * 0.1 }}
              whileHover={{ scale: 1.02, y: -5 }}
              className="w-full"
            >
              <div className="h-full p-8 bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl shadow-2xl hover:bg-white/15 transition-all duration-300 flex flex-col group">
                <div className="w-32 h-32 mx-auto mb-6 rounded-full overflow-hidden border-4 border-gradient-to-r from-blue-500/30 to-purple-500/30 shadow-lg bg-gradient-to-r from-blue-500/10 to-purple-500/10 flex items-center justify-center group-hover:scale-105 transition-transform duration-300">
                  {member.avatar ? (
                    <LazyImage
                      src={member.avatar}
                      alt={member.name}
                      className="w-full h-full rounded-full"
                      onError={() => {
                        // Fallback to generated avatar
                        const fallbackSrc = `https://ui-avatars.com/api/?name=${encodeURIComponent(member.name)}&background=random`;
                        return fallbackSrc;
                      }}
                      placeholder={
                        <div className="w-full h-full flex items-center justify-center bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-full">
                          <div className="text-2xl font-bold text-white/60">
                            {member.name.charAt(0)}
                          </div>
                        </div>
                      }
                    />
                  ) : (
                    <div className="text-4xl font-bold bg-gradient-to-r from-blue-500 to-purple-500 bg-clip-text text-transparent">
                      {member.name.charAt(0)}
                    </div>
                  )}
                </div>
                <div className="text-center flex-grow">
                  <h3 className="text-2xl font-bold mb-2 text-foreground">{member.name}</h3>
                  <p className="text-blue-400 text-lg font-medium mb-4">{member.role}</p>
                  <p className="text-foreground/80 mb-6 leading-relaxed">{member.description}</p>
                </div>
                <div className="flex justify-center space-x-4 mt-auto">
                  {member.socials.map((social, socialIndex) => (
                    <motion.a
                      key={socialIndex}
                      href={social.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="h-12 w-12 rounded-xl bg-gradient-to-r from-blue-500/20 to-purple-500/20 backdrop-blur-sm border border-blue-500/30 flex items-center justify-center text-blue-400 hover:from-blue-500/30 hover:to-purple-500/30 hover:border-blue-400/50 transition-all duration-300"
                      aria-label={social.label}
                      tabIndex={0}
                      onKeyDown={(e) => handleSocialLinkKeyDown(e, social.url)}
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      {social.icon}
                    </motion.a>
                  ))}
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Team;
