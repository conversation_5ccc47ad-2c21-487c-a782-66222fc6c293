import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>t<PERSON>riangle, FiAlertCircle, FiExternalLink } from 'react-icons/fi';
import { cn } from '../../lib/utils';

const ExecutorSupport = ({ script }) => {
  // Sample executor data - in a real app, this would come from an API
  const executors = {
    fullySupported: [
      { name: 'Arceus X', status: 'popular' },
      { name: 'AWP', status: 'free' },
      { name: 'Codex', status: 'popular' },
      { name: 'Delta', status: 'popular' },
      { name: 'Fluxus', status: 'free' },
      { name: 'Hydrogen', status: 'stable' },
      { name: 'Sirhurt', status: 'popular' },
      { name: 'Synapse Z', status: 'popular' },
      { name: 'Vega X', status: 'free' },
      { name: 'Volcano', status: 'free' },
      { name: 'Wave', status: 'free' },
    ],
    limitedSupport: [
      { name: 'JJSploit', status: 'free', note: 'Missing advanced functions' },
      { name: '<PERSON><PERSON>', status: 'free', note: 'Limited API support' },
      { name: 'Xeno', status: 'stable', note: 'Unstable execution' },
    ],
    variableSupport: [
      { name: 'Swift', status: 'free', note: 'Works for some users only' },
      { name: 'Visual', status: 'niche', note: 'Inconsistent performance' },
    ]
  };

  const getStatusBadge = (status) => {
    const statusClasses = {
      popular: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
      free: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
      stable: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200',
      niche: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
    };

    const statusText = {
      popular: 'Popular',
      free: 'Free',
      stable: 'Stable',
      niche: 'Niche'
    };

    return (
      <span className={cn("text-xs px-2 py-1 rounded-full", statusClasses[status] || 'bg-gray-100 dark:bg-gray-700')}>
        {statusText[status] || status}
      </span>
    );
  };

  const handleLinkKeyDown = (event, url) => {
    if (event.key === 'Enter' || event.key === 'Space') {
      event.preventDefault();
      if (url.startsWith('/')) {
        window.location.href = url;
      } else {
        window.open(url, '_blank', 'noopener noreferrer');
      }
    }
  };

  return (
    <div className={cn("space-y-8")}>
      <div className={cn("")}>
        <h3 className={cn("text-lg font-medium mb-4")}>Executor Compatibility</h3>
        <p className={cn("text-muted-foreground mb-6")}>
          Find out which executors work best with our scripts. We test all our scripts across multiple platforms to ensure the best experience.
        </p>
      </div>

      {/* Fully Supported Executors */}
      <div className={cn("space-y-4")}>
        <div className={cn("flex items-center space-x-2")}>
          <FiCheck className={cn("h-5 w-5 text-green-500")} aria-hidden="true" />
          <h4 className={cn("font-medium")}>Fully Supported</h4>
        </div>
        <p className={cn("text-sm text-muted-foreground")}>
          These executors work perfectly with all our scripts
        </p>
        <div className={cn("grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3 mt-2")}>
          {executors.fullySupported.map((executor, index) => (
            <div key={index} className={cn("flex items-center justify-between p-3 bg-muted/30 rounded-lg")}>
              <span className={cn("")}>{executor.name}</span>
              {getStatusBadge(executor.status)}
            </div>
          ))}
        </div>
        <p className={cn("text-xs text-muted-foreground mt-1")}>
          Total executors: {executors.fullySupported.length}
        </p>
      </div>

      {/* Limited Support Executors */}
      <div className={cn("space-y-4 pt-4 border-t border-border")}>
        <div className={cn("flex items-center space-x-2")}>
          <FiAlertTriangle className={cn("h-5 w-5 text-yellow-500")} aria-hidden="true" />
          <h4 className={cn("font-medium")}>Limited Support</h4>
        </div>
        <p className={cn("text-sm text-muted-foreground")}>
          Some functions may not work properly
        </p>
        <div className={cn("space-y-3")}>
          {executors.limitedSupport.map((executor, index) => (
            <div key={index} className={cn("p-3 bg-muted/20 rounded-lg")}>
              <div className={cn("flex items-center justify-between mb-1")}>
                <span className={cn("")}>{executor.name}</span>
                {getStatusBadge(executor.status)}
              </div>
              <p className={cn("text-xs text-muted-foreground")}>{executor.note}</p>
            </div>
          ))}
        </div>
        <p className={cn("text-xs text-muted-foreground mt-1")}>
          Total executors: {executors.limitedSupport.length}
        </p>
      </div>

      {/* Variable Support Executors */}
      <div className={cn("space-y-4 pt-4 border-t border-border")}>
        <div className={cn("flex items-center space-x-2")}>
          <FiAlertCircle className={cn("h-5 w-5 text-orange-500")} aria-hidden="true" />
          <h4 className={cn("font-medium")}>Variable Compatibility</h4>
        </div>
        <p className={cn("text-sm text-muted-foreground")}>
          Results may vary depending on device and setup
        </p>
        <div className={cn("space-y-3")}>
          {executors.variableSupport.map((executor, index) => (
            <div key={index} className={cn("p-3 bg-muted/10 rounded-lg")}>
              <div className={cn("flex items-center justify-between mb-1")}>
                <span className={cn("")}>{executor.name}</span>
                {getStatusBadge(executor.status)}
              </div>
              <p className={cn("text-xs text-muted-foreground")}>{executor.note}</p>
            </div>
          ))}
        </div>
        <p className={cn("text-xs text-muted-foreground mt-1")}>
          Total executors: {executors.variableSupport.length}
        </p>
      </div>

      <div className={cn("pt-4 border-t border-border")}>
        <p className={cn("text-sm text-muted-foreground")}>
          Having issues with an executor? Check our <a 
            href="/faq" 
            className={cn("text-primary hover:underline")}
            tabIndex={0}
            onKeyDown={(e) => handleLinkKeyDown(e, '/faq')}
            aria-label="Read our FAQ page"
          >FAQ</a> or join our <a 
            href="https://discord.gg/scriptmaster" 
            target="_blank" 
            rel="noopener noreferrer" 
            className={cn("text-primary hover:underline")}
            tabIndex={0}
            onKeyDown={(e) => handleLinkKeyDown(e, 'https://discord.gg/scriptmaster')}
            aria-label="Join our Discord community"
          >Discord <FiExternalLink className={cn("inline h-3 w-3")} aria-hidden="true" /></a> for support.
        </p>
      </div>
    </div>
  );
};

export default ExecutorSupport;
