import { useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuthContext } from '@/hooks/useAuth';
import { Spinner } from '@radix-ui/themes';
import { cn } from '../lib/utils';

/**
 * ProtectedRoute component that redirects to login if user is not authenticated
 * or doesn't have required permissions.
 * 
 * @param {Object} props
 * @param {React.ReactNode} props.children - Child components to render if authenticated
 * @param {string|string[]} [props.requiredPermission] - Required permission(s) to access the route
 * @param {boolean} [props.requireAdmin] - Whether admin role is required
 * @param {string} [props.redirectTo] - Path to redirect to if not authenticated (default: '/login')
 * @returns {JSX.Element}
 */
export function ProtectedRoute({ 
  children, 
  requiredPermission, 
  requireAdmin = false, 
  redirectTo = '/login' 
}) {
  const { isAuthenticated, admin, isLoading, hasPermission } = useAuthContext();
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    if (!isLoading) {
      // If not authenticated, redirect to login
      if (!isAuthenticated) {
        // Store the current location to redirect back after login
        const redirectPath = `${redirectTo}?redirect=${encodeURIComponent(location.pathname + location.search)}`;
        navigate(redirectPath, { replace: true });
        return;
      }

      // If admin role is required but user is not an admin
      if (requireAdmin && admin?.role !== 'admin') {
        navigate('/unauthorized', { replace: true });
        return;
      }

      // If specific permission is required
      if (requiredPermission) {
        const permissions = Array.isArray(requiredPermission) 
          ? requiredPermission 
          : [requiredPermission];
        
        const hasAllPermissions = permissions.every(permission => 
          hasPermission(permission)
        );
        
        if (!hasAllPermissions) {
          navigate('/unauthorized', { replace: true });
          return;
        }
      }
    }
  }, [
    isAuthenticated, 
    isLoading, 
    admin, 
    hasPermission, 
    requiredPermission, 
    requireAdmin, 
    navigate, 
    location,
    redirectTo
  ]);

  // Show loading spinner while checking auth state
  if (isLoading) {
    return (
      <div className={cn("flex items-center justify-center min-h-screen")}>
        <Spinner size="3" />
        <span className={cn("ml-2")} aria-live="polite">Verifying access...</span>
      </div>
    );
  }

  // If authenticated and has required permissions, render children
  return isAuthenticated ? children : null;
}

/**
 * Higher-order component for protecting routes with authentication and permissions
 * 
 * @param {React.ComponentType} Component - The component to protect
 * @param {Object} [options] - Protection options
 * @param {string|string[]} [options.requiredPermission] - Required permission(s)
 * @param {boolean} [options.requireAdmin] - Whether admin role is required
 * @param {string} [options.redirectTo] - Redirect path if not authenticated
 * @returns {React.ComponentType} Protected component
 */
export function withProtectedRoute(Component, options = {}) {
  return function ProtectedComponent(props) {
    return (
      <ProtectedRoute {...options}>
        <Component {...props} />
      </ProtectedRoute>
    );
  };
}
