import { <PERSON><PERSON>he<PERSON>, <PERSON><PERSON><PERSON>tTriangle, FiHelpCircle } from 'react-icons/fi';
import { motion } from 'framer-motion';
import { Badge } from './ui/Badge';
import { cn } from '../lib/utils';

const executorCategories = [
  {
    title: 'Fully Supported',
    description: 'These executors work perfectly with all our scripts',
    icon: <FiCheck className={cn("h-5 w-5 text-green-500")} aria-hidden="true" />,
    executors: [
      { name: 'Arceus X', status: 'Popular' },
      { name: 'AWP', status: 'Free' },
      { name: 'Codex', status: 'Popular' },
      { name: 'Delta', status: 'Popular' },
      { name: 'Fluxus', status: 'Free' },
      { name: 'Hydrogen', status: 'Stable' },
      { name: 'Sirhurt', status: 'Popular' },
      { name: 'Synapse Z', status: 'Popular' },
      { name: 'Vega X', status: 'Free' },
      { name: 'Volcano', status: 'Free' },
      { name: 'Wave', status: 'Free' }
    ]
  },
  {
    title: 'Limited Support',
    description: 'Some functions may not work properly',
    icon: <FiAlertTriangle className={cn("h-5 w-5 text-amber-500")} aria-hidden="true" />,
    executors: [
      { 
        name: 'JJSploit', 
        status: 'Free',
        note: 'Missing advanced functions'
      },
      { 
        name: 'Solara', 
        status: 'Free',
        note: 'Limited API support'
      },
      { 
        name: 'Xeno', 
        status: 'Stable',
        note: 'Unstable execution'
      }
    ]
  },
  {
    title: 'Variable Compatibility',
    description: 'Results may vary depending on device and setup',
    icon: <FiHelpCircle className={cn("h-5 w-5 text-blue-500")} aria-hidden="true" />,
    executors: [
      { 
        name: 'Swift', 
        status: 'Free',
        note: 'Works for some users only'
      },
      { 
        name: 'Visual', 
        status: 'Niche',
        note: 'Inconsistent performance'
      }
    ]
  }
];

const statusColors = {
  'Popular': 'bg-gradient-to-r from-purple-500/20 to-pink-500/20 text-purple-300 border border-purple-500/30 backdrop-blur-sm',
  'Free': 'bg-gradient-to-r from-green-500/20 to-emerald-500/20 text-green-300 border border-green-500/30 backdrop-blur-sm',
  'Stable': 'bg-gradient-to-r from-blue-500/20 to-cyan-500/20 text-blue-300 border border-blue-500/30 backdrop-blur-sm',
  'Niche': 'bg-gradient-to-r from-amber-500/20 to-orange-500/20 text-amber-300 border border-amber-500/30 backdrop-blur-sm'
};

const ExecutorsList = () => {
  return (
    <section className="relative overflow-hidden">
      {/* Enhanced Background Effects */}
      <div className="absolute inset-0">
        <motion.div
          className="absolute top-20 left-1/4 w-96 h-96 bg-gradient-to-r from-blue-500/5 to-purple-500/5 rounded-full blur-3xl"
          animate={{
            y: [0, -30, 0],
            x: [0, 20, 0],
            scale: [1, 1.1, 1],
          }}
          transition={{
            duration: 15,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        <motion.div
          className="absolute bottom-20 right-1/4 w-80 h-80 bg-gradient-to-r from-purple-500/5 to-pink-500/5 rounded-full blur-3xl"
          animate={{
            y: [0, 25, 0],
            x: [0, -15, 0],
            scale: [1, 0.9, 1],
          }}
          transition={{
            duration: 18,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 3
          }}
        />
      </div>

      <div className="relative z-10 container mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          className="grid gap-8 lg:grid-cols-3"
          initial={{ opacity: 0, y: 40 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, ease: [0.25, 0.46, 0.45, 0.94] }}
        >
          {executorCategories.map((category, index) => (
            <motion.div
              key={index}
              className="group relative"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: index * 0.2 }}
              whileHover={{ y: -8 }}
            >
              {/* Enhanced Glassmorphism Card */}
              <div
                className="relative overflow-hidden rounded-2xl backdrop-blur-xl border border-white/10 shadow-2xl"
                style={{
                  background: `
                    linear-gradient(135deg,
                      rgba(255, 255, 255, 0.1) 0%,
                      rgba(255, 255, 255, 0.05) 50%,
                      rgba(255, 255, 255, 0.02) 100%
                    )
                  `,
                }}
              >
                {/* Animated Border */}
                <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-pink-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500 blur-sm"></div>

                {/* Header Section */}
                <div className="relative p-6 border-b border-white/10">
                  <motion.div
                    className="flex items-center gap-4 mb-3"
                    whileHover={{ scale: 1.02 }}
                    transition={{ type: "spring", stiffness: 400, damping: 17 }}
                  >
                    <motion.div
                      className="p-2 rounded-xl bg-white/5 backdrop-blur-sm border border-white/10"
                      whileHover={{ scale: 1.1, rotate: 5 }}
                      transition={{ type: "spring", stiffness: 400, damping: 17 }}
                    >
                      {category.icon}
                    </motion.div>
                    <h3 className="text-xl font-bold text-slate-200">{category.title}</h3>
                  </motion.div>
                  <p className="text-sm text-slate-300/80 leading-relaxed">{category.description}</p>
                </div>

                {/* Executors List */}
                <div className="relative p-6">
                  <div className="space-y-3">
                    {category.executors.map((executor, idx) => (
                      <motion.div
                        key={idx}
                        className="group/item relative p-3 rounded-xl bg-white/5 backdrop-blur-sm border border-white/10 hover:bg-white/10 hover:border-white/20 transition-all duration-300"
                        initial={{ opacity: 0, x: -20 }}
                        whileInView={{ opacity: 1, x: 0 }}
                        viewport={{ once: true }}
                        transition={{ duration: 0.4, delay: idx * 0.1 }}
                        whileHover={{ scale: 1.02, x: 4 }}
                      >
                        <div className="flex justify-between items-center">
                          <span className="font-medium text-slate-200 group-hover/item:text-blue-300 transition-colors duration-300">
                            {executor.name}
                          </span>
                          <motion.div
                            whileHover={{ scale: 1.05 }}
                            transition={{ type: "spring", stiffness: 400, damping: 17 }}
                          >
                            <Badge
                              className={cn(
                                statusColors[executor.status] || 'bg-gray-500/20 text-gray-300 border border-gray-500/30',
                                "text-xs font-medium px-3 py-1 rounded-lg"
                              )}
                            >
                              {executor.status}
                            </Badge>
                          </motion.div>
                        </div>
                      </motion.div>
                    ))}
                  </div>

                  {/* Notes Section */}
                  {category.executors.some(e => e.note) && (
                    <motion.div
                      className="mt-6 pt-4 border-t border-white/10"
                      initial={{ opacity: 0, y: 10 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      viewport={{ once: true }}
                      transition={{ duration: 0.5, delay: 0.3 }}
                    >
                      <h4 className="text-sm font-semibold text-slate-300 mb-3">Important Notes:</h4>
                      <div className="space-y-2">
                        {category.executors
                          .filter(e => e.note)
                          .map((executor, idx) => (
                            <motion.div
                              key={idx}
                              className="p-2 rounded-lg bg-white/5 border border-white/10"
                              initial={{ opacity: 0, x: -10 }}
                              whileInView={{ opacity: 1, x: 0 }}
                              viewport={{ once: true }}
                              transition={{ duration: 0.4, delay: idx * 0.1 }}
                            >
                              <div className="flex items-start gap-2 text-xs">
                                <span className="font-medium text-slate-300">{executor.name}:</span>
                                <span className="text-slate-400/80">{executor.note}</span>
                              </div>
                            </motion.div>
                          ))
                        }
                      </div>
                    </motion.div>
                  )}

                  {/* Footer Stats */}
                  <motion.div
                    className="mt-6 pt-4 border-t border-white/10"
                    initial={{ opacity: 0, y: 10 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.5, delay: 0.4 }}
                  >
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-slate-300">
                        Total Executors: {category.executors.length}
                      </span>
                      <motion.div
                        className="px-3 py-1 rounded-full bg-gradient-to-r from-blue-500/20 to-purple-500/20 border border-blue-500/30"
                        whileHover={{ scale: 1.05 }}
                        transition={{ type: "spring", stiffness: 400, damping: 17 }}
                      >
                        <span className="text-xs font-medium text-blue-300">
                          {Math.round((category.executors.length / executorCategories.reduce((acc, cat) => acc + cat.executors.length, 0)) * 100)}%
                        </span>
                      </motion.div>
                    </div>
                  </motion.div>
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
};

export default ExecutorsList;
