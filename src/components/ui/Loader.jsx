import React from 'react';

const Loader = () => (
  <div className="flex flex-col items-center justify-center min-h-[40vh]">
    <div className="relative flex items-center justify-center">
      <span className="block w-20 h-20 rounded-full bg-gradient-to-tr from-blue-500 via-purple-500 to-pink-500 animate-spin-slow" style={{
        maskImage: 'radial-gradient(circle at 50% 50%, white 60%, transparent 61%)',
        WebkitMaskImage: 'radial-gradient(circle at 50% 50%, white 60%, transparent 61%)',
      }} />
      <span className="absolute text-2xl font-bold text-primary drop-shadow">⚡️</span>
    </div>
    <div className="mt-6 text-lg font-medium text-foreground/80 tracking-wide animate-pulse">Loading…</div>
  </div>
);

export default Loader; 