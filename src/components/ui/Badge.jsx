import React from 'react';
import { cva } from 'class-variance-authority';

const badgeVariants = cva(
  'inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',
  {
    variants: {
      variant: {
        default:
          'border-transparent bg-primary/10 text-primary hover:bg-primary/20',
        secondary:
          'border-transparent bg-secondary/10 text-secondary-foreground hover:bg-secondary/20',
        destructive:
          'border-transparent bg-destructive/10 text-destructive hover:bg-destructive/20',
        outline: 'text-foreground',
        success:
          'border-transparent bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300',
        warning:
          'border-transparent bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300',
        info: 'border-transparent bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300',
        premium:
          'bg-gradient-to-r from-purple-500 to-pink-500 text-white border-0',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  }
);

const Badge = ({
  className,
  variant,
  children,
  icon: Icon,
  iconPosition = 'left',
  ariaLabel,
  ...props
}) => {
  return (
    <div
      className={badgeVariants({ variant, className })}
      aria-label={ariaLabel}
      {...props}>
      {Icon && iconPosition === 'left' && (
        <Icon className="mr-1 h-3 w-3" />
      )}
      {children}
      {Icon && iconPosition === 'right' && (
        <Icon className="ml-1 h-3 w-3" />
      )}
    </div>
  );
};

export { Badge, badgeVariants };
