import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '../../lib/utils';

const CardComponent = ({
  children,
  className = '',
  hoverEffect = 'subtle-shadow',
  variant = 'default',
  ...props
}) => {
  const baseStyles = 'rounded-sm border border-gray-800 bg-card text-card-foreground shadow-none transition-all duration-200';
  
  const variants = {
    default: 'bg-card',
    glass: 'glass border border-white/10',
    primary: 'bg-primary/5 border-primary/20',
    secondary: 'bg-secondary/5 border-secondary/20',
  };

  const hoverEffects = {
    'subtle-shadow': 'hover:shadow-lg hover:shadow-gray-900/50',
    'lift-shadow': 'hover:-translate-y-3 group-hover:shadow-primary-glow',
    scale: 'hover:scale-[1.02]',
    shadow: 'hover:shadow-md',
    lift: 'hover:-translate-y-1',
    none: '',
  };

  return (
    <motion.div
      whileHover={hoverEffect !== 'none' ? { y: -2, boxShadow: '0 8px 25px -5px rgba(0,0,0,0.4)' } : {}}
      className={cn(baseStyles, variants[variant], hoverEffects[hoverEffect], className)}
      {...props}
    >
      {children}
    </motion.div>
  );
};

const CardHeader = ({ className = '', ...props }) => (
  <div
    className={cn('flex flex-col space-y-1.5 p-4', className)}
    {...props}
  />
);

const CardTitle = ({ className = '', ...props }) => (
  <h3
    className={cn('text-lg font-semibold leading-none tracking-tight text-white', className)}
    {...props}
  />
);

const CardDescription = ({ className = '', ...props }) => (
  <p
    className={cn('text-sm text-gray-400', className)}
    {...props}
  />
);

const CardContent = ({ className = '', ...props }) => (
  <div className={cn('p-4 pt-0', className)} {...props} />
);

const CardFooter = ({ className = '', ...props }) => (
  <div
    className={cn('flex items-center p-4 pt-0', className)}
    {...props}
  />
);

export const Card = Object.assign(CardComponent, {
  Header: CardHeader,
  Title: CardTitle,
  Description: CardDescription,
  Content: CardContent,
  Footer: CardFooter
});
