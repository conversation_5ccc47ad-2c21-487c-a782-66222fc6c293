import React from 'react';
import { motion } from 'framer-motion';
import { Slot } from '@radix-ui/react-slot';
import { cn } from '../../lib/utils';

const Button = ({
  children,
  variant = 'primary',
  size = 'md',
  className = '',
  icon: Icon,
  iconPosition = 'left',
  isLoading = false,
  asChild = false,
  ariaLabel,
  ...props
}) => {
  const Comp = asChild ? Slot : motion.button;

  // Base button styles
  const baseStyles = 'inline-flex items-center justify-center font-medium rounded-lg transition-all focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none';
  
  // Variant styles
  const variants = {
    primary: 'bg-gradient-to-br from-blue-600 to-purple-700 text-white shadow-lg hover:shadow-xl hover:-translate-y-0.5 transition-all duration-300 focus:ring-blue-500/50',
    secondary: 'bg-gray-700 text-gray-100 hover:bg-gray-600 focus:ring-gray-500/50',
    outline: 'border border-primary/50 text-primary bg-transparent hover:bg-primary/10 hover:text-primary focus:ring-primary/50',
    ghost: 'hover:bg-accent hover:text-accent-foreground',
    link: 'text-primary underline-offset-4 hover:underline',
  };

  // Size styles
  const sizes = {
    sm: 'h-9 px-3 text-sm',
    md: 'h-10 py-2 px-4 text-base',
    lg: 'h-12 px-6 text-lg',
    icon: 'h-10 w-10 p-0',
  };

  // Icon sizes
  const iconSizes = {
    sm: 'h-5 w-5',
    md: 'h-6 w-6',
    lg: 'h-7 w-7',
  };

  // Determine if this is an icon-only button
  const hasChildren = React.Children.count(children) > 0;
  const isIconOnly = !hasChildren && Icon;
  const sizeClass = isIconOnly ? 'icon' : size;
  
  // Get icon size based on button size
  const iconSize = size === 'sm' ? 'sm' : size === 'lg' ? 'lg' : 'md';
  const iconClassName = cn(
    iconSizes[iconSize],
    hasChildren && iconPosition === 'left' && 'mr-2',
    hasChildren && iconPosition === 'right' && 'ml-2'
  );

  // Handle keyboard interaction for accessibility
  const handleKeyDown = (event) => {
    if ((event.key === 'Enter' || event.key === 'Space') && props.onClick) {
      event.preventDefault(); // Prevent default scroll behavior for Space key
      props.onClick(event);
    }
  };

  // Determine aria-label
  const finalAriaLabel = ariaLabel || (typeof children === 'string' ? String(children) : undefined);

  // Render the button
  if (asChild) {
    return (
      <Comp
        tabIndex={0}
        aria-label={finalAriaLabel}
        onKeyDown={handleKeyDown}
        {...props}
      >
        {isLoading ? (
          <svg
            className={cn('animate-spin', iconSizes[iconSize])}
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            />
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            />
          </svg>
        ) : (
          <>
            {Icon && iconPosition === 'left' && (
              <span className={iconClassName}>
                <Icon />
              </span>
            )}
            {children}
            {Icon && iconPosition === 'right' && (
              <span className={iconClassName}>
                <Icon />
              </span>
            )}
          </>
        )}
      </Comp>
    );
  }
  return (
    <Comp
      whileTap={{ scale: 0.98 }}
      className={cn(baseStyles, variants[variant], sizes[sizeClass], className)}
      disabled={isLoading}
      tabIndex={0}
      aria-label={finalAriaLabel}
      onKeyDown={handleKeyDown}
      {...props}
    >
      {isLoading ? (
        <svg
          className={cn('animate-spin', iconSizes[iconSize])}
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle
            className="opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            strokeWidth="4"
          />
          <path
            className="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          />
        </svg>
      ) : (
        <>
          {Icon && iconPosition === 'left' && (
            <span className={iconClassName}>
              <Icon />
            </span>
          )}
          {children}
          {Icon && iconPosition === 'right' && (
            <span className={iconClassName}>
              <Icon />
            </span>
          )}
        </>
      )}
    </Comp>
  );
};

export { Button };
