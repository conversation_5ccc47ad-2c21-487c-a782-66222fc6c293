import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

/**
 * LazyMotion component that only applies animations when the element is in view
 * This helps improve performance by reducing the number of active animations
 */
const LazyMotion = ({ 
  children, 
  threshold = 0.1, 
  rootMargin = '50px',
  triggerOnce = true,
  fallback = null,
  ...motionProps 
}) => {
  const [isInView, setIsInView] = useState(false);
  const [hasTriggered, setHasTriggered] = useState(false);
  const ref = useRef(null);
  const observerRef = useRef(null);

  useEffect(() => {
    const currentRef = ref.current;
    
    if (!currentRef) return;

    // If triggerOnce is true and already triggered, don't observe
    if (triggerOnce && hasTriggered) return;

    observerRef.current = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true);
          setHasTriggered(true);
          
          if (triggerOnce) {
            observerRef.current?.disconnect();
          }
        } else if (!triggerOnce) {
          setIsInView(false);
        }
      },
      {
        threshold,
        rootMargin
      }
    );

    observerRef.current.observe(currentRef);

    return () => {
      observerRef.current?.disconnect();
    };
  }, [threshold, rootMargin, triggerOnce, hasTriggered]);

  // If not in view and we have a fallback, show it
  if (!isInView && fallback) {
    return (
      <div ref={ref}>
        {fallback}
      </div>
    );
  }

  // If not in view and no fallback, show static content
  if (!isInView) {
    return (
      <div ref={ref}>
        {children}
      </div>
    );
  }

  // If in view, show animated content
  return (
    <motion.div ref={ref} {...motionProps}>
      {children}
    </motion.div>
  );
};

/**
 * LazyStagger component for staggered animations that only trigger when in view
 */
const LazyStagger = ({ 
  children, 
  staggerDelay = 0.1,
  threshold = 0.1,
  rootMargin = '50px',
  containerProps = {},
  itemProps = {},
  ...props 
}) => {
  const [isInView, setIsInView] = useState(false);
  const ref = useRef(null);
  const observerRef = useRef(null);

  useEffect(() => {
    const currentRef = ref.current;
    
    if (!currentRef) return;

    observerRef.current = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true);
          observerRef.current?.disconnect();
        }
      },
      {
        threshold,
        rootMargin
      }
    );

    observerRef.current.observe(currentRef);

    return () => {
      observerRef.current?.disconnect();
    };
  }, [threshold, rootMargin]);

  if (!isInView) {
    return (
      <div ref={ref} {...props}>
        {children}
      </div>
    );
  }

  return (
    <motion.div
      ref={ref}
      initial="initial"
      animate="animate"
      variants={{
        initial: {},
        animate: {
          transition: {
            staggerChildren: staggerDelay
          }
        }
      }}
      {...containerProps}
      {...props}
    >
      {React.Children.map(children, (child, index) => (
        <motion.div
          key={index}
          variants={{
            initial: {
              opacity: 0,
              y: 20
            },
            animate: {
              opacity: 1,
              y: 0,
              transition: {
                duration: 0.5,
                ease: [0.25, 0.46, 0.45, 0.94]
              }
            }
          }}
          {...itemProps}
        >
          {child}
        </motion.div>
      ))}
    </motion.div>
  );
};

/**
 * LazyParallax component for parallax effects that only activate when in view
 */
const LazyParallax = ({ 
  children, 
  offset = 50,
  threshold = 0.1,
  rootMargin = '100px',
  ...props 
}) => {
  const [isInView, setIsInView] = useState(false);
  const [scrollY, setScrollY] = useState(0);
  const ref = useRef(null);
  const observerRef = useRef(null);

  useEffect(() => {
    const currentRef = ref.current;
    
    if (!currentRef) return;

    observerRef.current = new IntersectionObserver(
      ([entry]) => {
        setIsInView(entry.isIntersecting);
      },
      {
        threshold,
        rootMargin
      }
    );

    observerRef.current.observe(currentRef);

    return () => {
      observerRef.current?.disconnect();
    };
  }, [threshold, rootMargin]);

  useEffect(() => {
    if (!isInView) return;

    const handleScroll = () => {
      setScrollY(window.scrollY);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, [isInView]);

  const transform = isInView ? `translateY(${scrollY * offset * 0.01}px)` : 'none';

  return (
    <div
      ref={ref}
      style={{ transform }}
      {...props}
    >
      {children}
    </div>
  );
};

/**
 * LazyReveal component for reveal animations on scroll
 */
const LazyReveal = ({ 
  children, 
  direction = 'up',
  distance = 50,
  duration = 0.6,
  delay = 0,
  threshold = 0.1,
  rootMargin = '50px',
  ...props 
}) => {
  const [isInView, setIsInView] = useState(false);
  const ref = useRef(null);
  const observerRef = useRef(null);

  useEffect(() => {
    const currentRef = ref.current;
    
    if (!currentRef) return;

    observerRef.current = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true);
          observerRef.current?.disconnect();
        }
      },
      {
        threshold,
        rootMargin
      }
    );

    observerRef.current.observe(currentRef);

    return () => {
      observerRef.current?.disconnect();
    };
  }, [threshold, rootMargin]);

  const getInitialTransform = () => {
    switch (direction) {
      case 'up':
        return { y: distance, opacity: 0 };
      case 'down':
        return { y: -distance, opacity: 0 };
      case 'left':
        return { x: distance, opacity: 0 };
      case 'right':
        return { x: -distance, opacity: 0 };
      default:
        return { y: distance, opacity: 0 };
    }
  };

  const getAnimateTransform = () => {
    return { x: 0, y: 0, opacity: 1 };
  };

  if (!isInView) {
    return (
      <div ref={ref} style={{ opacity: 0 }} {...props}>
        {children}
      </div>
    );
  }

  return (
    <motion.div
      ref={ref}
      initial={getInitialTransform()}
      animate={getAnimateTransform()}
      transition={{
        duration,
        delay,
        ease: [0.25, 0.46, 0.45, 0.94]
      }}
      {...props}
    >
      {children}
    </motion.div>
  );
};

export {
  LazyMotion,
  LazyStagger,
  LazyParallax,
  LazyReveal
};

export default LazyMotion;
