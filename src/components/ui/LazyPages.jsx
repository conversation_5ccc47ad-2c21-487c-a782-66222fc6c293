import React, { Suspense } from 'react';
import { motion } from 'framer-motion';
import { FiLoader } from 'react-icons/fi';
import { CardSkeleton } from './LoadingSkeleton';

// Lazy load non-critical pages
const Scripts = React.lazy(() => import('../../pages/Scripts'));
const ScriptDetail = React.lazy(() => import('../../pages/ScriptDetail'));
const Contact = React.lazy(() => import('../../pages/Contact'));
const ScriptRequest = React.lazy(() => import('../../pages/ScriptRequest'));
const RequestStatus = React.lazy(() => import('../../pages/RequestStatus'));
const FAQ = React.lazy(() => import('../../pages/FAQ'));
const Terms = React.lazy(() => import('../../pages/Terms'));
const Privacy = React.lazy(() => import('../../pages/Privacy'));
const CookiePolicy = React.lazy(() => import('../../pages/CookiePolicy'));
const LoginPage = React.lazy(() => import('../../pages/LoginPage'));
const NotFound = React.lazy(() => import('../../pages/NotFound'));

// Generic page loading component
const PageLoading = ({ title = 'Loading Page' }) => (
  <div className="min-h-screen relative overflow-hidden bg-gradient-to-br from-slate-900 via-blue-900 to-purple-900">
    {/* Floating orbs background */}
    <div className="absolute inset-0 overflow-hidden">
      {Array.from({ length: 6 }).map((_, i) => (
        <motion.div
          key={i}
          className="absolute rounded-full bg-gradient-to-r from-blue-400/20 to-purple-400/20 backdrop-blur-sm"
          style={{
            width: Math.random() * 300 + 100,
            height: Math.random() * 300 + 100,
            left: `${Math.random() * 100}%`,
            top: `${Math.random() * 100}%`,
          }}
          animate={{
            x: [0, Math.random() * 100 - 50],
            y: [0, Math.random() * 100 - 50],
            scale: [1, 1.1, 1],
          }}
          transition={{
            duration: Math.random() * 10 + 10,
            repeat: Infinity,
            repeatType: "reverse",
            ease: "easeInOut",
          }}
        />
      ))}
    </div>
    
    <div className="container mx-auto px-4 py-8 relative z-10">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="text-center"
      >
        <div className="flex items-center justify-center mb-6">
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
            className="mr-3"
          >
            <FiLoader className="h-8 w-8 text-blue-400" />
          </motion.div>
          <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
            {title}
          </h1>
        </div>
        <p className="text-white/80 mb-8">Please wait while we load the content...</p>
        
        {/* Loading skeleton */}
        <div className="max-w-4xl mx-auto space-y-6">
          <CardSkeleton className="h-32" showImage={false} showBadge={false} />
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {Array.from({ length: 3 }).map((_, index) => (
              <CardSkeleton 
                key={index}
                className="h-48"
                showImage={true}
                showBadge={true}
              />
            ))}
          </div>
        </div>
      </motion.div>
    </div>
  </div>
);

// Error boundary for lazy pages
class LazyPageErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    console.error('Lazy page loading error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-900 to-blue-900">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="text-center p-8 bg-white/10 backdrop-blur-md rounded-2xl border border-white/20"
          >
            <h2 className="text-2xl font-bold text-white mb-4">
              Failed to Load Page
            </h2>
            <p className="text-white/80 mb-6">
              Something went wrong while loading this page.
            </p>
            <button
              onClick={() => window.location.reload()}
              className="px-6 py-3 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors"
            >
              Refresh Page
            </button>
          </motion.div>
        </div>
      );
    }

    return this.props.children;
  }
}

// Lazy page wrapper component
const LazyPageWrapper = ({ children, title }) => (
  <LazyPageErrorBoundary>
    <Suspense fallback={<PageLoading title={title} />}>
      {children}
    </Suspense>
  </LazyPageErrorBoundary>
);

// Export lazy page components
export const LazyScripts = (props) => (
  <LazyPageWrapper title="Loading Scripts">
    <Scripts {...props} />
  </LazyPageWrapper>
);

export const LazyScriptDetail = (props) => (
  <LazyPageWrapper title="Loading Script Details">
    <ScriptDetail {...props} />
  </LazyPageWrapper>
);

export const LazyContact = (props) => (
  <LazyPageWrapper title="Loading Contact">
    <Contact {...props} />
  </LazyPageWrapper>
);

export const LazyScriptRequest = (props) => (
  <LazyPageWrapper title="Loading Script Request">
    <ScriptRequest {...props} />
  </LazyPageWrapper>
);

export const LazyRequestStatus = (props) => (
  <LazyPageWrapper title="Loading Request Status">
    <RequestStatus {...props} />
  </LazyPageWrapper>
);

export const LazyFAQ = (props) => (
  <LazyPageWrapper title="Loading FAQ">
    <FAQ {...props} />
  </LazyPageWrapper>
);

export const LazyTerms = (props) => (
  <LazyPageWrapper title="Loading Terms">
    <Terms {...props} />
  </LazyPageWrapper>
);

export const LazyPrivacy = (props) => (
  <LazyPageWrapper title="Loading Privacy Policy">
    <Privacy {...props} />
  </LazyPageWrapper>
);

export const LazyCookiePolicy = (props) => (
  <LazyPageWrapper title="Loading Cookie Policy">
    <CookiePolicy {...props} />
  </LazyPageWrapper>
);

export const LazyLoginPage = (props) => (
  <LazyPageWrapper title="Loading Login">
    <LoginPage {...props} />
  </LazyPageWrapper>
);

export const LazyNotFound = (props) => (
  <LazyPageWrapper title="Loading Page">
    <NotFound {...props} />
  </LazyPageWrapper>
);
