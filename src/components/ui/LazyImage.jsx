import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '../../lib/utils';

const LazyImage = ({ 
  src, 
  alt, 
  className = '', 
  placeholder = null,
  blurDataURL = null,
  onLoad = () => {},
  onError = () => {},
  ...props 
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isInView, setIsInView] = useState(false);
  const [hasError, setHasError] = useState(false);
  const imgRef = useRef(null);
  const observerRef = useRef(null);

  useEffect(() => {
    const currentRef = imgRef.current;
    
    if (!currentRef) return;

    // Create intersection observer for lazy loading
    observerRef.current = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true);
          observerRef.current?.disconnect();
        }
      },
      {
        threshold: 0.1,
        rootMargin: '50px'
      }
    );

    observerRef.current.observe(currentRef);

    return () => {
      observerRef.current?.disconnect();
    };
  }, []);

  const handleLoad = () => {
    setIsLoaded(true);
    onLoad();
  };

  const handleError = () => {
    setHasError(true);
    onError();
  };

  // Generate a simple blur placeholder if none provided
  const defaultBlurDataURL = blurDataURL || 
    `data:image/svg+xml;base64,${btoa(`
      <svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:rgb(59,130,246);stop-opacity:0.1" />
            <stop offset="50%" style="stop-color:rgb(147,51,234);stop-opacity:0.05" />
            <stop offset="100%" style="stop-color:rgb(236,72,153);stop-opacity:0.02" />
          </linearGradient>
        </defs>
        <rect width="100%" height="100%" fill="url(#grad)" />
      </svg>
    `)}`;

  return (
    <div ref={imgRef} className={cn("relative overflow-hidden", className)} {...props}>
      <AnimatePresence mode="wait">
        {!isInView ? (
          // Placeholder before image comes into view
          <motion.div
            key="placeholder"
            className="absolute inset-0 bg-gradient-to-br from-slate-800/50 to-slate-900/50 backdrop-blur-sm"
            initial={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
          >
            {placeholder || (
              <div className="flex items-center justify-center h-full">
                <motion.div
                  className="w-8 h-8 border-2 border-blue-500/30 border-t-blue-500 rounded-full"
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                />
              </div>
            )}
          </motion.div>
        ) : (
          <>
            {/* Blur placeholder while loading */}
            {!isLoaded && !hasError && (
              <motion.div
                key="blur-placeholder"
                className="absolute inset-0"
                initial={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.5 }}
              >
                <img
                  src={defaultBlurDataURL}
                  alt=""
                  className="w-full h-full object-cover filter blur-sm scale-110"
                  aria-hidden="true"
                />
                <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-purple-500/10" />
                
                {/* Loading indicator */}
                <div className="absolute inset-0 flex items-center justify-center">
                  <motion.div
                    className="w-8 h-8 border-2 border-blue-400/50 border-t-blue-400 rounded-full"
                    animate={{ rotate: 360 }}
                    transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                  />
                </div>
              </motion.div>
            )}

            {/* Error state */}
            {hasError && (
              <motion.div
                key="error"
                className="absolute inset-0 bg-gradient-to-br from-red-900/20 to-red-800/20 backdrop-blur-sm flex items-center justify-center"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.3 }}
              >
                <div className="text-center text-red-400">
                  <div className="text-2xl mb-2">⚠️</div>
                  <div className="text-sm">Failed to load image</div>
                </div>
              </motion.div>
            )}

            {/* Actual image */}
            {isInView && (
              <motion.img
                key="image"
                src={src}
                alt={alt}
                className={cn(
                  "w-full h-full object-cover transition-all duration-500",
                  isLoaded ? "opacity-100" : "opacity-0"
                )}
                onLoad={handleLoad}
                onError={handleError}
                initial={{ opacity: 0, scale: 1.1 }}
                animate={{ 
                  opacity: isLoaded ? 1 : 0, 
                  scale: isLoaded ? 1 : 1.1 
                }}
                transition={{ 
                  duration: 0.6, 
                  ease: [0.25, 0.46, 0.45, 0.94] 
                }}
              />
            )}
          </>
        )}
      </AnimatePresence>
    </div>
  );
};

export default LazyImage;
