import { Link, useLocation } from 'react-router-dom';
import {
  FiGithub,
  FiMessageCircle,
  FiExternalLink,
  FiCode,
  FiBookOpen,
  FiFileText,
  FiShield,
  FiCoffee
} from 'react-icons/fi';
import { motion } from 'framer-motion';
import { cn } from '../../lib/utils';

const Footer = () => {
  const currentYear = new Date().getFullYear();
  const location = useLocation();

  const sections = [
    {
      title: 'Explore',
      icon: FiCode,
      links: [
        { name: 'Home', path: '/', icon: null },
        { name: 'Scripts', path: '/scripts', icon: null },
      ],
    },
    {
      title: 'Requests',
      icon: FiFileText,
      links: [
        { name: 'Request a Script', path: '/request-script', icon: null },
        { name: 'Request Status', path: '/request-status', icon: null },
      ],
    },
    {
      title: 'Resources',
      icon: FiBookOpen,
      links: [
        { name: 'FAQ', path: '/faq', icon: null },
      ],
    },
    {
      title: 'Legal',
      icon: FiShield,
      links: [
        { name: 'Terms of Service', path: '/terms', icon: null },
        { name: 'Privacy Policy', path: '/privacy', icon: null },
        { name: 'Cookie Policy', path: '/cookies', icon: null },
      ],
    },
  ];

  const socialLinks = [
    { 
      name: 'GitHub', 
      icon: FiGithub, 
      href: 'https://github.com/scriptmaster',
      label: 'Visit our GitHub repository',
    },
    { 
      name: 'Discord', 
      icon: FiMessageCircle, 
      href: 'https://discord.gg/scriptmaster',
      label: 'Join our Discord community',
    },
  ];

  const LinkItem = ({
    href, 
    children, 
    isExternal = false, 
    className = '',
    isActive = false,
    icon: Icon = null
  }) => {
    const baseClasses = 'flex items-center gap-2 transition-colors';
    const activeClasses = isActive 
      ? 'text-primary font-medium' 
      : 'text-foreground/70 hover:text-foreground';
    
    const handleKeyDown = (event, action) => {
      if (event.key === 'Enter' || event.key === 'Space') {
        event.preventDefault();
        action();
      }
    };

    const content = (
      <>
        {Icon && <Icon className="h-4 w-4 flex-shrink-0" />}
        <span>{children}</span>
        {isExternal && (
          <FiExternalLink className="h-3 w-3 opacity-0 group-hover:opacity-100 transition-opacity flex-shrink-0" />
        )}
      </>
    );
    
    if (isExternal) {
      return (
        <a 
          href={href} 
          target="_blank" 
          rel="noopener noreferrer nofollow"
          className={cn(
            baseClasses, 
            activeClasses,
            'group',
            className
          )}
          aria-label={`${children} (opens in new tab)`}
          role="link"
          tabIndex={0}
          onKeyDown={(e) => handleKeyDown(e, () => window.open(href, '_blank'))}
        >
          {content}
        </a>
      );
    }
    
    return (
      <Link 
        to={href} 
        className={cn(
          baseClasses, 
          activeClasses,
          className
        )}
        role="link"
        tabIndex={0}
        onKeyDown={(e) => handleKeyDown(e, () => navigate(href))}
      >
        {content}
      </Link>
    );
  };

  return (
    <footer className="relative overflow-hidden">
      {/* Enhanced Background with Multiple Layers */}
      <div
        className="absolute inset-0"
        style={{
          background: `
            linear-gradient(135deg,
              rgba(15, 23, 42, 0.95) 0%,
              rgba(30, 41, 59, 0.9) 25%,
              rgba(51, 65, 85, 0.85) 50%,
              rgba(71, 85, 105, 0.8) 75%,
              rgba(100, 116, 139, 0.75) 100%
            ),
            linear-gradient(45deg,
              rgba(59, 130, 246, 0.1) 0%,
              rgba(147, 51, 234, 0.08) 35%,
              rgba(236, 72, 153, 0.06) 70%,
              rgba(59, 130, 246, 0.04) 100%
            )
          `,
          backdropFilter: 'blur(40px) saturate(180%)',
        }}
      />

      {/* Animated Background Elements */}
      <div className="absolute inset-0">
        <motion.div
          className="absolute top-0 left-1/4 w-96 h-96 bg-gradient-to-r from-blue-500/5 to-cyan-500/5 rounded-full blur-3xl"
          animate={{
            y: [0, -40, 0],
            x: [0, 30, 0],
            scale: [1, 1.1, 1],
          }}
          transition={{
            duration: 12,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        <motion.div
          className="absolute bottom-0 right-1/4 w-80 h-80 bg-gradient-to-r from-purple-500/5 to-pink-500/5 rounded-full blur-3xl"
          animate={{
            y: [0, 25, 0],
            x: [0, -20, 0],
            scale: [1, 0.9, 1],
          }}
          transition={{
            duration: 15,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 3
          }}
        />
        <motion.div
          className="absolute top-1/2 left-0 w-64 h-64 bg-gradient-to-r from-indigo-500/4 to-blue-500/4 rounded-full blur-3xl"
          animate={{
            y: [0, -20, 0],
            x: [0, 15, 0],
          }}
          transition={{
            duration: 18,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 6
          }}
        />
      </div>

      {/* Subtle Grid Pattern */}
      <div
        className="absolute inset-0 opacity-[0.02]"
        style={{
          backgroundImage: `
            linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px),
            linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px)
          `,
          backgroundSize: '50px 50px'
        }}
      />

      <div className="relative z-10 container mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Premium Footer Content */}
        <div className="space-y-8">
          {/* Main Content Row */}
          <motion.div
            className="flex flex-col lg:flex-row items-start lg:items-center justify-between space-y-8 lg:space-y-0"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, ease: [0.25, 0.46, 0.45, 0.94] }}
          >
            {/* Brand Section - Enhanced */}
            <motion.div
              className="flex flex-col space-y-6 lg:max-w-md"
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              <Link to="/" className="flex items-center space-x-4 group w-fit" aria-label="Home page">
                <motion.div
                  className="relative"
                  whileHover={{ scale: 1.08, rotate: 3 }}
                  transition={{ type: "spring", stiffness: 300, damping: 20 }}
                >
                  {/* Enhanced glow effect */}
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-400/30 via-purple-400/25 to-pink-400/20 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-500 scale-110"></div>
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-xl blur-md group-hover:blur-lg transition-all duration-300"></div>
                  <img
                    src="/project-madara-logo.svg"
                    alt="Project Madara Logo"
                    className="relative h-12 w-12 drop-shadow-2xl transition-all duration-500 group-hover:drop-shadow-[0_0_30px_rgba(147,197,253,0.6)]"
                  />
                </motion.div>
                <div className="flex flex-col">
                  <motion.span
                    className="text-xl font-bold bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 bg-clip-text text-transparent"
                    whileHover={{ scale: 1.02 }}
                    transition={{ type: "spring", stiffness: 400, damping: 17 }}
                  >
                    Project Madara
                  </motion.span>
                  <span className="text-xs text-blue-300/60 font-medium tracking-wider uppercase">
                    Script Hub
                  </span>
                </div>
              </Link>

              <p className="text-sm text-slate-300/80 leading-relaxed">
                Empowering developers with premium scripts and tools.
                <br />
                <span className="text-blue-300/70">Built for excellence, designed for performance.</span>
              </p>

              {/* Social Links - Enhanced */}
              <div className="flex items-center space-x-3">
                {socialLinks.map((link, index) => (
                  <motion.a
                    key={link.name}
                    href={link.href}
                    target="_blank"
                    rel="noopener noreferrer nofollow"
                    aria-label={link.label}
                    className="group relative p-3 rounded-2xl bg-white/5 backdrop-blur-md border border-white/10 text-slate-300/70 hover:text-blue-300 transition-all duration-500 shadow-lg hover:shadow-2xl hover:shadow-blue-500/10"
                    tabIndex={0}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' || e.key === 'Space') {
                        e.preventDefault();
                        window.open(link.href, '_blank');
                      }
                    }}
                    whileHover={{ scale: 1.15, y: -3 }}
                    whileTap={{ scale: 0.95 }}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.5, delay: 0.4 + index * 0.1 }}
                  >
                    <div className="absolute inset-0 bg-gradient-to-r from-blue-500/0 to-purple-500/0 group-hover:from-blue-500/10 group-hover:to-purple-500/10 rounded-2xl transition-all duration-500"></div>
                    <link.icon className="relative h-5 w-5" />
                  </motion.a>
                ))}
              </div>
            </motion.div>

            {/* Navigation Grid - Redesigned */}
            <motion.div
              className="grid grid-cols-2 md:grid-cols-4 gap-6 lg:gap-8"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, delay: 0.3 }}
            >
              {sections.map((section, sectionIndex) => (
                <motion.div
                  key={section.title}
                  className="space-y-4"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: 0.4 + sectionIndex * 0.1 }}
                >
                  <div className="flex items-center space-x-2">
                    <motion.div
                      whileHover={{ scale: 1.1, rotate: 5 }}
                      transition={{ type: "spring", stiffness: 400, damping: 17 }}
                    >
                      <section.icon className="h-4 w-4 text-blue-400" />
                    </motion.div>
                    <h3 className="text-sm font-semibold text-slate-200 tracking-wide">
                      {section.title}
                    </h3>
                  </div>
                  <ul className="space-y-2">
                    {section.links.map((link, linkIndex) => {
                      const isActive = location.pathname === link.path;
                      return (
                        <motion.li
                          key={link.name}
                          initial={{ opacity: 0, x: -10 }}
                          whileInView={{ opacity: 1, x: 0 }}
                          viewport={{ once: true }}
                          transition={{ duration: 0.4, delay: 0.5 + sectionIndex * 0.1 + linkIndex * 0.05 }}
                        >
                          <LinkItem
                            href={link.path}
                            isActive={isActive}
                            className={cn(
                              "text-sm py-2 px-3 rounded-lg transition-all duration-300 hover:bg-white/5 hover:text-blue-300 block",
                              isActive
                                ? "bg-gradient-to-r from-blue-500/15 to-purple-500/10 backdrop-blur-sm border border-blue-500/25 text-blue-300 shadow-lg"
                                : "text-slate-300/70"
                            )}
                          >
                            {link.name}
                          </LinkItem>
                        </motion.li>
                      );
                    })}
                  </ul>
                </motion.div>
              ))}
            </motion.div>
          </motion.div>

          {/* Premium Bottom Bar */}
          <motion.div
            className="pt-8 mt-8"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.6 }}
          >
            {/* Elegant Divider */}
            <div className="relative mb-8">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gradient-to-r from-transparent via-white/20 to-transparent"></div>
              </div>
              <div className="relative flex justify-center">
                <div className="bg-gradient-to-r from-blue-500/10 to-purple-500/10 backdrop-blur-sm px-6 py-2 rounded-full border border-white/10">
                  <motion.div
                    animate={{ rotate: [0, 10, -10, 0] }}
                    transition={{ duration: 3, repeat: Infinity, repeatDelay: 2 }}
                  >
                    <FiCoffee className="h-4 w-4 text-amber-400" />
                  </motion.div>
                </div>
              </div>
            </div>

            <div className="flex flex-col md:flex-row justify-between items-center space-y-6 md:space-y-0">
              {/* Left - Enhanced Copyright */}
              <motion.div
                className="flex flex-col md:flex-row items-center space-y-2 md:space-y-0 md:space-x-6"
                initial={{ opacity: 0, x: -30 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.8, delay: 0.7 }}
              >
                <p className="text-sm text-slate-300/80 flex items-center">
                  <span className="font-medium">&copy; {currentYear} Project Madara.</span>
                  <span className="ml-2 text-slate-400/60">All rights reserved.</span>
                </p>
                <div className="hidden md:block w-px h-4 bg-white/20"></div>
                <p className="text-xs text-slate-400/60 flex items-center">
                  <span>Crafted with precision for the developer community</span>
                </p>
              </motion.div>

              {/* Right - Enhanced Legal Links */}
              <motion.div
                className="flex items-center space-x-1"
                initial={{ opacity: 0, x: 30 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.8, delay: 0.8 }}
              >
                {[
                  { href: "/privacy", text: "Privacy Policy" },
                  { href: "/terms", text: "Terms of Service" },
                  { href: "/cookies", text: "Cookie Policy" }
                ].map((item, index) => (
                  <motion.div
                    key={item.href}
                    className="flex items-center"
                    initial={{ opacity: 0, y: 10 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.5, delay: 0.9 + index * 0.1 }}
                  >
                    <LinkItem
                      href={item.href}
                      className="text-xs text-slate-300/70 hover:text-blue-300 transition-all duration-300 px-3 py-2 rounded-lg hover:bg-white/5 hover:backdrop-blur-sm"
                    >
                      {item.text}
                    </LinkItem>
                    {index < 2 && (
                      <span className="mx-2 text-slate-500/40 text-xs">•</span>
                    )}
                  </motion.div>
                ))}
              </motion.div>
            </div>
          </motion.div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
