import { Suspense, useEffect } from 'react';
import { Outlet, useLocation } from 'react-router-dom';
import { Toaster } from 'sonner';
import Header from './Header';
import Footer from './Footer';
import Loader from '../ui/Loader';
import { cn } from '../../lib/utils';

const MainLayout = () => {
  const location = useLocation();

  // Reset scroll on route change
  useEffect(() => {
    window.scrollTo(0, 0);
  }, [location.pathname]);

  // Handle scroll to top
  const handleScrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  // Handle keyboard interaction for scroll to top button
  const handleKeyDownScrollToTop = (event) => {
    if (event.key === 'Enter' || event.key === 'Space') {
      event.preventDefault();
      handleScrollToTop();
    }
  };

  return (
    <div className="min-h-screen flex flex-col bg-background text-foreground antialiased">
      {/* Global Notifications */}
      <Toaster 
        position="top-center" 
        toastOptions={{
          className: 'font-sans',
          style: {
            background: 'hsl(var(--background))',
            color: 'hsl(var(--foreground))',
            border: '1px solid hsl(var(--border))',
            padding: '0.75rem 1rem',
            borderRadius: '0.5rem',
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
          },
        }}
      />

      {/* Header */}
      <header className="sticky top-0 z-50">
        <Header />
      </header>

      {/* Main Content */}
      <main className="flex-grow pt-16 md:pt-20">
        <Suspense 
          fallback={<Loader />}
        >
          <Outlet />
        </Suspense>
      </main>

      {/* Footer */}
      <footer className="mt-auto">
        <Footer />
      </footer>

      {/* Scroll to Top Button */}
      <button
        onClick={handleScrollToTop}
        onKeyDown={handleKeyDownScrollToTop}
        tabIndex={0}
        className={cn(
          'fixed bottom-6 right-6 p-3 rounded-full bg-primary/10 backdrop-blur-sm text-primary',
          'hover:bg-primary/20 transition-colors shadow-lg z-40',
        )}
        aria-label="Scroll to top"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="20"
          height="20"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        >
          <path d="M18 15l-6-6-6 6" />
        </svg>
      </button>
    </div>
  );
};

export default MainLayout;
