import { useState, useEffect, useRef } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  FiMenu, 
  FiX, 
  FiMoon, 
  FiSun, 
  FiGithub, 
  FiMessageCircle,
  FiSunrise,
  FiSettings,
  FiChevronDown,
  FiCheck,
  FiExternalLink
} from 'react-icons/fi';
import { useTheme } from '../../context/ThemeContext';
import { Button } from '../ui/Button';
import { cn } from '../../lib/utils';

const Header = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const [showThemeMenu, setShowThemeMenu] = useState(false);
  const { theme, toggleTheme, isDark, resolvedTheme, systemTheme } = useTheme();
  const location = useLocation();
  const navigate = useNavigate();
  const themeMenuRef = useRef(null);
  
  // Close theme menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (themeMenuRef.current && !themeMenuRef.current.contains(event.target)) {
        setShowThemeMenu(false);
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Handle scrolling effect - throttled for better performance
  useEffect(() => {
    let lastScrollY = window.scrollY;
    let ticking = false;

    const updateScrolled = () => {
      const currentScrollY = window.scrollY;
      setScrolled(currentScrollY > 50);
      lastScrollY = currentScrollY;
      ticking = false;
    };

    const handleScroll = () => {
      if (!ticking) {
        window.requestAnimationFrame(updateScrolled);
        ticking = true;
      }
    };

    // Add passive: true for better scrolling performance
    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  // Close mobile menu when route changes
  useEffect(() => {
    setIsOpen(false);
  }, [location]);

  const navLinks = [
    { name: 'Home', path: '/', icon: null },
    { name: 'Scripts', path: '/scripts', icon: null },
    { name: 'Get Key', path: '/get-key', icon: null },
    { name: 'Contact Us', path: '/contact', icon: null },
  ];

  const socialLinks = [
    { 
      name: 'GitHub', 
      icon: FiGithub, 
      href: 'https://github.com/scriptmaster',
      label: 'GitHub repository'
    },
    { 
      name: 'Discord', 
      icon: FiMessageCircle, 
      href: 'https://discord.gg/scriptmaster',
      label: 'Discord community'
    },
  ];

  const toggleMobileMenu = () => setIsOpen(!isOpen);

  const themeOptions = [
    { id: 'light', label: 'Light', icon: FiSun },
    { id: 'dark', label: 'Dark', icon: FiMoon },
    { id: 'system', label: 'System', icon: FiSettings }
  ];

  // Theme toggle with dropdown
  const ThemeToggle = () => {
    const currentThemeIcon = {
      light: <FiSun className="h-5 w-5" aria-hidden="true" />,
      dark: <FiMoon className="h-5 w-5" aria-hidden="true" />,
      system: <FiSettings className="h-5 w-5" aria-hidden="true" />
    }[resolvedTheme] || <FiSun className="h-5 w-5" aria-hidden="true" />;

    return (
      <div className="relative" ref={themeMenuRef}>
        <Button
          variant="ghost"
          size="icon"
          onClick={() => setShowThemeMenu(!showThemeMenu)}
          className="w-10 h-10 p-0 flex items-center justify-center text-lg"
          aria-label="Toggle theme"
          aria-expanded={showThemeMenu}
          aria-haspopup="true"
        >
          {currentThemeIcon}
        </Button>

        <AnimatePresence>
          {showThemeMenu && (
            <motion.div
              initial={{ opacity: 0, y: 10, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: 10, scale: 0.95 }}
              transition={{ 
                type: 'spring', 
                stiffness: 400, 
                damping: 25,
                duration: 0.2
              }}
              className={cn(
                "absolute right-0 mt-2 w-48 z-50",
                "bg-popover rounded-lg shadow-lg overflow-hidden",
                "border border-border/50 backdrop-blur-sm"
              )}
              role="menu"
              aria-orientation="vertical"
              aria-labelledby="theme-menu-button"
            >
              <div className="p-1">
                <div className="px-3 py-1.5 text-xs font-medium text-muted-foreground">
                  Theme
                </div>
                {themeOptions.map((option) => (
                  <button
                    key={option.id}
                    onClick={() => {
                      toggleTheme(option.id);
                      setShowThemeMenu(false);
                    }}
                    className={cn(
                      'w-full flex items-center justify-between px-3 py-2 text-sm rounded-md',
                      'hover:bg-accent hover:text-accent-foreground transition-colors',
                      'focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 focus:ring-offset-background',
                      'text-left',
                      theme === option.id ? 'text-foreground font-medium' : 'text-muted-foreground'
                    )}
                    role="menuitem"
                  >
                    <div className="flex items-center space-x-2">
                      <option.icon className="h-4 w-4" />
                      <span>{option.label}</span>
                    </div>
                    {theme === option.id && (
                      <FiCheck className="h-4 w-4 text-primary" aria-hidden="true" />
                    )}
                  </button>
                ))}
              </div>
              <div 
                className="px-3 py-1.5 text-xs text-muted-foreground border-t border-border/30"
                aria-live="polite"
                aria-atomic="true"
              >
                {theme === 'system' 
                  ? `System (${systemTheme})` 
                  : `Using ${theme} theme`}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    );
  };

  return (
    <header
      className={cn(
        'fixed top-0 left-0 right-0 z-50 transition-all duration-500 transform-gpu',
        scrolled
          ? 'bg-white/10 dark:bg-gray-900/10 backdrop-blur-xl border-b border-white/20 shadow-2xl'
          : 'bg-white/5 dark:bg-gray-900/5 backdrop-blur-lg border-b border-white/10',
      )}
      style={{
        willChange: 'transform, background-color, backdrop-filter, border-color, box-shadow',
        transform: 'translateZ(0)', // Force GPU acceleration
        background: scrolled
          ? 'linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(147,197,253,0.05) 50%, rgba(196,181,253,0.05) 100%)'
          : 'linear-gradient(135deg, rgba(255,255,255,0.05) 0%, rgba(147,197,253,0.02) 50%, rgba(196,181,253,0.02) 100%)',
      }}
    >
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16 md:h-20">
          {/* Logo */}
          <div className="flex-shrink-0 flex items-center">
            <Link to="/" className="flex items-center space-x-3 group">
              <motion.div
                className="relative"
                whileHover={{ scale: 1.05, rotate: 5 }}
                whileTap={{ scale: 0.95 }}
                transition={{ type: "spring", stiffness: 400, damping: 17 }}
              >
                <div className="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-xl blur-lg group-hover:blur-xl transition-all duration-300"></div>
                <img
                  src="/project-madara-logo.svg"
                  alt="Project Madara Logo"
                  className="relative h-12 w-12 drop-shadow-2xl transition-all duration-300 group-hover:drop-shadow-[0_0_20px_rgba(147,197,253,0.5)]"
                />
              </motion.div>
              <motion.span
                className="text-xl font-bold bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 bg-clip-text text-transparent"
                whileHover={{ scale: 1.02 }}
                transition={{ type: "spring", stiffness: 400, damping: 17 }}
              >
                Project Madara
              </motion.span>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-2">
            {navLinks
              .filter(link => !link.mobileOnly)
              .map((link) => (
                <motion.div key={link.path} whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                  <Link
                    to={link.path}
                    className={cn(
                      'relative px-4 py-2.5 rounded-xl text-sm font-medium transition-all duration-300',
                      'hover:bg-white/10 hover:backdrop-blur-sm hover:shadow-lg',
                      'before:absolute before:inset-0 before:rounded-xl before:bg-gradient-to-r before:from-blue-500/10 before:to-purple-500/10 before:opacity-0 before:transition-opacity before:duration-300 hover:before:opacity-100',
                      location.pathname === link.path
                        ? 'text-blue-400 font-semibold bg-white/10 backdrop-blur-sm shadow-lg border border-white/20'
                        : 'text-foreground/80 hover:text-white'
                    )}
                  >
                    <span className="relative z-10">{link.name}</span>
                  </Link>
                </motion.div>
              ))}
          </nav>

          {/* Right Side - Desktop */}
          <div className="hidden md:flex items-center space-x-4">
            {/* Theme Toggle */}
            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <ThemeToggle />
            </motion.div>

            {/* Request Button */}
            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Button
                variant="default"
                size="sm"
                onClick={() => navigate('/request-script')}
                className="hidden sm:inline-flex items-center group bg-gradient-to-r from-blue-500/20 to-purple-500/20 backdrop-blur-sm border border-blue-500/30 hover:from-blue-500/30 hover:to-purple-500/30 hover:border-blue-400/50 text-blue-400 hover:text-white transition-all duration-300 shadow-lg hover:shadow-xl"
              >
                <span>Request Script</span>
                <FiExternalLink className="ml-1 h-3 w-3 opacity-0 group-hover:opacity-100 transition-opacity" />
              </Button>
            </motion.div>

            {/* Social Links */}
            <div className="flex items-center space-x-2">
              {socialLinks.map((social) => (
                <motion.a
                  key={social.name}
                  href={social.href}
                  target="_blank"
                  rel="noopener noreferrer nofollow"
                  aria-label={social.label}
                  className="p-2.5 rounded-xl bg-white/5 backdrop-blur-sm border border-white/10 text-foreground/60 hover:text-blue-400 hover:bg-white/10 hover:border-blue-500/30 transition-all duration-300 shadow-lg hover:shadow-xl"
                  whileHover={{ scale: 1.1, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <social.icon className="h-4 w-4" />
                </motion.a>
              ))}
            </div>
          </div>

          {/* Mobile menu button */}
          <div className="flex items-center md:hidden">
            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Button
                variant="ghost"
                size="sm"
                onClick={toggleMobileMenu}
                className="p-2.5 rounded-xl bg-white/5 backdrop-blur-sm border border-white/10 hover:bg-white/10 hover:border-blue-500/30 transition-all duration-300 shadow-lg"
                aria-label={isOpen ? 'Close menu' : 'Open menu'}
                aria-expanded={isOpen}
                aria-controls="mobile-menu"
              >
                <motion.div
                  animate={{ rotate: isOpen ? 180 : 0 }}
                  transition={{ duration: 0.3 }}
                >
                  {isOpen ? (
                    <FiX className="h-5 w-5 text-blue-400" />
                  ) : (
                    <FiMenu className="h-5 w-5 text-foreground/80" />
                  )}
                </motion.div>
              </Button>
            </motion.div>
          </div>
        </div>
      </div>

      {/* Mobile Menu */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, height: 0, y: -20 }}
            animate={{ opacity: 1, height: 'auto', y: 0 }}
            exit={{ opacity: 0, height: 0, y: -20 }}
            transition={{ duration: 0.3, ease: [0.4, 0, 0.2, 1] }}
            className="md:hidden overflow-hidden"
            id="mobile-menu"
          >
            <div
              className="px-4 pt-4 pb-6 space-y-3 border-t border-white/20 backdrop-blur-xl shadow-2xl"
              style={{
                background: 'linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(147,197,253,0.05) 50%, rgba(196,181,253,0.05) 100%)',
              }}
            >
              {navLinks.map((link, index) => (
                <motion.div
                  key={link.path}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                >
                  <Link
                    to={link.path}
                    className={cn(
                      'block px-4 py-3 rounded-xl text-base font-medium transition-all duration-300',
                      'hover:bg-white/10 hover:backdrop-blur-sm hover:shadow-lg',
                      location.pathname === link.path
                        ? 'bg-gradient-to-r from-blue-500/20 to-purple-500/20 backdrop-blur-sm border border-blue-500/30 text-blue-400 font-semibold shadow-lg'
                        : 'text-foreground/80 hover:text-white'
                    )}
                    onClick={() => setIsOpen(false)}
                  >
                    <div className="flex items-center">
                      {link.icon && <link.icon className="mr-3 h-5 w-5 flex-shrink-0" />}
                      <span>{link.name}</span>
                    </div>
                  </Link>
                </motion.div>
              ))}
              
              <div className="pt-2 border-t border-border/10 mt-2">
                <div className="px-3 py-2 space-y-2">
                  <div className="text-sm font-medium text-foreground/70 mb-1">Theme</div>
                  <div className="grid grid-cols-3 gap-2">
                    {themeOptions.map((option) => (
                      <button
                        key={option.id}
                        onClick={() => {
                          toggleTheme(option.id);
                        }}
                        className={cn(
                          'flex items-center justify-center p-2 rounded-md transition-colors',
                          'hover:bg-accent/10',
                          'focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 focus:ring-offset-background',
                          theme === option.id 
                            ? 'bg-accent/20 text-primary font-medium' 
                            : 'text-foreground/60'
                        )}
                        aria-label={`Set theme to ${option.label}`}
                      >
                        <div className="flex flex-col items-center">
                          <option.icon className="h-4 w-4 mb-1" />
                          <span className="text-xs">{option.label}</span>
                        </div>
                      </button>
                    ))}
                  </div>
                </div>
                
                <div className="px-3 py-2">
                  <Button
                    variant="default"
                    size="sm"
                    onClick={() => {
                      navigate('/request-script');
                      setIsOpen(false);
                    }}
                    className="w-full justify-center"
                  >
                    Request Script
                  </Button>
                </div>
                
                <div className="flex justify-center space-x-4 px-3 py-3 border-t border-border/10 mt-2">
                  {socialLinks.map((social) => (
                    <a
                      key={social.name}
                      href={social.href}
                      target="_blank"
                      rel="noopener noreferrer nofollow"
                      aria-label={social.label}
                      className="p-2 rounded-full text-foreground/60 hover:text-foreground hover:bg-accent/10 transition-colors"
                    >
                      <social.icon className="h-4 w-4" />
                    </a>
                  ))}
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </header>
  );
};

export default Header;
