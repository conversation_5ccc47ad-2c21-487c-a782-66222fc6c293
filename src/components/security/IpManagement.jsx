import React, { useState, useEffect } from 'react';
import { 
  Table, 
  Button, 
  Modal, 
  Form, 
  Input, 
  Typography, 
  Card, 
  Tag, 
  Alert, 
  Space,
  Tabs,
  List,
  Popconfirm,
  message
} from 'antd';
import { 
  StopOutlined, 
  CheckCircleOutlined, 
  PlusOutlined, 
  DeleteOutlined,
  WarningOutlined,
  SecurityScanOutlined
} from '@ant-design/icons';
import { useSecurity } from '../../context/SecurityContext';

const { Title, Text } = Typography;
const { TabPane } = Tabs;

const IpManagement = () => {
  const { 
    blockedIps, 
    blockIp, 
    unblockIp, 
    loading,
    securityService
  } = useSecurity();
  
  const [isBlockModalVisible, setIsBlockModalVisible] = useState(false);
  const [isWhitelistModalVisible, setIsWhitelistModalVisible] = useState(false);
  const [activeTab, setActiveTab] = useState('blocked');
  const [form] = Form.useForm();
  const [whitelistForm] = Form.useForm();
  
  const [whitelistedIps, setWhitelistedIps] = useState([]);
  const [loadingWhitelist, setLoadingWhitelist] = useState(false);
  
  // Load whitelisted IPs
  useEffect(() => {
    const fetchWhitelistedIps = async () => {
      try {
        setLoadingWhitelist(true);
        const response = await securityService.ipWhitelist.list();
        setWhitelistedIps(response);
      } catch (error) {
        console.error('Error fetching whitelisted IPs:', error);
        message.error('Failed to load whitelisted IPs');
      } finally {
        setLoadingWhitelist(false);
      }
    };
    
    fetchWhitelistedIps();
  }, []);
  
  const handleBlockIp = async (values) => {
    try {
      await blockIp(values.ip, values.reason);
      setIsBlockModalVisible(false);
      form.resetFields();
      message.success(`IP ${values.ip} has been blocked`);
    } catch (error) {
      console.error('Error blocking IP:', error);
      message.error('Failed to block IP. Please try again.');
    }
  };
  
  const handleUnblockIp = async (ip) => {
    try {
      await unblockIp(ip);
      message.success(`IP ${ip} has been unblocked`);
    } catch (error) {
      console.error('Error unblocking IP:', error);
      message.error('Failed to unblock IP. Please try again.');
    }
  };
  
  const handleAddToWhitelist = async (values) => {
    try {
      await securityService.ipWhitelist.add(values.ip);
      setWhitelistedIps(prev => [...new Set([...prev, values.ip])]);
      setIsWhitelistModalVisible(false);
      whitelistForm.resetFields();
      message.success(`IP ${values.ip} has been added to whitelist`);
    } catch (error) {
      console.error('Error adding IP to whitelist:', error);
      message.error('Failed to add IP to whitelist');
    }
  };
  
  const handleRemoveFromWhitelist = async (ip) => {
    try {
      await securityService.ipWhitelist.remove(ip);
      setWhitelistedIps(prev => prev.filter(i => i !== ip));
      message.success(`IP ${ip} has been removed from whitelist`);
    } catch (error) {
      console.error('Error removing IP from whitelist:', error);
      message.error('Failed to remove IP from whitelist');
    }
  };
  
  const renderBlockedIps = () => (
    <div>
      <div style={{ marginBottom: 16, textAlign: 'right' }}>
        <Button 
          type="primary" 
          danger 
          icon={<StopOutlined />}
          onClick={() => setIsBlockModalVisible(true)}
        >
          Block IP Address
        </Button>
      </div>
      
      {blockedIps.length > 0 ? (
        <List
          itemLayout="horizontal"
          dataSource={blockedIps}
          renderItem={(ip) => (
            <List.Item
              actions={[
                <Popconfirm
                  title="Are you sure you want to unblock this IP?"
                  onConfirm={() => handleUnblockIp(ip)}
                  okText="Yes, unblock"
                  cancelText="No, keep blocked"
                >
                  <Button type="link" size="small">Unblock</Button>
                </Popconfirm>
              ]}
            >
              <List.Item.Meta
                title={<Text copyable>{ip}</Text>}
                description="Blocked on 2023-06-15 • Suspicious activity detected"
              />
            </List.Item>
          )}
        />
      ) : (
        <div style={{ textAlign: 'center', padding: '40px 0' }}>
          <SecurityScanOutlined style={{ fontSize: 48, color: '#d9d9d9', marginBottom: 16 }} />
          <div>No IPs are currently blocked</div>
          <Text type="secondary">All IP addresses have access to your account</Text>
        </div>
      )}
    </div>
  );
  
  const renderWhitelistedIps = () => (
    <div>
      <Alert
        message="Whitelisted IPs"
        description="IP addresses in the whitelist will bypass all security restrictions. Use with caution."
        type="info"
        showIcon
        style={{ marginBottom: 16 }}
      />
      
      <div style={{ marginBottom: 16, textAlign: 'right' }}>
        <Button 
          type="primary" 
          icon={<PlusOutlined />}
          onClick={() => setIsWhitelistModalVisible(true)}
        >
          Add IP to Whitelist
        </Button>
      </div>
      
      {whitelistedIps.length > 0 ? (
        <List
          itemLayout="horizontal"
          dataSource={whitelistedIps}
          renderItem={(ip) => (
            <List.Item
              actions={[
                <Popconfirm
                  title="Are you sure you want to remove this IP from whitelist?"
                  onConfirm={() => handleRemoveFromWhitelist(ip)}
                  okText="Yes, remove"
                  cancelText="No, keep"
                >
                  <Button type="link" danger size="small">Remove</Button>
                </Popconfirm>
              ]}
            >
              <List.Item.Meta
                title={<Text copyable>{ip}</Text>}
                description={
                  <Space>
                    <Tag color="green" icon={<CheckCircleOutlined />}>Whitelisted</Tag>
                    <Text type="secondary">Added on 2023-01-01</Text>
                  </Space>
                }
              />
            </List.Item>
          )}
        />
      ) : (
        <div style={{ textAlign: 'center', padding: '40px 0' }}>
          <SecurityScanOutlined style={{ fontSize: 48, color: '#d9d9d9', marginBottom: 16 }} />
          <div>No whitelisted IPs</div>
          <Text type="secondary">Add IPs to bypass security restrictions</Text>
        </div>
      )}
    </div>
  );
  
  return (
    <Card>
      <Tabs 
        activeKey={activeTab} 
        onChange={setActiveTab}
        style={{ marginBottom: 24 }}
      >
        <TabPane
          tab={
            <span>
              <StopOutlined />
              Blocked IPs
              {blockedIps.length > 0 && (
                <span style={{ marginLeft: 8 }}>
                  <Tag color="red">{blockedIps.length}</Tag>
                </span>
              )}
            </span>
          }
          key="blocked"
        >
          {renderBlockedIps()}
        </TabPane>
        <TabPane
          tab={
            <span>
              <CheckCircleOutlined />
              Whitelisted IPs
              {whitelistedIps.length > 0 && (
                <span style={{ marginLeft: 8 }}>
                  <Tag color="green">{whitelistedIps.length}</Tag>
                </span>
              )}
            </span>
          }
          key="whitelisted"
        >
          {renderWhitelistedIps()}
        </TabPane>
      </Tabs>
      
      {/* Block IP Modal */}
      <Modal
        title="Block IP Address"
        open={isBlockModalVisible}
        onCancel={() => {
          setIsBlockModalVisible(false);
          form.resetFields();
        }}
        footer={null}
        destroyOnClose
      >
        <Form
          loading={loading || loadingWhitelist}
          layout="vertical"
          onFinish={handleBlockIp}
        >
          <Form.Item
            name="ip"
            label="IP Address"
            rules={[
              { required: true, message: 'Please enter an IP address' },
              {
                pattern: /^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
                message: 'Please enter a valid IP address',
              },
            ]}
          >
            <Input placeholder="e.g., ***********" />
          </Form.Item>
          
          <Form.Item
            name="reason"
            label="Reason"
            rules={[{ required: true, message: 'Please provide a reason for blocking this IP' }]}
          >
            <Input.TextArea 
              placeholder="e.g., Multiple failed login attempts"
              rows={3}
            />
          </Form.Item>
          
          <Alert
            message="Warning"
            description="Blocking an IP address will prevent any requests from that IP. Use with caution."
            type="warning"
            showIcon
            style={{ marginBottom: 16 }}
          />
          
          <div style={{ textAlign: 'right' }}>
            <Button 
              onClick={() => {
                setIsBlockModalVisible(false);
                form.resetFields();
              }}
              style={{ marginRight: 8 }}
            >
              Cancel
            </Button>
            <Button type="primary" htmlType="submit" danger>
              Block IP
            </Button>
          </div>
        </Form>
      </Modal>
      
      {/* Whitelist IP Modal */}
      <Modal
        title="Add IP to Whitelist"
        open={isWhitelistModalVisible}
        onCancel={() => {
          setIsWhitelistModalVisible(false);
          whitelistForm.resetFields();
        }}
        footer={null}
        destroyOnClose
      >
        <Form
          form={whitelistForm}
          layout="vertical"
          onFinish={handleAddToWhitelist}
        >
          <Form.Item
            name="ip"
            label="IP Address or Range (CIDR)"
            rules={[
              { required: true, message: 'Please enter an IP address or range' },
              {
                pattern: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)(?:\/([0-9]|[1-2][0-9]|3[0-2]))?$/,
                message: 'Please enter a valid IP address or CIDR range',
              },
            ]}
          >
            <Input placeholder="e.g., *********** or ***********/24" />
          </Form.Item>
          
          <Form.Item
            name="description"
            label="Description (Optional)"
          >
            <Input placeholder="e.g., Office Network" />
          </Form.Item>
          
          <Alert
            message="Security Notice"
            description={
              <div>
                <p>Whitelisted IPs will bypass all security restrictions, including:</p>
                <ul style={{ margin: '8px 0 0 16px', paddingLeft: 0 }}>
                  <li>Rate limiting</li>
                  <li>Brute force protection</li>
                  <li>Geo-blocking</li>
                </ul>
                <p style={{ margin: '8px 0 0 0' }}>Only whitelist trusted IP addresses.</p>
              </div>
            }
            type="warning"
            showIcon
            style={{ marginBottom: 16 }}
          />
          
          <div style={{ textAlign: 'right' }}>
            <Button 
              onClick={() => {
                setIsWhitelistModalVisible(false);
                whitelistForm.resetFields();
              }}
              style={{ marginRight: 8 }}
            >
              Cancel
            </Button>
            <Button type="primary" htmlType="submit">
              Add to Whitelist
            </Button>
          </div>
        </Form>
      </Modal>
    </Card>
  );
};

export default IpManagement;
