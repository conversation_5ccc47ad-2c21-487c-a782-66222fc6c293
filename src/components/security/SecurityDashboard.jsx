import React, { useState, useEffect, useCallback } from 'react';
import { 
  Card, 
  Row, 
  Col, 
  Typography, 
  Tabs, 
  Spin, 
  Modal, 
  Button,
  Badge,
  message
} from 'antd';
import { 
  SafetyCertificateOutlined, 
  KeyOutlined, 
  LockOutlined, 
  AuditOutlined,
  SecurityScanOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import { useSecurity } from '../../context/SecurityContext';
import SecurityOverview from './SecurityOverview';
import ApiKeys from './ApiKeys';
import IpManagement from './IpManagement';
import SecurityLogs from './SecurityLogs';

const { Title, Text } = Typography;
const { TabPane } = Tabs;
const { confirm } = Modal;

const SecurityDashboard = () => {
  const { 
    isLoading,
    error,
    securityLogs,
    securityStats,
    blockedIPs,
    apiKeys,
    fetchSecurityLogs,
    fetchSecurityStats,
    fetchBlockedIPs,
    fetchAPIKeys,
    blockIP,
    unblockIP,
    generateAPIKey,
    revokeAPIKey,
    reportViolation
  } = useSecurity();
  
  const [activeTab, setActiveTab] = useState('overview');
  const [logsPagination, setLogsPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const [logsFilters, setLogsFilters] = useState({});
  const [logsSorter, setLogsSorter] = useState({});
  
  // Load data when tab changes or pagination/filters/sort changes
  useEffect(() => {
    loadData();
  }, [activeTab, logsPagination.current, logsPagination.pageSize, logsFilters, logsSorter]);
  
  const loadData = useCallback(async () => {
    try {
      switch (activeTab) {
        case 'overview':
          await fetchSecurityStats();
          break;
          
        case 'logs':
          await fetchSecurityLogs({
            page: logsPagination.current,
            pageSize: logsPagination.pageSize,
            ...logsFilters,
            sortBy: logsSorter.field,
            sortOrder: logsSorter.order === 'ascend' ? 'asc' : 'desc',
          });
          break;
          
        case 'ip':
          await fetchBlockedIPs();
          break;
          
        case 'api-keys':
          await fetchAPIKeys();
          break;
          
        default:
          break;
      }
    } catch (error) {
      console.error('Error loading security data:', error);
      message.error(`Failed to load ${activeTab} data: ${error.message}`);
    }
  }, [
    activeTab, 
    fetchSecurityStats, 
    fetchSecurityLogs, 
    fetchBlockedIPs, 
    fetchAPIKeys,
    logsPagination.current, 
    logsPagination.pageSize,
    logsFilters,
    logsSorter
  ]);
  
  // Handle table change for logs (pagination, filters, sorter)
  const handleTableChange = (pagination, filters, sorter) => {
    setLogsPagination({
      ...logsPagination,
      current: pagination.current,
      pageSize: pagination.pageSize,
    });
    
    // Convert filters from Ant Design format to our API format
    const processedFilters = {};
    Object.entries(filters).forEach(([key, value]) => {
      if (value && value.length > 0) {
        processedFilters[key] = value[0];
      }
    });
    
    setLogsFilters(processedFilters);
    
    // Handle sorting
    if (sorter.field) {
      setLogsSorter({
        field: sorter.field,
        order: sorter.order,
      });
    } else {
      setLogsSorter({});
    }
  };
  
  // Handle IP blocking
  const handleBlockIP = async (ip, reason = 'Manual block') => {
    try {
      await blockIP(ip, reason);
      message.success(`IP ${ip} has been blocked`);
    } catch (error) {
      console.error('Error blocking IP:', error);
      message.error(`Failed to block IP: ${error.message}`);
    }
  };
  
  // Handle IP unblocking with confirmation
  const handleUnblockIP = (ip) => {
    confirm({
      title: 'Unblock IP Address',
      icon: <ExclamationCircleOutlined />,
      content: `Are you sure you want to unblock IP ${ip}?`,
      okText: 'Yes, unblock',
      okType: 'danger',
      cancelText: 'No, keep blocked',
      onOk: async () => {
        try {
          await unblockIP(ip);
          message.success(`IP ${ip} has been unblocked`);
        } catch (error) {
          console.error('Error unblocking IP:', error);
          message.error(`Failed to unblock IP: ${error.message}`);
        }
      },
    });
  };
  
  // Handle API key generation
  const handleGenerateAPIKey = async (name, permissions) => {
    try {
      const newKey = await generateAPIKey(name, permissions);
      message.success('API key generated successfully');
      return newKey;
    } catch (error) {
      console.error('Error generating API key:', error);
      message.error(`Failed to generate API key: ${error.message}`);
      throw error;
    }
  };
  
  // Handle API key revocation with confirmation
  const handleRevokeAPIKey = (keyId) => {
    confirm({
      title: 'Revoke API Key',
      icon: <ExclamationCircleOutlined />,
      content: 'Are you sure you want to revoke this API key? This action cannot be undone.',
      okText: 'Yes, revoke',
      okType: 'danger',
      cancelText: 'No, keep it',
      onOk: async () => {
        try {
          await revokeAPIKey(keyId);
          message.success('API key has been revoked');
        } catch (error) {
          console.error('Error revoking API key:', error);
          message.error(`Failed to revoke API key: ${error.message}`);
        }
      },
    });
  };
  
  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return <SecurityOverview stats={securityStats} />;
      case 'api-keys':
        return <ApiKeys />;
      case 'ip-management':
        return <IpManagement />;
      case 'logs':
        return <SecurityLogs logs={securityLogs} loading={isLoading} onRefresh={loadData} onTableChange={handleTableChange} />;
      default:
        return null;
    }
  };
  
  if (isLoading && activeTab === 'overview') {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <Spin size="large" tip="Loading security data..." />
      </div>
    );
  }
  
  if (error) {
    return (
      <div style={{ padding: '24px' }}>
        <Alert
          message="Error Loading Security Data"
          description={error}
          type="error"
          showIcon
          action={
            <Button type="primary" onClick={loadData}>
              Retry
            </Button>
          }
        />
      </div>
    );
  }
  
  return (
    <div className="security-dashboard">
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>
          <SecurityScanOutlined style={{ marginRight: 12 }} />
          Security Center
        </Title>
        <Text type="secondary">
          Monitor and manage your account's security settings and activity
        </Text>
      </div>
      
      <Tabs 
        activeKey={activeTab} 
        onChange={setActiveTab}
        style={{ marginBottom: 24 }}
      >
        <TabPane
          key="overview"
          tab={
            <span>
              <SafetyCertificateOutlined />
              Overview
            </span>
          }
        />
        <TabPane
          key="api-keys"
          tab={
            <span>
              <KeyOutlined />
              API Keys
            </span>
          }
        />
        <TabPane
          key="ip-management"
          tab={
            <span>
              <LockOutlined />
              IP Management
            </span>
          }
        />
        <TabPane
          key="logs"
          tab={
            <span>
              <AuditOutlined />
              <Badge count={securityLogs.length} size="small" offset={[5, -5]}>
                <span style={{ marginLeft: 8 }}>Security Logs</span>
              </Badge>
            </span>
          }
        />
      </Tabs>
      
      <Spin spinning={loading && activeTab !== 'overview'}>
        {renderTabContent()}
      </Spin>
    </div>
  );
};

export default SecurityDashboard;
