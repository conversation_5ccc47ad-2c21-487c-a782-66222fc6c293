import { useState, useEffect } from 'react';
import { FiEye, FiCheckCircle, FiXCircle, FiTrash } from 'react-icons/fi';
import { Button } from '../ui/Button';
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from '../ui/Table';
import { Badge } from '../ui/Badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/Dialog';
import { Textarea } from '../ui/Textarea';
import { useAuth } from '../../context/AuthContext';

const statusColors = {
  approved: 'bg-green-100 text-green-700',
  denied: 'bg-red-100 text-red-700',
  working: 'bg-yellow-100 text-yellow-700',
  pending: 'bg-gray-100 text-gray-700',
};

const ScriptRequestManagement = () => {
  const [scriptRequests, setScriptRequests] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [currentRequest, setCurrentRequest] = useState(null);
  const [adminNotes, setAdminNotes] = useState('');

  const { token, isAuthenticated } = useAuth();

  const getAdminHeaders = () => ({
    'x-admin-username': localStorage.getItem('adminUsername') || '',
    'x-admin-password': localStorage.getItem('adminPassword') || '',
  });

  const fetchScriptRequests = async () => {
    setLoading(true);
    setError(null);
    if (!isAuthenticated) {
      setError('User not authenticated.');
      setLoading(false);
      return;
    }
    try {
      const response = await fetch('/.netlify/functions/security?action=script-requests', {
        headers: {
          ...getAdminHeaders(),
          'Content-Type': 'application/json',
        },
      });
      const data = await response.json();
      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch script requests');
      }
      setScriptRequests(data);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (isAuthenticated) {
      fetchScriptRequests();
    }
  }, [isAuthenticated, token]);

  useEffect(() => {
    if (scriptRequests.length > 0) {
      // Debug: print the first script request object
      // eslint-disable-next-line no-console
      console.log('First script request object:', scriptRequests[0]);
    }
  }, [scriptRequests]);

  const handleViewDetails = (request) => {
    setCurrentRequest(request);
    setAdminNotes(request.admin_notes || '');
    setIsModalOpen(true);
  };

  const handleUpdateStatus = async (requestId, newStatus) => {
    setLoading(true);
    setError(null);
    if (!isAuthenticated) {
      setError('User not authenticated.');
      setLoading(false);
      return;
    }
    try {
      const response = await fetch('/.netlify/functions/security?action=update-script-request', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          ...getAdminHeaders(),
        },
        body: JSON.stringify({ id: requestId, status: newStatus, admin_notes: adminNotes }),
      });
      const data = await response.json();
      if (!response.ok) {
        throw new Error(data.error || 'Failed to update script request status');
      }
      fetchScriptRequests();
      setIsModalOpen(false);
      setAdminNotes('');
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteRequest = async (requestId) => {
    if (!window.confirm('Are you sure you want to delete this script request?')) return;
    setLoading(true);
    setError(null);
    if (!isAuthenticated) {
      setError('User not authenticated.');
      setLoading(false);
      return;
    }
    try {
      const response = await fetch(`/.netlify/functions/security?action=delete-script-request&id=${requestId}`, {
        method: 'DELETE',
        headers: {
          ...getAdminHeaders(),
          'Content-Type': 'application/json',
        },
      });
      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || 'Failed to delete script request');
      }
      fetchScriptRequests();
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-1">Script Request Review</h1>
      <p className="text-gray-400 mb-6">Review, approve, or deny user script requests. This page is for admin moderation only.</p>
      {error && <div className="mb-4 text-red-500">{error}</div>}
      <div className="bg-white/5 rounded-lg shadow overflow-x-auto">
        <Table className="min-w-full">
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Requested By</TableHead>
              <TableHead>Description</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Date Submitted</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={6} className="text-center py-8">Loading...</TableCell>
              </TableRow>
            ) : scriptRequests.length === 0 ? (
              <TableRow>
                <TableCell colSpan={6} className="text-center py-8">No script requests found.</TableCell>
              </TableRow>
            ) : (
              scriptRequests.map((req) => (
                <TableRow key={req.id} className="hover:bg-white/10 focus-within:bg-white/10">
                  <TableCell className="font-semibold">{req.script_name}</TableCell>
                  <TableCell>{req.user_name || 'Unknown'}</TableCell>
                  <TableCell className="max-w-xs truncate" title={req.description}>{req.description}</TableCell>
                  <TableCell>
                    <span className={`px-2 py-1 rounded text-xs font-semibold ${statusColors[req.status] || statusColors['pending']}`}>{req.status || 'pending'}</span>
                  </TableCell>
                  <TableCell>{req.requested_at ? new Date(req.requested_at).toLocaleDateString() : '-'}</TableCell>
                  <TableCell>
                    <button
                      className="mr-2 p-1 rounded hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-400"
                      aria-label="View details"
                      tabIndex={0}
                      onClick={() => handleViewDetails(req)}
                      onKeyDown={(e) => e.key === 'Enter' && handleViewDetails(req)}
                    >
                      <FiEye className="inline" />
                    </button>
                    <button
                      className="p-1 rounded hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-red-400"
                      aria-label="Delete request"
                      tabIndex={0}
                      onClick={() => handleDeleteRequest(req.id)}
                      onKeyDown={(e) => e.key === 'Enter' && handleDeleteRequest(req.id)}
                    >
                      <FiTrash className="inline" />
                    </button>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
      {/* Modal for viewing and moderating request */}
      {isModalOpen && currentRequest && (
        <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
          <DialogContent className="max-w-lg">
            <DialogHeader>
              <DialogTitle>Script Request Details</DialogTitle>
              <DialogDescription>
                Review the request and take action below.
              </DialogDescription>
            </DialogHeader>
            <div className="mb-2 space-y-2">
              <div>
                <span className="font-semibold">Name:</span> {currentRequest.script_name}
              </div>
              <div>
                <span className="font-semibold">Requested By:</span> {currentRequest.user_name || 'Unknown'}
              </div>
              <div>
                <span className="font-semibold">Status:</span> <span className={`px-2 py-1 rounded text-xs font-semibold ${statusColors[currentRequest.status] || statusColors['pending']}`}>{currentRequest.status || 'pending'}</span>
              </div>
              <div>
                <span className="font-semibold">Submitted At:</span> {currentRequest.requested_at ? new Date(currentRequest.requested_at).toLocaleString() : '-'}
              </div>
              <div>
                <span className="font-semibold">Description:</span>
                <div className="whitespace-pre-wrap text-sm text-gray-300 bg-black/10 rounded p-2 mt-1 max-h-32 overflow-y-auto">{currentRequest.description || 'No description provided.'}</div>
              </div>
              <div>
                <span className="font-semibold">Admin Notes:</span>
                <Textarea
                  className="w-full mt-1"
                  value={adminNotes}
                  onChange={(e) => setAdminNotes(e.target.value)}
                  aria-label="Admin notes"
                  rows={3}
                />
              </div>
            </div>
            <DialogFooter className="flex flex-row gap-2 justify-end">
              <Button
                variant="destructive"
                onClick={() => handleUpdateStatus(currentRequest.id, 'denied')}
                aria-label="Deny request"
                tabIndex={0}
              >
                <FiXCircle className="inline mr-1" /> Deny
              </Button>
              <Button
                variant="success"
                onClick={() => handleUpdateStatus(currentRequest.id, 'approved')}
                aria-label="Approve request"
                tabIndex={0}
              >
                <FiCheckCircle className="inline mr-1" /> Approve
              </Button>
              <Button
                variant="secondary"
                onClick={() => setIsModalOpen(false)}
                aria-label="Close modal"
                tabIndex={0}
              >
                Close
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};

export default ScriptRequestManagement; 