import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>oon, FiMonitor } from 'react-icons/fi';
import { useTheme } from '../../context/ThemeContext';

const icons = {
  light: <FiSun className="h-6 w-6" />, 
  dark: <FiMoon className="h-6 w-6" />, 
  system: <FiMonitor className="h-6 w-6" />
};

const labels = {
  light: 'Switch to dark mode',
  dark: 'Switch to system mode',
  system: 'Switch to light mode',
};

const nextTheme = {
  light: 'dark',
  dark: 'system',
  system: 'light',
};

const ThemeToggle = () => {
  const { theme, toggleTheme, isChangingTheme } = useTheme();
  const current = theme || 'system';
  return (
    <button
      className="rounded-full p-2 bg-white/20 dark:bg-black/20 hover:bg-white/40 dark:hover:bg-black/40 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-400"
      aria-label={labels[current]}
      title={labels[current]}
      disabled={isChangingTheme}
      onClick={() => toggleTheme(nextTheme[current])}
      tabIndex={0}
    >
      <span className="sr-only">{labels[current]}</span>
      <span className="transition-all duration-200">{icons[current]}</span>
    </button>
  );
};

export default ThemeToggle; 