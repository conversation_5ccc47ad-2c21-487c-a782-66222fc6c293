import { Routes, Route, Navigate } from 'react-router-dom';
import LazyAdminDashboard from './LazyAdminDashboard';

const AdminRoutes = () => {
  return (
    <Routes>
      <Route path="/" element={<LazyAdminDashboard />} />
      {/* Fallback: redirect any unknown admin route to dashboard */}
      <Route path="*" element={<Navigate to="/admin" replace />} />
    </Routes>
  );
};

export default AdminRoutes; 