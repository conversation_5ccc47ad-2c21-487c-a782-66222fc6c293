import React from 'react';
import { cn } from '../../lib/utils';

const StatCard = ({ title, value, icon, accent = 'from-blue-500 to-purple-500' }) => (
  <div className={cn(
    'relative flex items-center gap-4 p-6 rounded-2xl shadow-xl bg-white/10 dark:bg-white/5 backdrop-blur-lg border border-white/10',
    'transition-all duration-200 group hover:scale-[1.03] hover:shadow-2xl'
  )}>
    <div className={cn(
      'flex items-center justify-center w-12 h-12 rounded-xl bg-gradient-to-br',
      accent
    )}>
      {icon}
    </div>
    <div>
      <div className="text-sm text-white/70 font-medium mb-1">{title}</div>
      <div className="text-2xl font-extrabold text-white drop-shadow-lg">{value}</div>
    </div>
  </div>
);

export default StatCard; 