import { useState, useEffect } from 'react';
import {
  LineChart, Line, AreaChart, Area, BarChart, Bar, PieChart, Pie, Cell,
  XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer
} from 'recharts';
import { FiDownload, FiCalendar, FiTrendingUp, FiTrendingDown, FiUsers, FiKey, FiDollarSign, FiActivity } from 'react-icons/fi';
import UserActivityHeatmap from './UserActivityHeatmap';
import ExportManager from './ExportManager';

// Helper to get admin credentials from localStorage
const getAdminHeaders = () => {
  const username = localStorage.getItem('adminUsername');
  const password = localStorage.getItem('adminPassword');
  return {
    'x-admin-username': username || '',
    'x-admin-password': password || '',
  };
};

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D'];

const AnalyticsDashboard = () => {
  const [timeRange, setTimeRange] = useState('7d');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showExportModal, setShowExportModal] = useState(false);
  
  // Data states
  const [keyTrends, setKeyTrends] = useState([]);
  const [userActivity, setUserActivity] = useState([]);
  const [revenueData, setRevenueData] = useState([]);
  const [keyDistribution, setKeyDistribution] = useState([]);
  const [summaryStats, setSummaryStats] = useState({
    totalKeys: 0,
    activeKeys: 0,
    totalUsers: 0,
    totalRevenue: 0,
    growthRate: 0
  });

  useEffect(() => {
    fetchAnalyticsData();
  }, [timeRange]);

  const fetchAnalyticsData = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // Fetch key generation trends
      const trendsResponse = await fetch(`/.netlify/functions/analytics?action=key-trends&range=${timeRange}`, {
        headers: getAdminHeaders()
      });
      
      if (trendsResponse.ok) {
        const trendsData = await trendsResponse.json();
        setKeyTrends(trendsData);
      }

      // Fetch user activity
      const activityResponse = await fetch(`/.netlify/functions/analytics?action=user-activity&range=${timeRange}`, {
        headers: getAdminHeaders()
      });
      
      if (activityResponse.ok) {
        const activityData = await activityResponse.json();
        setUserActivity(activityData);
      }

      // Fetch revenue data
      const revenueResponse = await fetch(`/.netlify/functions/analytics?action=revenue&range=${timeRange}`, {
        headers: getAdminHeaders()
      });
      
      if (revenueResponse.ok) {
        const revenueData = await revenueResponse.json();
        setRevenueData(revenueData);
      }

      // Fetch key distribution
      const distributionResponse = await fetch(`/.netlify/functions/analytics?action=key-distribution`, {
        headers: getAdminHeaders()
      });
      
      if (distributionResponse.ok) {
        const distributionData = await distributionResponse.json();
        setKeyDistribution(distributionData);
      }

      // Fetch summary stats
      const summaryResponse = await fetch(`/.netlify/functions/analytics?action=summary&range=${timeRange}`, {
        headers: getAdminHeaders()
      });
      
      if (summaryResponse.ok) {
        const summaryData = await summaryResponse.json();
        setSummaryStats(summaryData);
      }

    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleExport = () => {
    setShowExportModal(true);
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatNumber = (num) => {
    return new Intl.NumberFormat('en-US').format(num);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-400">Loading analytics...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-red-400">Error: {error}</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with controls */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold text-white">Analytics Dashboard</h2>
          <p className="text-gray-400">Comprehensive insights into your key system performance</p>
        </div>
        <div className="flex items-center gap-3">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="bg-gray-800 border border-gray-700 text-white rounded-lg px-3 py-2 text-sm"
          >
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
            <option value="90d">Last 90 days</option>
            <option value="1y">Last year</option>
          </select>
          <div className="flex gap-2">
            <button
              onClick={handleExport}
              className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded-lg text-sm transition-colors"
            >
              <FiDownload className="h-4 w-4" />
              Export Data
            </button>
          </div>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <div className="bg-gray-800 border border-gray-700 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-400">Total Keys</p>
              <p className="text-2xl font-bold text-white">{formatNumber(summaryStats.totalKeys)}</p>
            </div>
            <FiKey className="h-8 w-8 text-blue-500" />
          </div>
        </div>
        
        <div className="bg-gray-800 border border-gray-700 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-400">Active Keys</p>
              <p className="text-2xl font-bold text-white">{formatNumber(summaryStats.activeKeys)}</p>
            </div>
            <FiActivity className="h-8 w-8 text-green-500" />
          </div>
        </div>
        
        <div className="bg-gray-800 border border-gray-700 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-400">Total Users</p>
              <p className="text-2xl font-bold text-white">{formatNumber(summaryStats.totalUsers)}</p>
            </div>
            <FiUsers className="h-8 w-8 text-purple-500" />
          </div>
        </div>
        
        <div className="bg-gray-800 border border-gray-700 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-400">Total Revenue</p>
              <p className="text-2xl font-bold text-white">{formatCurrency(summaryStats.totalRevenue)}</p>
            </div>
            <FiDollarSign className="h-8 w-8 text-yellow-500" />
          </div>
        </div>
        
        <div className="bg-gray-800 border border-gray-700 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-400">Growth Rate</p>
              <p className={`text-2xl font-bold ${summaryStats.growthRate >= 0 ? 'text-green-500' : 'text-red-500'}`}>
                {summaryStats.growthRate >= 0 ? '+' : ''}{summaryStats.growthRate}%
              </p>
            </div>
            {summaryStats.growthRate >= 0 ? (
              <FiTrendingUp className="h-8 w-8 text-green-500" />
            ) : (
              <FiTrendingDown className="h-8 w-8 text-red-500" />
            )}
          </div>
        </div>
      </div>

      {/* Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Key Generation Trends */}
        <div className="bg-gray-800 border border-gray-700 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-white mb-4">Key Generation Trends</h3>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={keyTrends}>
              <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
              <XAxis dataKey="date" stroke="#9CA3AF" />
              <YAxis stroke="#9CA3AF" />
              <Tooltip 
                contentStyle={{ 
                  backgroundColor: '#1F2937', 
                  border: '1px solid #374151',
                  borderRadius: '8px'
                }}
              />
              <Legend />
              <Line 
                type="monotone" 
                dataKey="keys" 
                stroke="#3B82F6" 
                strokeWidth={2}
                dot={{ fill: '#3B82F6', strokeWidth: 2, r: 4 }}
              />
            </LineChart>
          </ResponsiveContainer>
        </div>

        {/* User Activity */}
        <div className="bg-gray-800 border border-gray-700 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-white mb-4">User Activity</h3>
          <ResponsiveContainer width="100%" height={300}>
            <AreaChart data={userActivity}>
              <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
              <XAxis dataKey="hour" stroke="#9CA3AF" />
              <YAxis stroke="#9CA3AF" />
              <Tooltip 
                contentStyle={{ 
                  backgroundColor: '#1F2937', 
                  border: '1px solid #374151',
                  borderRadius: '8px'
                }}
              />
              <Area 
                type="monotone" 
                dataKey="users" 
                stroke="#10B981" 
                fill="#10B981" 
                fillOpacity={0.3}
              />
            </AreaChart>
          </ResponsiveContainer>
        </div>

        {/* Revenue Tracking */}
        <div className="bg-gray-800 border border-gray-700 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-white mb-4">Revenue Tracking</h3>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={revenueData}>
              <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
              <XAxis dataKey="month" stroke="#9CA3AF" />
              <YAxis stroke="#9CA3AF" />
              <Tooltip 
                contentStyle={{ 
                  backgroundColor: '#1F2937', 
                  border: '1px solid #374151',
                  borderRadius: '8px'
                }}
                formatter={(value) => [formatCurrency(value), 'Revenue']}
              />
              <Bar dataKey="revenue" fill="#F59E0B" />
            </BarChart>
          </ResponsiveContainer>
        </div>

        {/* Key Distribution */}
        <div className="bg-gray-800 border border-gray-700 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-white mb-4">Key Distribution</h3>
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={keyDistribution}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
              >
                {keyDistribution.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip 
                contentStyle={{ 
                  backgroundColor: '#1F2937', 
                  border: '1px solid #374151',
                  borderRadius: '8px'
                }}
              />
            </PieChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* User Activity Heatmap */}
      <UserActivityHeatmap />

      {/* Detailed Stats Table */}
      <div className="bg-gray-800 border border-gray-700 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-white mb-4">Detailed Statistics</h3>
        <div className="overflow-x-auto">
          <table className="w-full text-sm">
            <thead>
              <tr className="border-b border-gray-700">
                <th className="text-left py-2 text-gray-300">Metric</th>
                <th className="text-right py-2 text-gray-300">Today</th>
                <th className="text-right py-2 text-gray-300">This Week</th>
                <th className="text-right py-2 text-gray-300">This Month</th>
                <th className="text-right py-2 text-gray-300">Total</th>
              </tr>
            </thead>
            <tbody>
              <tr className="border-b border-gray-700">
                <td className="py-2 text-gray-200">Keys Generated</td>
                <td className="text-right py-2 text-gray-200">{formatNumber(summaryStats.todayKeys || 0)}</td>
                <td className="text-right py-2 text-gray-200">{formatNumber(summaryStats.weekKeys || 0)}</td>
                <td className="text-right py-2 text-gray-200">{formatNumber(summaryStats.monthKeys || 0)}</td>
                <td className="text-right py-2 text-gray-200">{formatNumber(summaryStats.totalKeys)}</td>
              </tr>
              <tr className="border-b border-gray-700">
                <td className="py-2 text-gray-200">New Users</td>
                <td className="text-right py-2 text-gray-200">{formatNumber(summaryStats.todayUsers || 0)}</td>
                <td className="text-right py-2 text-gray-200">{formatNumber(summaryStats.weekUsers || 0)}</td>
                <td className="text-right py-2 text-gray-200">{formatNumber(summaryStats.monthUsers || 0)}</td>
                <td className="text-right py-2 text-gray-200">{formatNumber(summaryStats.totalUsers)}</td>
              </tr>
              <tr>
                <td className="py-2 text-gray-200">Revenue</td>
                <td className="text-right py-2 text-gray-200">{formatCurrency(summaryStats.todayRevenue || 0)}</td>
                <td className="text-right py-2 text-gray-200">{formatCurrency(summaryStats.weekRevenue || 0)}</td>
                <td className="text-right py-2 text-gray-200">{formatCurrency(summaryStats.monthRevenue || 0)}</td>
                <td className="text-right py-2 text-gray-200">{formatCurrency(summaryStats.totalRevenue)}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      
      {/* Export Modal */}
      {showExportModal && (
        <ExportManager onClose={() => setShowExportModal(false)} />
      )}
    </div>
  );
};

export default AnalyticsDashboard; 