import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  FiShield, 
  FiAlertTriangle, 
  FiActivity, 
  FiUsers, 
  FiEye,
  FiSettings,
  FiRefreshCw,
  FiDownload,
  FiTrash2
} from 'react-icons/fi';
import { Card } from '../ui/Card';
import { Button } from '../ui/Button';
import { securityConfig, initializeSecurityConfig } from '../../utils/securityConfig';

const SecurityDashboard = () => {
  const [securityLogs, setSecurityLogs] = useState([]);
  const [config, setConfig] = useState({});
  const [stats, setStats] = useState({
    totalEvents: 0,
    botDetections: 0,
    adminBypasses: 0,
    securityViolations: 0
  });

  // Load security data
  useEffect(() => {
    // Initialize security config first, then load data
    initializeSecurityConfig().then(() => {
      loadSecurityData();
      const interval = setInterval(loadSecurityData, 5000); // Refresh every 5 seconds
      return () => clearInterval(interval);
    });
  }, []);

  const loadSecurityData = () => {
    try {
      const logs = securityConfig.getSecurityLogs();
      const currentConfig = securityConfig.getConfig();

      setSecurityLogs(logs.slice(-50)); // Show last 50 events
      setConfig(currentConfig);

      // Calculate stats
      const stats = {
        totalEvents: logs.length,
        botDetections: logs.filter(log => log.event === 'BOT_DETECTION_BLOCK').length,
        adminBypasses: logs.filter(log => log.event === 'BOT_DETECTION_BYPASSED').length,
        securityViolations: logs.filter(log => log.event.includes('VIOLATION')).length
      };
      setStats(stats);
    } catch (error) {
      console.warn('Failed to load security data:', error);
    }
  };

  const handleConfigUpdate = (key, value) => {
    securityConfig.updateConfig({ [key]: value });
    setConfig(prev => ({ ...prev, [key]: value }));
  };

  const clearLogs = () => {
    securityConfig.clearSecurityLogs();
    loadSecurityData();
  };

  const exportLogs = () => {
    const dataStr = JSON.stringify(securityLogs, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `security-logs-${new Date().toISOString().split('T')[0]}.json`;
    link.click();
    URL.revokeObjectURL(url);
  };

  const getEventIcon = (event) => {
    if (event.includes('BOT_DETECTION')) return '🤖';
    if (event.includes('ADMIN') || event.includes('BYPASS')) return '👑';
    if (event.includes('VIOLATION')) return '🚨';
    if (event.includes('SYSTEM')) return '⚙️';
    return '📊';
  };

  const getEventColor = (event) => {
    if (event.includes('BOT_DETECTION_BLOCK') || event.includes('VIOLATION')) return 'text-red-400';
    if (event.includes('BYPASS') || event.includes('ADMIN')) return 'text-blue-400';
    if (event.includes('SYSTEM')) return 'text-green-400';
    return 'text-yellow-400';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white mb-2">Security Dashboard</h2>
          <p className="text-white/70">Real-time security monitoring and configuration</p>
        </div>
        <div className="flex gap-3">
          <Button
            onClick={loadSecurityData}
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
          >
            <FiRefreshCw className="h-4 w-4" />
            Refresh
          </Button>
          <Button
            onClick={exportLogs}
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
          >
            <FiDownload className="h-4 w-4" />
            Export
          </Button>
          <Button
            onClick={clearLogs}
            variant="outline"
            size="sm"
            className="flex items-center gap-2 text-red-400 border-red-400/30 hover:bg-red-400/10"
          >
            <FiTrash2 className="h-4 w-4" />
            Clear
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="p-6 bg-gradient-to-br from-blue-500/10 to-purple-500/10 border-blue-500/20">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-white/70 text-sm">Total Events</p>
              <p className="text-2xl font-bold text-white">{stats.totalEvents}</p>
            </div>
            <FiActivity className="h-8 w-8 text-blue-400" />
          </div>
        </Card>

        <Card className="p-6 bg-gradient-to-br from-red-500/10 to-orange-500/10 border-red-500/20">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-white/70 text-sm">Bot Detections</p>
              <p className="text-2xl font-bold text-white">{stats.botDetections}</p>
            </div>
            <FiAlertTriangle className="h-8 w-8 text-red-400" />
          </div>
        </Card>

        <Card className="p-6 bg-gradient-to-br from-green-500/10 to-emerald-500/10 border-green-500/20">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-white/70 text-sm">Admin Bypasses</p>
              <p className="text-2xl font-bold text-white">{stats.adminBypasses}</p>
            </div>
            <FiUsers className="h-8 w-8 text-green-400" />
          </div>
        </Card>

        <Card className="p-6 bg-gradient-to-br from-yellow-500/10 to-orange-500/10 border-yellow-500/20">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-white/70 text-sm">Violations</p>
              <p className="text-2xl font-bold text-white">{stats.securityViolations}</p>
            </div>
            <FiShield className="h-8 w-8 text-yellow-400" />
          </div>
        </Card>
      </div>

      {/* Configuration Panel */}
      <Card className="p-6">
        <div className="flex items-center gap-3 mb-6">
          <FiSettings className="h-5 w-5 text-blue-400" />
          <h3 className="text-xl font-semibold text-white">Security Configuration</h3>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div>
            <label className="block text-white/70 text-sm mb-2">Security Level</label>
            <select
              value={config.securityLevel || 'HIGH'}
              onChange={(e) => {
                securityConfig.applySecurityLevel(e.target.value);
                loadSecurityData();
              }}
              className="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white"
            >
              <option value="LOW">Low</option>
              <option value="MEDIUM">Medium</option>
              <option value="HIGH">High</option>
              <option value="MAXIMUM">Maximum</option>
            </select>
          </div>

          <div>
            <label className="block text-white/70 text-sm mb-2">Log Level</label>
            <select
              value={config.logLevel || 'DETAILED'}
              onChange={(e) => handleConfigUpdate('logLevel', e.target.value)}
              className="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white"
            >
              <option value="MINIMAL">Minimal</option>
              <option value="NORMAL">Normal</option>
              <option value="DETAILED">Detailed</option>
              <option value="VERBOSE">Verbose</option>
            </select>
          </div>

          <div className="flex items-center gap-3">
            <input
              type="checkbox"
              id="adminBypass"
              checked={config.adminBypass || false}
              onChange={(e) => handleConfigUpdate('adminBypass', e.target.checked)}
              className="w-4 h-4 text-blue-600 bg-white/10 border-white/20 rounded"
            />
            <label htmlFor="adminBypass" className="text-white/70 text-sm">
              Enable Admin Bypass
            </label>
          </div>
        </div>
      </Card>

      {/* Security Logs */}
      <Card className="p-6">
        <div className="flex items-center gap-3 mb-6">
          <FiEye className="h-5 w-5 text-green-400" />
          <h3 className="text-xl font-semibold text-white">Recent Security Events</h3>
          <span className="text-white/50 text-sm">({securityLogs.length} events)</span>
        </div>
        
        <div className="space-y-3 max-h-96 overflow-y-auto">
          {securityLogs.length === 0 ? (
            <p className="text-white/50 text-center py-8">No security events recorded</p>
          ) : (
            securityLogs.slice().reverse().map((log, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="flex items-start gap-3 p-3 bg-white/5 rounded-lg border border-white/10"
              >
                <span className="text-lg">{getEventIcon(log.event)}</span>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <span className={`font-medium ${getEventColor(log.event)}`}>
                      {log.event.replace(/_/g, ' ')}
                    </span>
                    <span className="text-white/50 text-xs">
                      {new Date(log.timestamp).toLocaleTimeString()}
                    </span>
                  </div>
                  <p className="text-white/70 text-sm">
                    User: {log.userType || 'unknown'} | 
                    Level: {log.securityLevel || 'N/A'}
                    {log.confidence && ` | Confidence: ${Math.round(log.confidence * 100)}%`}
                  </p>
                </div>
              </motion.div>
            ))
          )}
        </div>
      </Card>
    </div>
  );
};

export default SecurityDashboard;
