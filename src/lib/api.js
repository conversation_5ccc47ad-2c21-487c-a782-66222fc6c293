import { getGameThumbnail } from './robloxAPI';

// Real scripts API that connects to Netlify functions
export const scriptsAPI = {
  list: async () => {
    try {
      const response = await fetch('/.netlify/functions/scripts');
      if (!response.ok) {
        throw new Error(`Failed to fetch scripts: ${response.status}`);
      }
      const data = await response.json();
      
      // Transform the data to match the expected format
      return data.map(script => ({
        id: script.id,
        name: script.name,
        description: script.description,
        category: script.category || 'other',
        views: script.views?.toString() || '0',
        rating: parseFloat(script.rating) || 0,
        rating_count: script.rating_count || 0,
        updated_at: script.updated_at,
        tags: JSON.stringify(script.tags || []),
        executor: script.executor || 'Synapse X',
        version: script.version || '1.0.0',
        content: script.content || '-- Script content not available',
        gameLink: 'https://www.roblox.com/games/126884695634066/Grow-a-Gardene',
        thumbnailUrl: getGameThumbnail('126884695634066'),
      }));
    } catch (error) {
      console.error('Error fetching scripts:', error);
      // Return empty array if there's an error
      return [];
    }
  },

  get: async (id) => {
    try {
      const response = await fetch(`/.netlify/functions/scripts?id=${id}`);
      if (!response.ok) {
        throw new Error(`Failed to fetch script: ${response.status}`);
      }
      const script = await response.json();
      
      return {
        id: script.id,
        name: script.name,
        description: script.description,
        category: script.category || 'other',
        views: script.views?.toString() || '0',
        rating: parseFloat(script.rating) || 0,
        rating_count: script.rating_count || 0,
        updated_at: script.updated_at,
        tags: JSON.stringify(script.tags || []),
        executor: script.executor || 'Synapse X',
        version: script.version || '1.0.0',
        content: script.content || '-- Script content not available',
        gameLink: 'https://www.roblox.com/games/126884695634066/Grow-a-Gardene',
        thumbnailUrl: getGameThumbnail('126884695634066'),
      };
    } catch (error) {
      console.error('Error fetching script:', error);
      throw error;
    }
  },

  rate: async (scriptId, userName, rating) => {
    try {
      const response = await fetch('/.netlify/functions/scripts?action=rate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          script_id: scriptId,
          user_name: userName,
          rating: parseInt(rating)
        }),
      });
      
      if (!response.ok) {
        throw new Error(`Failed to rate script: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Error rating script:', error);
      throw error;
    }
  },

  getRating: async (scriptId, userName) => {
    try {
      const response = await fetch(`/.netlify/functions/scripts?action=rate&script_id=${scriptId}&user_name=${userName}`);
      if (!response.ok) {
        throw new Error(`Failed to get rating: ${response.status}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error getting rating:', error);
      return { rating: null };
    }
  },

  getUpdateLogs: async (scriptId) => {
    try {
      const response = await fetch(`/.netlify/functions/scripts?action=update-logs&script_id=${scriptId}`);
      if (!response.ok) {
        throw new Error(`Failed to get update logs: ${response.status}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error getting update logs:', error);
      return [];
    }
  },
}; 