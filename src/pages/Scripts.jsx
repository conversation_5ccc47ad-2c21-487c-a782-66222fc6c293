import { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Link } from 'react-router-dom';
import { 
  FiSearch, 
  FiFilter, 
  FiDownload, 
  FiStar, 
  FiClock, 
  FiCopy, 
  FiCheck,
  FiCode,
  FiInfo,
  FiExternalLink,
  FiLoader,
  FiAlertCircle,
  FiFileText,
  FiEye,
  FiMonitor
} from 'react-icons/fi';
import { scriptsAPI } from '../lib/api';
import { Badge } from '../components/ui/Badge';
import { Button } from '../components/ui/Button';
import { Card } from '../components/ui/Card';
import UpdateLogModal from '../components/scripts/UpdateLogModal';
import ExecutorSupport from '../components/scripts/ExecutorSupport';
import ScriptRating from '../components/scripts/ScriptRating';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '../components/ui/Tabs';
import { CardSkeleton } from '../components/ui/LoadingSkeleton';
import { cn } from '../lib/utils';

// Categories for filtering
const CATEGORIES = [
  { id: 'all', name: 'All Scripts' },
  { id: 'game', name: 'Games' },
  { id: 'utility', name: 'Utilities' },
  { id: 'other', name: 'Other' },
];

// Helper function to format date
const formatDate = (dateString) => {
  return 'N/A'; // Always N/A as per design
};
// Sample script data structure for reference
// Actual scripts will be loaded from the API
const SAMPLE_SCRIPT = {
  id: 'sample-id',
  name: 'Sample Script',
  description: 'This is a sample script',
  category: 'game',
  views: '1K+',
  rating: 4.5,
  updated_at: new Date().toISOString(),
  tags: JSON.stringify(['Feature 1', 'Feature 2']),
  executor: 'Synapse X',
  version: '1.0.0',
  content: '-- Sample script content'
};

const Scripts = () => {
  const [scripts, setScripts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState('script');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedScript, setSelectedScript] = useState(null);
  const [showUpdateLog, setShowUpdateLog] = useState(false);
  const [copied, setCopied] = useState(false);
  const [copiedId, setCopiedId] = useState(null);
  const [selectedExecutor, setSelectedExecutor] = useState('all');
  const [showExecutorSupport, setShowExecutorSupport] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState('all');

  // Fetch scripts from API
  useEffect(() => {
    const fetchScripts = async () => {
      try {
        setLoading(true);
        setError(null);
        
        console.log('Fetching scripts from API...');
        const data = await scriptsAPI.list();
        console.log('Fetched scripts:', data);
        
        // Ensure scripts is always an array
        setScripts(Array.isArray(data) ? data : []);
      } catch (err) {
        console.error('Error fetching scripts:', err);
        setError(err.message || 'Failed to load scripts');
      } finally {
        setLoading(false);
      }
    };

    fetchScripts();
  }, []);

  const filteredScripts = useMemo(() => {
    if (!scripts || !Array.isArray(scripts)) return [];

    return scripts.filter(script => {
      if (!script || typeof script !== 'object') return false;

      const name = typeof script.name === 'string' ? script.name : '';
      const description = typeof script.description === 'string' ? script.description : '';

      const matchesSearch = searchQuery === '' || 
        name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        description.toLowerCase().includes(searchQuery.toLowerCase());

      return matchesSearch;
    });
  }, [scripts, searchQuery]);

  const handleCopyToClipboard = useCallback((text, id) => {
    if (typeof text !== 'string' || !navigator.clipboard || !text.trim()) {
      console.error('Invalid text to copy or Clipboard API not available');
      return;
    }

    navigator.clipboard.writeText(text.trim())
      .then(() => {
        setCopied(true);
        setCopiedId(id);
        const timer = setTimeout(() => {
          setCopied(false);
          setCopiedId(null);
        }, 2000);
        return () => clearTimeout(timer);
      })
      .catch(err => {
        console.error('Failed to copy text:', err);
      });
  }, []);

  const handleViewUpdateLog = useCallback((script) => {
    setSelectedScript(script);
    setShowUpdateLog(true);
  }, []);

  const handleCloseUpdateLog = useCallback(() => {
    setShowUpdateLog(false);
  }, []);

  const handleFilterChange = useCallback((key, value) => {
    // Ensure value is a string before attempting to use it
    if (typeof value !== 'string') {
      console.warn(`handleFilterChange received non-string value for ${key}:`, value);
      return; // Exit if value is not a string
    }
    if (key === 'category') {
      setSelectedCategory(value);
    } else if (key === 'executor') {
      setSelectedExecutor(value);
    }
  }, []);

  const handleSearchChange = useCallback((e) => {
    setSearchQuery(e.target.value);
  }, []);

  const handleReloadPage = () => {
    window.location.reload();
  };



  if (loading) {
    return (
      <div className="min-h-screen pt-16 bg-gradient-to-br from-gray-50 via-blue-50/30 to-purple-50/30 dark:from-gray-900 dark:via-blue-900/20 dark:to-purple-900/20 relative overflow-hidden">
        {/* Background effects */}
        <div className="absolute inset-0 bg-grid-pattern opacity-5 dark:opacity-10"></div>
        <motion.div
          className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-r from-blue-400/20 to-purple-400/20 rounded-full blur-xl"
          animate={{
            y: [0, -20, 0],
            x: [0, 10, 0],
          }}
          transition={{
            duration: 6,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />

        <div className="container mx-auto px-4 py-8 relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <div className="text-center mb-8">
              <h1 className="text-4xl font-bold text-foreground mb-4">Loading Scripts</h1>
              <p className="text-foreground/80">Please wait while we load the latest scripts...</p>
            </div>

            {/* Loading skeleton grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {Array.from({ length: 6 }).map((_, index) => (
                <CardSkeleton
                  key={index}
                  showImage={true}
                  showBadge={true}
                  className="h-64"
                />
              ))}
            </div>
          </motion.div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen pt-16 bg-gradient-to-br from-gray-50 via-red-50/30 to-orange-50/30 dark:from-gray-900 dark:via-red-900/20 dark:to-orange-900/20 relative overflow-hidden">
        {/* Background effects */}
        <div className="absolute inset-0 bg-grid-pattern opacity-5 dark:opacity-10"></div>
        <motion.div
          className="absolute bottom-20 right-10 w-24 h-24 bg-gradient-to-r from-red-400/20 to-orange-400/20 rounded-full blur-xl"
          animate={{
            y: [0, 15, 0],
            x: [0, -15, 0],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />

        <div className="container mx-auto px-4 py-8 flex flex-col items-center justify-center min-h-[60vh] text-center">
          <motion.div
            className="p-8 bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl shadow-2xl max-w-md"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <FiAlertCircle className="h-12 w-12 text-red-500 mb-4 mx-auto" aria-hidden="true" />
            <h2 className="text-xl font-semibold text-foreground mb-2">Error Loading Scripts</h2>
            <p className="text-foreground/80 mb-6" aria-live="assertive">{error}</p>
            <Button
              variant="outline"
              onClick={handleReloadPage}
              className="bg-white/10 backdrop-blur-sm border-white/20 hover:bg-white/20 transition-all duration-300"
              ariaLabel="Reload page to try again"
            >
              <FiLoader className="mr-2 h-4 w-4" />
              Reload Page
            </Button>
          </motion.div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen pt-16 bg-gradient-to-br from-gray-50 via-blue-50/30 to-purple-50/30 dark:from-gray-900 dark:via-blue-900/20 dark:to-purple-900/20 relative overflow-hidden">
      {/* Background effects */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5 dark:opacity-10"></div>
      <motion.div
        className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-r from-blue-400/20 to-purple-400/20 rounded-full blur-xl"
        animate={{
          y: [0, -20, 0],
          x: [0, 10, 0],
        }}
        transition={{
          duration: 6,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />
      <motion.div
        className="absolute bottom-20 right-10 w-24 h-24 bg-gradient-to-r from-purple-400/20 to-pink-400/20 rounded-full blur-xl"
        animate={{
          y: [0, 15, 0],
          x: [0, -15, 0],
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 1
        }}
      />
      <motion.div
        className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-40 h-40 bg-gradient-to-r from-green-400/10 to-blue-400/10 rounded-full blur-2xl"
        animate={{
          scale: [1, 1.2, 1],
          opacity: [0.3, 0.5, 0.3],
        }}
        transition={{
          duration: 10,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 2
        }}
      />

      <div className="container relative mx-auto px-4 py-12 md:py-16">
        {/* Header */}
        <motion.div
          className="text-center mb-12"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <motion.div
            className="inline-block px-6 py-3 mb-6 rounded-full bg-gradient-to-r from-blue-500/10 to-purple-500/10 backdrop-blur-sm border border-blue-500/20 text-primary text-sm font-semibold shadow-lg"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <span className="flex items-center">
              <FiCode className="mr-2 h-4 w-4" />
              Script Library
            </span>
          </motion.div>

          <motion.h1
            className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 bg-clip-text text-transparent leading-tight"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            Project Madara Library
          </motion.h1>
          <motion.p
            className="text-lg text-foreground/80 max-w-2xl mx-auto leading-relaxed"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            Explore a vast collection of powerful and secure scripts designed to enhance your Roblox gaming experience. Find the perfect tools to elevate your gameplay.
          </motion.p>
        </motion.div>

        {/* Search and Filter Section */}
        <motion.div
          className="flex flex-col md:flex-row justify-center items-center gap-6 mb-12"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.5 }}
        >
          <motion.div
            className="relative w-full max-w-xl"
            whileHover={{ scale: 1.02 }}
            transition={{ duration: 0.2 }}
          >
            <input
              type="text"
              placeholder="Search scripts..."
              value={searchQuery}
              onChange={handleSearchChange}
              className="w-full pl-12 pr-6 py-4 bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl shadow-lg focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-300 text-foreground placeholder:text-foreground/50"
              aria-label="Search scripts"
            />
            <FiSearch className="absolute left-4 top-1/2 -translate-y-1/2 text-foreground/60 h-5 w-5" aria-hidden="true" />
            <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-blue-500/10 to-purple-500/10 opacity-0 hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
          </motion.div>

          {/* Executor Support Button */}
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Button
              variant="outline"
              onClick={() => setShowExecutorSupport(prev => !prev)}
              className="py-4 px-8 text-sm rounded-xl bg-white/10 backdrop-blur-sm border border-white/20 text-foreground hover:bg-white/20 shadow-lg transition-all duration-300 group"
              ariaLabel="Open executor support information"
              tabIndex={0}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === 'Space') {
                  setShowExecutorSupport(prev => !prev);
                }
              }}
            >
              <FiInfo className="mr-2 h-5 w-5 text-blue-400 group-hover:text-blue-300 transition-colors" aria-hidden="true" />
              Executor Support
            </Button>
          </motion.div>
        </motion.div>

        {/* Script List & Content */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3 gap-8"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.6 }}
        >
          {filteredScripts
            .filter(script => selectedCategory === 'all' || script.category === selectedCategory)
            .map((script, index) => (
              <motion.div
                key={script.id}
                layout
                initial={{ opacity: 0, y: 50 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className="w-full"
                whileHover={{ y: -5 }}
              >
                <div className="flex flex-col h-full overflow-hidden p-6 bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl shadow-2xl group hover:border-blue-500/50 transition-all duration-300 relative">
                  {/* Animated border */}
                  <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-pink-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm"></div>
                  <div className="relative z-10">
                    {/* Script Icon */}
                    <motion.div
                      className="flex items-center justify-center h-32 bg-gradient-to-br from-blue-500/20 to-purple-500/20 rounded-xl mb-6 relative overflow-hidden shadow-inner border border-white/10"
                      whileHover={{ scale: 1.05 }}
                      transition={{ duration: 0.3 }}
                      aria-hidden="true"
                    >
                      <motion.div
                        className="absolute inset-0 bg-gradient-to-r from-blue-400/10 to-purple-400/10"
                        animate={{
                          opacity: [0.5, 0.8, 0.5],
                        }}
                        transition={{
                          duration: 3,
                          repeat: Infinity,
                          ease: "easeInOut"
                        }}
                      />
                      <FiCode className="text-blue-400 w-16 h-16 opacity-60 group-hover:opacity-80 transition-opacity duration-300 relative z-10" />

                      {/* Floating sparkles */}
                      <motion.div
                        className="absolute top-2 right-2 w-2 h-2 bg-blue-400 rounded-full opacity-60"
                        animate={{
                          scale: [0, 1, 0],
                          opacity: [0, 1, 0],
                        }}
                        transition={{
                          duration: 2,
                          repeat: Infinity,
                          delay: 0.5
                        }}
                      />
                      <motion.div
                        className="absolute bottom-3 left-3 w-1.5 h-1.5 bg-purple-400 rounded-full opacity-60"
                        animate={{
                          scale: [0, 1, 0],
                          opacity: [0, 1, 0],
                        }}
                        transition={{
                          duration: 2,
                          repeat: Infinity,
                          delay: 1.2
                        }}
                      />
                    </motion.div>
                    {/* Script Title and Description */}
                    <motion.h2
                      className="text-xl font-bold text-foreground mb-2 group-hover:text-blue-400 transition-colors duration-300"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: 0.2 }}
                    >
                      {script.name}
                    </motion.h2>
                    <motion.p
                      className="text-foreground/70 text-sm mb-4 line-clamp-2 leading-relaxed"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: 0.3 }}
                    >
                      {script.description}
                    </motion.p>
                
                    {/* Enhanced Metadata */}
                    <div className="space-y-4 mb-6">
                      {/* Views and Rating Row */}
                      <div className="flex items-center justify-between">
                        <motion.div
                          className="flex items-center text-foreground/60 text-sm"
                          initial={{ opacity: 0, x: -10 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: 0.4 }}
                        >
                          <FiEye className="mr-2 h-4 w-4 text-blue-400" aria-hidden="true" />
                          <span>{script.views || "N/A"} Views</span>
                        </motion.div>

                        <motion.div
                          initial={{ opacity: 0, x: 10 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: 0.5 }}
                        >
                          <ScriptRating
                            scriptId={script.id}
                            scriptName={script.name}
                            currentRating={script.rating || 0}
                            ratingCount={script.rating_count || 0}
                            onRatingChange={(newRating) => {
                              setScripts(prevScripts =>
                                prevScripts.map(s =>
                                  s.id === script.id
                                    ? { ...s, rating: newRating, rating_count: (s.rating_count || 0) + 1 }
                                    : s
                                )
                              );
                            }}
                          />
                        </motion.div>
                      </div>

                      {/* Tags */}
                      {script.tags && JSON.parse(script.tags).length > 0 && (
                        <motion.div
                          className="flex flex-wrap gap-2"
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: 0.6 }}
                        >
                          {JSON.parse(script.tags).map((tag, index) => (
                            <motion.div
                              key={index}
                              initial={{ opacity: 0, scale: 0.8 }}
                              animate={{ opacity: 1, scale: 1 }}
                              transition={{ delay: 0.7 + index * 0.1 }}
                            >
                              <Badge className="px-3 py-1 text-xs font-medium rounded-full bg-gradient-to-r from-blue-500/20 to-purple-500/20 text-blue-300 border border-blue-500/30 hover:from-blue-500/30 hover:to-purple-500/30 transition-all duration-300">
                                {tag}
                              </Badge>
                            </motion.div>
                          ))}
                        </motion.div>
                      )}

                      {/* Executor and Status */}
                      <div className="flex items-center justify-between text-sm">
                        {script.executor && (
                          <motion.div
                            className="flex items-center text-foreground/60"
                            initial={{ opacity: 0, x: -10 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ delay: 0.8 }}
                          >
                            <FiMonitor className="mr-2 h-4 w-4 text-purple-400" aria-hidden="true" />
                            <span>Executor: {script.executor}</span>
                          </motion.div>
                        )}

                        <motion.div
                          className="flex items-center text-foreground/60"
                          initial={{ opacity: 0, x: 10 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: 0.9 }}
                        >
                          <span className="relative flex h-2 w-2 mr-2">
                            <span className="absolute inline-flex h-full w-full rounded-full bg-green-400 opacity-75 animate-ping"></span>
                            <span className="relative inline-flex rounded-full h-2 w-2 bg-green-500"></span>
                          </span>
                          <span className="text-green-400 font-medium">Active</span>
                        </motion.div>
                      </div>
                    </div>

                    {/* Enhanced Buttons */}
                    <div className="flex justify-between items-center gap-3 mt-auto">
                      <motion.div
                        className="flex-1"
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <Button
                          variant="default"
                          size="sm"
                          onClick={() => handleCopyToClipboard(script.content, script.id)}
                          className="w-full py-3 px-4 text-sm bg-gradient-to-r from-blue-500 to-purple-500 text-white hover:from-blue-600 hover:to-purple-600 rounded-xl shadow-lg transition-all duration-300 group relative overflow-hidden"
                          ariaLabel={copiedId === script.id ? "Script copied!" : `Copy script ${script.name}`}
                          tabIndex={0}
                          onKeyDown={(e) => {
                            if (e.key === 'Enter' || e.key === 'Space') {
                              handleCopyToClipboard(script.content, script.id);
                            }
                          }}
                        >
                          <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                          <span className="relative z-10 flex items-center justify-center">
                            {copiedId === script.id ? (
                              <>
                                <FiCheck className="mr-2 h-4 w-4" aria-hidden="true" /> Copied!
                              </>
                            ) : (
                              <>
                                <FiCopy className="mr-2 h-4 w-4" aria-hidden="true" /> Copy Script
                              </>
                            )}
                          </span>
                        </Button>
                      </motion.div>

                      <motion.div
                        className="flex-1"
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <Button
                          variant="default"
                          size="sm"
                          onClick={() => handleViewUpdateLog(script)}
                          className="w-full py-3 px-4 text-sm bg-white/10 backdrop-blur-sm border border-white/20 text-foreground hover:bg-white/20 rounded-xl shadow-lg transition-all duration-300 group relative overflow-hidden"
                          ariaLabel={`View update log for ${script.name}`}
                          tabIndex={0}
                          onKeyDown={(e) => {
                            if (e.key === 'Enter' || e.key === 'Space') {
                              handleViewUpdateLog(script);
                            }
                          }}
                        >
                          <div className="absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                          <span className="relative z-10 flex items-center justify-center">
                            <FiFileText className="mr-2 h-4 w-4" aria-hidden="true" /> Update Log
                          </span>
                        </Button>
                      </motion.div>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
        </motion.div>

        <AnimatePresence>
          {showUpdateLog && selectedScript && (
            <UpdateLogModal
              script={selectedScript}
              onClose={handleCloseUpdateLog}
            />
          )}
          {showExecutorSupport && (
            <motion.div
              className="mt-8 w-full"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
            >
              <div className="p-6 bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl shadow-2xl">
                <ExecutorSupport
                  onClose={() => setShowExecutorSupport(false)}
                />
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default Scripts;
