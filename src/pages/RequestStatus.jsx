import { useState, useEffect } from 'react';
import { useSearchParams, Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { cn } from '../lib/utils';

import { PageLayout, Section } from '../components/pages/PageLayout';
import { Button } from '../components/ui/Button';
import { FiAlertCircle, FiCheckCircle, FiClock, FiXCircle, FiSearch, FiDownload, FiExternalLink, FiFileText, FiUser, FiCalendar } from 'react-icons/fi';

const statusConfig = {
  pending: {
    icon: <FiClock className={cn("h-5 w-5 text-yellow-500")} />,
    color: cn('bg-yellow-500/10 text-yellow-600 dark:text-yellow-400 border-yellow-500/20'),
    text: 'Pending Review',
    description: 'Your request has been received and is awaiting review by our team.'
  },
  in_progress: {
    icon: <div className={cn("h-5 w-5 flex items-center justify-center")}><div className={cn("animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500")}></div></div>,
    color: cn('bg-blue-500/10 text-blue-600 dark:text-blue-400 border-blue-500/20'),
    text: 'In Progress',
    description: 'Our team is currently working on your script request.'
  },
  approved: {
    icon: <FiCheckCircle className={cn("h-5 w-5 text-green-500")} />,
    color: cn('bg-green-500/10 text-green-600 dark:text-green-400 border-green-500/20'),
    text: 'Approved',
    description: 'Your request has been approved and is in the development queue.'
  },
  completed: {
    icon: <FiCheckCircle className={cn("h-5 w-5 text-green-500")} />,
    color: cn('bg-green-500/10 text-green-600 dark:text-green-400 border-green-500/20'),
    text: 'Completed',
    description: 'Your script request has been completed and is now available.'
  },
  rejected: {
    icon: <FiXCircle className={cn("h-5 w-5 text-red-500")} />,
    color: cn('bg-red-500/10 text-red-600 dark:text-red-400 border-red-500/20'),
    text: 'Rejected',
    description: 'Your request could not be fulfilled at this time.'
  }
};

const RequestStatus = () => {
  const [searchParams] = useSearchParams();
  const [requestId, setRequestId] = useState('');
  const [request, setRequest] = useState(null);
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  
  const handleCheckStatus = async (id) => {
    const checkId = id || requestId.trim();
    if (!checkId) {
      setError('Please enter a request ID');
      return;
    }
    
    setIsLoading(true);
    setError('');
    setRequest(null);

    try {
      const response = await fetch(`/.netlify/functions/get-script-request-by-id?id=${checkId}`);
      if (!response.ok) {
        const errData = await response.json();
        throw new Error(errData.error || 'Request not found.');
      }
      const data = await response.json();
      setRequest(data);
    } catch (err) {
      console.error('Error checking status:', err);
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  // Check for ID in URL params on component mount
  useEffect(() => {
    const idFromUrl = searchParams.get('id');
    if (idFromUrl) {
      setRequestId(idFromUrl);
      handleCheckStatus(idFromUrl);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchParams]);
  
  const handleFormSubmit = (e) => {
    e.preventDefault();
    handleCheckStatus();
  };
  
  const handleRequestIdChange = (e) => {
    setRequestId(e.target.value);
  };
  
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    
    const options = { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    };
    
    return new Date(dateString).toLocaleDateString(undefined, options);
  };
  
  const getStatusConfig = (status) => {
    return statusConfig[status] || statusConfig.pending;
  };
  
  return (
    <div className="min-h-screen pt-16 bg-gradient-to-br from-gray-50 via-blue-50/30 to-purple-50/30 dark:from-gray-900 dark:via-blue-900/20 dark:to-purple-900/20 relative overflow-hidden">
      {/* Background effects */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5 dark:opacity-10"></div>
      <motion.div
        className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-r from-blue-400/20 to-purple-400/20 rounded-full blur-xl"
        animate={{
          y: [0, -20, 0],
          x: [0, 10, 0],
        }}
        transition={{
          duration: 6,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />
      <motion.div
        className="absolute bottom-20 right-10 w-24 h-24 bg-gradient-to-r from-purple-400/20 to-pink-400/20 rounded-full blur-xl"
        animate={{
          y: [0, 15, 0],
          x: [0, -15, 0],
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 1
        }}
      />

      <div className="container relative mx-auto px-4 py-12">
        <div className="max-w-4xl mx-auto">
          <motion.div
            className="text-center mb-12"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <motion.div
              className="inline-block px-6 py-3 mb-6 rounded-full bg-gradient-to-r from-blue-500/10 to-purple-500/10 backdrop-blur-sm border border-blue-500/20 text-primary text-sm font-semibold shadow-lg"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <span className="flex items-center">
                <FiSearch className="mr-2 h-4 w-4" />
                Request Tracking System
              </span>
            </motion.div>

            <motion.h1
              className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 bg-clip-text text-transparent leading-tight"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
              Request Status
            </motion.h1>
            <motion.p
              className="text-lg text-foreground/80 max-w-2xl mx-auto leading-relaxed"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
              Track the progress of your script request and get real-time updates
            </motion.p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 30, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            transition={{ duration: 0.6, delay: 0.5 }}
            className="relative overflow-hidden bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl shadow-2xl p-8 md:p-12"
          >
            {/* Background gradient */}
            <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 via-purple-500/5 to-pink-500/5" />

            <motion.div
              className="relative z-10 mb-8"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.6 }}
            >
              <h2 className="text-2xl font-bold mb-4 text-foreground">Check Your Request</h2>
              <p className="text-foreground/70 text-lg">
                Enter the ID you received when you submitted your request to track its progress.
              </p>
            </motion.div>

            <motion.form
              onSubmit={handleFormSubmit}
              className="relative z-10 space-y-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.7 }}
            >
              <div>
                <label htmlFor="requestId" className="block text-sm font-semibold mb-3 text-foreground/90">
                  <FiFileText className="inline mr-2 h-4 w-4" />
                  Request ID
                </label>
                <div className="flex flex-col md:flex-row gap-4">
                  <motion.input
                    type="text"
                    id="requestId"
                    value={requestId}
                    onChange={handleRequestIdChange}
                    placeholder="e.g., 48a20f19-6c48-473f-92f1-d127dc03348d"
                    className="flex-1 px-4 py-4 bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl shadow-lg focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-300 text-foreground placeholder-foreground/50 hover:bg-white/15 font-mono"
                    disabled={isLoading}
                    aria-label="Enter your request ID"
                    whileFocus={{ scale: 1.02 }}
                  />
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <Button
                      type="submit"
                      disabled={isLoading || !requestId.trim()}
                      className="w-full md:w-auto bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 hover:from-blue-700 hover:via-purple-700 hover:to-indigo-700 text-white py-4 px-6 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 relative overflow-hidden"
                    >
                      <motion.div
                        className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0"
                        initial={{ x: "-100%" }}
                        whileHover={{ x: "100%" }}
                        transition={{ duration: 0.6 }}
                      />
                      <span className="relative z-10 flex items-center gap-2">
                        {isLoading ? (
                          <>
                            <motion.div
                              animate={{ rotate: 360 }}
                              transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                            >
                              <svg className="h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                              </svg>
                            </motion.div>
                            Checking...
                          </>
                        ) : (
                          <>
                            <FiSearch className="h-4 w-4" />
                            <span>Check Status</span>
                          </>
                        )}
                      </span>
                    </Button>
                  </motion.div>
                </div>
              </div>

              {error && (
                <motion.div
                  initial={{ opacity: 0, y: -10, scale: 0.95 }}
                  animate={{ opacity: 1, y: 0, scale: 1 }}
                  transition={{ duration: 0.4 }}
                  className="p-6 bg-red-500/10 backdrop-blur-md border border-red-500/30 text-red-800 dark:text-red-200 rounded-2xl shadow-lg flex items-start"
                  aria-live="assertive"
                >
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ duration: 0.3, delay: 0.1 }}
                  >
                    <FiAlertCircle className="h-6 w-6 mr-3 mt-0.5 flex-shrink-0" />
                  </motion.div>
                  <span className="font-medium">{error}</span>
                </motion.div>
              )}
            </motion.form>

            {!request && !error && !isLoading && (
              <motion.div
                className="relative z-10 mt-12 text-center py-12 border-t border-white/10"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.8 }}
              >
                <motion.div
                  className="mx-auto w-20 h-20 bg-gradient-to-r from-blue-500/20 to-purple-500/20 backdrop-blur-sm rounded-2xl flex items-center justify-center mb-6 shadow-lg"
                  animate={{
                    y: [0, -5, 0],
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                >
                  <FiSearch className="h-8 w-8 text-foreground/60" />
                </motion.div>
                <h3 className="text-xl font-bold mb-3 text-foreground">No request selected</h3>
                <p className="text-foreground/70 max-w-md mx-auto mb-8 leading-relaxed">
                  Enter a request ID above to check the status of your submission and track its progress.
                </p>
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Button asChild variant="outline" className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl hover:bg-white/20 transition-all duration-300 px-6 py-3">
                    <Link to="/request-script" aria-label="Submit a New Request">
                      <FiFileText className="mr-2 h-4 w-4" />
                      Submit a New Request
                    </Link>
                  </Button>
                </motion.div>
              </motion.div>
            )}

            {isLoading && !request && (
              <motion.div
                className="relative z-10 mt-12 py-16 flex flex-col items-center"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.4 }}
              >
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                  className="w-16 h-16 border-4 border-blue-500/30 border-t-blue-500 rounded-full mb-6"
                />
                <motion.p
                  className="text-foreground/70 font-medium"
                  animate={{ opacity: [0.5, 1, 0.5] }}
                  transition={{ duration: 2, repeat: Infinity }}
                >
                  Searching for your request...
                </motion.p>
              </motion.div>
            )}

            {request && (
              <motion.div
                className="relative z-10 mt-12 border-t border-white/10 pt-8"
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.8 }}
              >
                <div className="flex flex-col md:flex-row md:items-center justify-between gap-6 mb-8">
                  <div>
                    <h3 className="text-2xl font-bold mb-2 text-foreground">Request Details</h3>
                    <p className="text-sm text-foreground/60 font-mono bg-white/10 px-3 py-1 rounded-lg inline-block">
                      ID: {request.id}
                    </p>
                  </div>
                  <motion.div
                    className={cn("inline-flex items-center px-6 py-3 rounded-2xl text-sm font-semibold shadow-lg backdrop-blur-sm", getStatusConfig(request.status).color)}
                    whileHover={{ scale: 1.05 }}
                    animate={request.status === 'in_progress' ? {
                      boxShadow: ["0 0 0 0 rgba(59, 130, 246, 0.4)", "0 0 0 10px rgba(59, 130, 246, 0)", "0 0 0 0 rgba(59, 130, 246, 0)"]
                    } : {}}
                    transition={{ duration: 1.5, repeat: request.status === 'in_progress' ? Infinity : 0 }}
                  >
                    {getStatusConfig(request.status).icon}
                    <span className="ml-3">{getStatusConfig(request.status).text}</span>
                  </motion.div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                  <motion.div
                    className="p-6 bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5, delay: 0.9 }}
                  >
                    <div className="flex items-center mb-3">
                      <FiFileText className="h-5 w-5 text-blue-400 mr-2" />
                      <p className="font-semibold text-foreground/90">Script Name</p>
                    </div>
                    <p className="text-foreground/80 text-lg">{request.script_name || 'N/A'}</p>
                  </motion.div>

                  <motion.div
                    className="p-6 bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl"
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5, delay: 1.0 }}
                  >
                    <div className="flex items-center mb-3">
                      <FiUser className="h-5 w-5 text-purple-400 mr-2" />
                      <p className="font-semibold text-foreground/90">Roblox Username</p>
                    </div>
                    <p className="text-foreground/80 text-lg">{request.user_name || 'N/A'}</p>
                  </motion.div>

                  <motion.div
                    className="md:col-span-2 p-6 bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 1.1 }}
                  >
                    <div className="flex items-center mb-3">
                      <FiFileText className="h-5 w-5 text-green-400 mr-2" />
                      <p className="font-semibold text-foreground/90">Description</p>
                    </div>
                    <p className="text-foreground/80 leading-relaxed break-words">{request.description || 'N/A'}</p>
                  </motion.div>

                  <motion.div
                    className="p-6 bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5, delay: 1.2 }}
                  >
                    <div className="flex items-center mb-3">
                      <FiCalendar className="h-5 w-5 text-orange-400 mr-2" />
                      <p className="font-semibold text-foreground/90">Submitted On</p>
                    </div>
                    <p className="text-foreground/80">{formatDate(request.requested_at)}</p>
                  </motion.div>

                  <motion.div
                    className="p-6 bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl"
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5, delay: 1.3 }}
                  >
                    <div className="flex items-center mb-3">
                      <FiClock className="h-5 w-5 text-pink-400 mr-2" />
                      <p className="font-semibold text-foreground/90">Last Updated</p>
                    </div>
                    <p className="text-foreground/80">{formatDate(request.reviewed_at)}</p>
                  </motion.div>
                </div>

                {request.status === 'completed' && request.downloadLink && (
                  <motion.div
                    className="bg-gradient-to-r from-green-500/10 via-blue-500/10 to-purple-500/10 backdrop-blur-md border border-green-500/30 p-8 rounded-2xl text-center shadow-lg"
                    initial={{ opacity: 0, scale: 0.95 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.5, delay: 1.4 }}
                  >
                    <motion.div
                      animate={{
                        y: [0, -5, 0],
                        rotate: [0, 5, -5, 0]
                      }}
                      transition={{
                        duration: 2,
                        repeat: Infinity,
                        ease: "easeInOut"
                      }}
                    >
                      <FiDownload className="h-12 w-12 text-green-400 mb-4 mx-auto" />
                    </motion.div>
                    <p className="text-xl font-bold text-green-400 mb-3">Your script is ready!</p>
                    <p className="text-foreground/70 mb-6">Click below to download your custom script</p>
                    <motion.a
                      href={request.downloadLink}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 relative overflow-hidden"
                      aria-label="Download your script"
                      tabIndex={0}
                      onKeyDown={(e) => { if (e.key === 'Enter' || e.key === 'Space') { e.preventDefault(); window.open(request.downloadLink, '_blank'); } }}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <motion.div
                        className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0"
                        initial={{ x: "-100%" }}
                        whileHover={{ x: "100%" }}
                        transition={{ duration: 0.6 }}
                      />
                      <span className="relative z-10 flex items-center">
                        Download Script
                        <FiExternalLink className="ml-2 h-5 w-5" />
                      </span>
                    </motion.a>
                  </motion.div>
                )}

                {request.status === 'rejected' && request.reason && (
                  <motion.div
                    className="bg-gradient-to-r from-red-500/10 via-orange-500/10 to-red-500/10 backdrop-blur-md border border-red-500/30 p-8 rounded-2xl text-center shadow-lg"
                    initial={{ opacity: 0, scale: 0.95 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.5, delay: 1.4 }}
                  >
                    <motion.div
                      animate={{
                        scale: [1, 1.1, 1],
                      }}
                      transition={{
                        duration: 2,
                        repeat: Infinity,
                        ease: "easeInOut"
                      }}
                    >
                      <FiXCircle className="h-12 w-12 text-red-400 mb-4 mx-auto" />
                    </motion.div>
                    <p className="text-xl font-bold text-red-400 mb-3">Request Rejected</p>
                    <p className="text-foreground/70 break-words leading-relaxed">
                      <span className="font-semibold">Reason:</span> {request.reason}
                    </p>
                  </motion.div>
                )}
              </motion.div>
            )}
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default RequestStatus;
