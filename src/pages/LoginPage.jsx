import { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { cn } from '../lib/utils';
import { <PERSON>Lock, FiUser, <PERSON><PERSON>ye, FiEyeOff } from 'react-icons/fi';

/**
 * Admin Login Page
 */
const LoginPage = () => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const navigate = useNavigate();
  const location = useLocation();
  const { login, isAuthenticated } = useAuth();

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated && isAuthenticated) {
      const from = location.state?.from || '/admin';
      navigate(from, { replace: true });
    }
  }, [isAuthenticated, navigate, location.state]);

  const handleLogin = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');
    try {
      // Test credentials by making a request to the security endpoint
      const response = await fetch('/.netlify/functions/security?action=ip-bans', {
        method: 'GET',
        headers: {
          'x-admin-username': username,
          'x-admin-password': password,
          'Content-Type': 'application/json',
        },
      });
      if (response.ok) {
        // Store credentials in localStorage for later authenticated requests
        localStorage.setItem('adminUsername', username);
        localStorage.setItem('adminPassword', password);
        window.location.href = '/admin'; // or your admin dashboard route
      } else {
        setError('Invalid username or password');
        localStorage.removeItem('adminUsername');
        localStorage.removeItem('adminPassword');
      }
    } catch (err) {
      setError('Login failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleUsernameChange = (e) => {
    setUsername(e.target.value);
    setError('');
  };

  const handlePasswordChange = (e) => {
    setPassword(e.target.value);
    setError('');
  };

  const handleBackToHomeClick = (e) => {
    e.preventDefault();
    navigate('/');
  };

  const handleTogglePassword = () => setShowPassword((prev) => !prev);

  return (
    <div className="min-h-screen flex items-center justify-center bg-background dark:bg-background p-4">
      <div className="w-full max-w-md p-8 bg-white dark:bg-gray-900 rounded-2xl shadow-2xl border border-gray-100 dark:border-gray-800 flex flex-col items-center text-gray-900 dark:text-white">
        {/* Logo/Icon */}
        <div className="mb-6 flex flex-col items-center">
          <div className="bg-blue-100 dark:bg-blue-900 rounded-full p-3 mb-2">
            <FiLock className="text-blue-600 dark:text-blue-400 w-8 h-8" aria-hidden="true" />
          </div>
          <h1 className="text-3xl font-bold mb-1">Admin Login</h1>
          <p className="text-gray-500 dark:text-gray-400 text-sm">Sign in to access the admin dashboard</p>
        </div>
        
        {error && (
          <div className="mb-4 w-full p-3 bg-red-50 dark:bg-red-900/30 text-red-700 dark:text-red-300 rounded-md text-sm border border-red-200 dark:border-red-400" aria-live="assertive">
            {error}
          </div>
        )}
        
        <form onSubmit={handleLogin} className="w-full space-y-6">
          <div>
            <label htmlFor="username" className="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-1">
              Username
            </label>
            <div className="relative">
              <span className="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-400 dark:text-gray-500">
                <FiUser className="w-5 h-5" />
              </span>
              <input
                id="username"
                type="text"
                className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900 dark:text-white bg-white dark:bg-gray-800"
                placeholder="Enter your username"
                value={username}
                onChange={handleUsernameChange}
                disabled={isLoading}
                autoComplete="username"
                tabIndex={0}
                aria-label="Username"
                required
              />
            </div>
          </div>
          <div>
            <label htmlFor="password" className="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-1">
              Password
            </label>
            <div className="relative">
              <span className="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-400 dark:text-gray-500">
                <FiLock className="w-5 h-5" />
              </span>
              <input
                id="password"
                type={showPassword ? 'text' : 'password'}
                className="w-full pl-10 pr-10 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900 dark:text-white bg-white dark:bg-gray-800"
                placeholder="Enter your password"
                value={password}
                onChange={handlePasswordChange}
                disabled={isLoading}
                autoComplete="current-password"
                tabIndex={0}
                aria-label="Password"
                required
              />
              <button
                type="button"
                className="absolute inset-y-0 right-0 flex items-center px-3 text-gray-400 dark:text-gray-500 hover:text-blue-600 dark:hover:text-blue-400 focus:outline-none"
                onClick={handleTogglePassword}
                tabIndex={0}
                aria-label={showPassword ? 'Hide password' : 'Show password'}
              >
                {showPassword ? <FiEyeOff className="w-5 h-5" /> : <FiEye className="w-5 h-5" />}
              </button>
            </div>
          </div>
          
          <button
            type="submit"
            className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 dark:bg-blue-500 hover:bg-blue-700 dark:hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition"
            disabled={isLoading}
            aria-label="Sign in"
          >
            {isLoading ? (
              <>
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Signing in...
              </>
            ) : 'Sign In'}
          </button>
          
          <div className="text-center">
            <a
              href="/"
              className="text-sm font-medium text-blue-600 dark:text-blue-400 hover:text-blue-500 dark:hover:text-blue-300 hover:underline"
              onClick={handleBackToHomeClick}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === 'Space') {
                  e.preventDefault();
                  handleBackToHomeClick(e);
                }
              }}
              tabIndex={0}
              aria-label="Back to Home page"
            >
               Back to Home
            </a>
          </div>
        </form>
      </div>
    </div>
  );
};

export default LoginPage;
