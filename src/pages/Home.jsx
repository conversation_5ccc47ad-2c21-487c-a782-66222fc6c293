import { motion, AnimatePresence } from 'framer-motion';
import { Link, useNavigate } from 'react-router-dom';
import { 
  <PERSON>ArrowRight, 
  FiCode, 
  FiZap, 
  FiShield, 
  FiGithub, 
  FiDownload, 
  FiMail,
  FiStar,
  FiUsers,
  FiRefreshCw,
  FiCheckCircle,
  FiTrendingUp,
  FiAward
} from 'react-icons/fi';
import { Button } from '../components/ui/Button';
import { Card } from '../components/ui/Card';
import Team from '../components/Team';
import ExecutorsList from '../components/ExecutorsList';
import React from 'react';
import { cn } from '../lib/utils';

// Add grid pattern background style
const gridPatternStyle = {
  backgroundImage: 'linear-gradient(to right, rgba(0, 0, 0, 0.03) 1px, transparent 1px), linear-gradient(to bottom, rgba(0, 0, 0, 0.03) 1px, transparent 1px)',
  backgroundSize: '40px 40px',
  backgroundPosition: 'center',
};

// In dark mode
const darkGridPatternStyle = {
  backgroundImage: 'linear-gradient(to right, rgba(255, 255, 255, 0.05) 1px, transparent 1px), linear-gradient(to bottom, rgba(255, 255, 255, 0.05) 1px, transparent 1px)',
  backgroundSize: '40px 40px',
  backgroundPosition: 'center',
};

const Home = () => {
  const navigate = useNavigate();

  const handleExploreScriptsClick = () => {
    navigate('/scripts');
  };

  const handleLearnMoreClick = () => {
    const el = document.getElementById('features');
    if (el) el.scrollIntoView({ behavior: 'smooth' });
  };

  const features = [
    {
      icon: <FiCode className="h-8 w-8 text-blue-500" />,
      title: 'Premium Scripts',
      description: 'Access to exclusive, high-performance Roblox scripts with advanced features and regular updates.',
      color: 'from-blue-500 to-blue-600',
    },
    {
      icon: <FiZap className="h-8 w-8 text-purple-500" />,
      title: 'Optimized Performance',
      description: 'Lightning-fast scripts optimized for maximum performance and minimal resource usage.',
      color: 'from-purple-500 to-purple-600',
    },
    {
      icon: <FiShield className="h-8 w-8 text-green-500" />,
      title: 'Safe & Undetectable',
      description: 'Carefully tested to ensure safety and prevent detection by anti-cheat systems.',
      color: 'from-green-500 to-green-600',
    },
    {
      icon: <FiUsers className="h-8 w-8 text-amber-500" />,
      title: 'Active Community',
      description: 'Join thousands of Roblox players in our thriving community.',
      color: 'from-amber-500 to-amber-600',
    },
    {
      icon: <FiRefreshCw className="h-8 w-8 text-pink-500" />,
      title: 'Frequent Updates',
      description: 'Regular script updates to ensure compatibility with the latest Roblox versions.',
      color: 'from-pink-500 to-pink-600',
    },
    {
      icon: <FiCheckCircle className="h-8 w-8 text-indigo-500" />,
      title: 'Easy to Use',
      description: 'Simple installation and user-friendly interface for all skill levels.',
      color: 'from-indigo-500 to-indigo-600',
    },
  ];

  const stats = [
    { value: '500+', label: 'Active Scripts' },
    { value: '100K+', label: 'Users' },
    { value: '99.9%', label: 'Uptime' },
    { value: '24/7', label: 'Support' },
  ];

  return (
    <div className={cn(
      "min-h-screen",
      "bg-gradient-to-b from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800",
      "relative"
    )}>
      {/* Background grid pattern */}
      <div className="fixed inset-0 -z-10" style={gridPatternStyle}></div>
      <div className="dark:hidden fixed inset-0 -z-10 bg-gradient-to-b from-white/80 to-transparent"></div>
      <div className="hidden dark:block fixed inset-0 -z-10" style={darkGridPatternStyle}></div>
      <div className="hidden dark:block fixed inset-0 -z-10 bg-gradient-to-b from-gray-900/95 to-gray-900/80"></div>
      {/* Hero Section */}
      <section className="relative overflow-hidden pt-24 pb-20 md:pt-32 md:pb-28">
        {/* Enhanced background effects */}
        <div className="absolute inset-0 bg-grid-pattern opacity-5 dark:opacity-10"></div>
        <div className="absolute inset-0 bg-gradient-to-b from-transparent to-background/80 dark:to-background/90"></div>

        {/* Floating orbs for visual enhancement */}
        <motion.div
          className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-r from-blue-400/20 to-purple-400/20 rounded-full blur-xl"
          animate={{
            y: [0, -20, 0],
            x: [0, 10, 0],
          }}
          transition={{
            duration: 6,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        <motion.div
          className="absolute top-40 right-20 w-24 h-24 bg-gradient-to-r from-purple-400/20 to-pink-400/20 rounded-full blur-xl"
          animate={{
            y: [0, 15, 0],
            x: [0, -15, 0],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 1
          }}
        />

        <div className="container relative mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-5xl mx-auto text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className={cn(
                "inline-block px-6 py-3 mb-8 rounded-full",
                "bg-gradient-to-r from-blue-500/10 to-purple-500/10 backdrop-blur-sm",
                "border border-blue-500/20 text-primary text-sm font-medium",
                "shadow-lg hover:shadow-xl transition-all duration-300"
              )}
            >
              <span className="flex items-center">
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                >
                  <FiStar className="mr-2 h-4 w-4" />
                </motion.div>
                Trusted by 100,000+ Roblox Players
              </span>
            </motion.div>

            <motion.h1
              className={cn(
                "text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-bold tracking-tight leading-tight",
                "bg-clip-text text-transparent bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500",
                "drop-shadow-lg"
              )}
              initial={{ opacity: 0, y: 30, scale: 0.9 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              transition={{
                duration: 0.8,
                delay: 0.1,
                type: "spring",
                stiffness: 100
              }}
            >
              <motion.span
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.3, duration: 0.6 }}
              >
                Take Your Roblox
              </motion.span>
              <br />
              <motion.span
                className="text-foreground dark:text-white relative"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.6, duration: 0.6 }}
              >
                Experience
                <motion.span
                  className="bg-clip-text text-transparent bg-gradient-to-r from-yellow-400 to-orange-500"
                  animate={{
                    backgroundPosition: ["0% 50%", "100% 50%", "0% 50%"],
                  }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    ease: "linear"
                  }}
                >
                  Further
                </motion.span>
              </motion.span>
            </motion.h1>
            
            <motion.p 
              className={cn(
                "mt-6 text-lg md:text-xl text-foreground/80",
                "max-w-3xl mx-auto leading-relaxed"
              )}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              Unlock the full potential of your favorite Roblox games with our premium scripts.
              Join thousands of players who trust us for the best scripting experience.
            </motion.p>
            
            <motion.div
              className={cn("mt-12 flex flex-col sm:flex-row justify-center gap-6")}
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Button
                  size="lg"
                  className={cn(
                    "group px-10 py-5 text-lg font-semibold relative overflow-hidden",
                    "bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600",
                    "hover:from-blue-700 hover:via-purple-700 hover:to-indigo-700 text-white",
                    "shadow-2xl hover:shadow-blue-500/25 transition-all duration-500",
                    "border border-blue-500/20 rounded-xl"
                  )}
                  onClick={handleExploreScriptsClick}
                  ariaLabel="Explore Scripts"
                >
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0"
                    initial={{ x: "-100%" }}
                    whileHover={{ x: "100%" }}
                    transition={{ duration: 0.6 }}
                  />
                  <span className="relative z-10 flex items-center">
                    Explore Scripts
                    <motion.div
                      className="ml-2"
                      animate={{ x: [0, 4, 0] }}
                      transition={{ duration: 1.5, repeat: Infinity }}
                    >
                      <FiArrowRight className="h-5 w-5" />
                    </motion.div>
                  </span>
                </Button>
              </motion.div>

              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Button
                  variant="outline"
                  size="lg"
                  className={cn(
                    "px-10 py-5 text-lg font-semibold relative overflow-hidden",
                    "bg-white/5 backdrop-blur-md hover:bg-white/10",
                    "border-2 border-gradient-to-r from-blue-500/30 to-purple-500/30",
                    "hover:border-blue-500/50 transition-all duration-500",
                    "text-foreground hover:text-primary rounded-xl",
                    "shadow-lg hover:shadow-xl"
                  )}
                  onClick={handleLearnMoreClick}
                  ariaLabel="Learn More about our features"
                >
                  <span className="relative z-10">Learn More</span>
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10"
                    initial={{ opacity: 0 }}
                    whileHover={{ opacity: 1 }}
                    transition={{ duration: 0.3 }}
                  />
                </Button>
              </motion.div>
            </motion.div>
            
            {/* Enhanced Stats */}
            <motion.div
              className={cn("mt-20 grid grid-cols-2 sm:grid-cols-4 gap-8 max-w-5xl mx-auto")}
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.5 }}
            >
              {stats.map((stat, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.6 + index * 0.1 }}
                  whileHover={{
                    scale: 1.05,
                    y: -5,
                    transition: { duration: 0.2 }
                  }}
                  className={cn(
                    "relative group bg-white/10 backdrop-blur-md rounded-2xl p-8",
                    "border border-white/20 hover:border-blue-500/40",
                    "transition-all duration-500 hover:shadow-2xl hover:shadow-blue-500/20",
                    "overflow-hidden"
                  )}
                >
                  {/* Gradient background effect */}
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-br from-blue-500/10 via-purple-500/10 to-pink-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"
                    initial={false}
                  />

                  {/* Animated border */}
                  <motion.div
                    className="absolute inset-0 rounded-2xl"
                    style={{
                      background: "linear-gradient(45deg, transparent, rgba(59, 130, 246, 0.3), transparent)",
                    }}
                    animate={{
                      rotate: [0, 360],
                    }}
                    transition={{
                      duration: 8,
                      repeat: Infinity,
                      ease: "linear"
                    }}
                    initial={{ opacity: 0 }}
                    whileHover={{ opacity: 1 }}
                  />

                  <div className="relative z-10">
                    <motion.div
                      className={cn(
                        "text-4xl md:text-5xl font-bold mb-3",
                        "bg-gradient-to-r from-blue-400 via-purple-500 to-pink-500 bg-clip-text text-transparent"
                      )}
                      whileHover={{
                        scale: 1.1,
                        transition: { duration: 0.2 }
                      }}
                    >
                      {stat.value}
                    </motion.div>
                    <div className={cn("text-sm font-semibold text-foreground/80 uppercase tracking-wider")}>
                      {stat.label}
                    </div>
                  </div>

                  {/* Sparkle effect */}
                  <motion.div
                    className="absolute top-4 right-4 w-2 h-2 bg-blue-400 rounded-full opacity-0 group-hover:opacity-100"
                    animate={{
                      scale: [0, 1, 0],
                      opacity: [0, 1, 0],
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      delay: index * 0.2
                    }}
                  />
                </motion.div>
              ))}
            </motion.div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-16 md:py-24 relative">
        <div className="absolute inset-0 bg-grid-pattern opacity-5 dark:opacity-10"></div>
        <div className="container relative mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-5xl mx-auto text-center mb-20">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5 }}
              className={cn(
                "inline-block px-6 py-3 mb-6 rounded-full",
                "bg-gradient-to-r from-blue-500/10 to-purple-500/10 backdrop-blur-sm",
                "border border-blue-500/20 text-primary text-sm font-semibold",
                "shadow-lg"
              )}
            >
              <span className="flex items-center">
                <FiZap className="mr-2 h-4 w-4" />
                Why Choose Us
              </span>
            </motion.div>
            <motion.h2
              className={cn(
                "text-4xl sm:text-5xl md:text-6xl font-bold tracking-tight text-foreground dark:text-white mb-6",
                "leading-tight"
              )}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.1 }}
            >
              Discover the Power of{" "}
              <motion.span
                className="bg-clip-text text-transparent bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500"
                animate={{
                  backgroundPosition: ["0% 50%", "100% 50%", "0% 50%"],
                }}
                transition={{
                  duration: 5,
                  repeat: Infinity,
                  ease: "linear"
                }}
              >
                Madara
              </motion.span>
            </motion.h2>
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className={cn("text-xl text-foreground/80 max-w-3xl mx-auto leading-relaxed")}
            >
              Our cutting-edge features are designed to provide you with an unparalleled Roblox experience,
              combining security, performance, and innovation.
            </motion.p>
          </div>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-10">
            {features.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 60, scale: 0.9 }}
                whileInView={{ opacity: 1, y: 0, scale: 1 }}
                viewport={{ once: true }}
                transition={{
                  duration: 0.7,
                  delay: index * 0.15,
                  type: "spring",
                  stiffness: 100
                }}
                whileHover={{
                  y: -10,
                  transition: { duration: 0.3 }
                }}
              >
                <Card
                  className={cn(
                    "relative group p-8 text-center overflow-hidden",
                    "bg-white/10 backdrop-blur-md border border-white/20",
                    "hover:border-blue-500/40 transition-all duration-500",
                    "shadow-xl hover:shadow-2xl hover:shadow-blue-500/20",
                    "rounded-2xl"
                  )}
                >
                  {/* Animated background gradient */}
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-br from-blue-500/5 via-purple-500/5 to-pink-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"
                    initial={false}
                  />

                  {/* Floating icon container */}
                  <motion.div
                    className={cn(
                      "relative w-20 h-20 rounded-2xl flex items-center justify-center mx-auto mb-6",
                      "bg-gradient-to-br shadow-lg",
                      feature.color,
                      "text-white group-hover:scale-110 transition-transform duration-300"
                    )}
                    whileHover={{
                      rotate: [0, -5, 5, 0],
                      transition: { duration: 0.5 }
                    }}
                  >
                    <motion.div
                      animate={{
                        y: [0, -2, 0],
                      }}
                      transition={{
                        duration: 2,
                        repeat: Infinity,
                        ease: "easeInOut",
                        delay: index * 0.2
                      }}
                    >
                      {feature.icon}
                    </motion.div>

                    {/* Glow effect */}
                    <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  </motion.div>

                  <div className="relative z-10">
                    <Card.Title className={cn(
                      "text-2xl font-bold text-foreground dark:text-white mb-4",
                      "group-hover:text-blue-400 transition-colors duration-300"
                    )}>
                      {feature.title}
                    </Card.Title>
                    <Card.Description className={cn(
                      "text-foreground/80 leading-relaxed text-base",
                      "group-hover:text-foreground/90 transition-colors duration-300"
                    )}>
                      {feature.description}
                    </Card.Description>
                  </div>

                  {/* Corner accent */}
                  <motion.div
                    className="absolute top-4 right-4 w-3 h-3 bg-blue-400 rounded-full opacity-0 group-hover:opacity-100"
                    animate={{
                      scale: [0, 1, 0],
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      delay: index * 0.3
                    }}
                  />
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Executors List Section */}
      <section className="py-16 md:py-24 bg-background dark:bg-gray-900">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center mb-16">
            <motion.h2
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5 }}
              className="text-3xl sm:text-4xl md:text-5xl font-bold tracking-tight text-foreground dark:text-white"
            >
              Our Supported <span className="bg-clip-text text-transparent bg-gradient-to-r from-green-500 to-teal-600">Executors</span>
            </motion.h2>
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.1 }}
              className="mt-4 text-lg text-foreground/80 max-w-2xl mx-auto"
            >
              We proudly support a wide range of popular Roblox executors, ensuring broad compatibility and ease of use.
            </motion.p>
          </div>
          <ExecutorsList />
        </div>
      </section>

      {/* Team Section */}
      <section className="py-16 md:py-24 bg-gradient-to-br from-gray-50 via-blue-50/30 to-purple-50/30 dark:from-gray-900 dark:via-blue-900/20 dark:to-purple-900/20 relative overflow-hidden">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <Team />
        </div>
      </section>

      {/* Contact Section */}
      <section id="contact" className="py-16 md:py-24 bg-background dark:bg-gray-900">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center mb-16">
            <motion.h2
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5 }}
              className="text-3xl sm:text-4xl md:text-5xl font-bold tracking-tight text-foreground dark:text-white"
            >
              Get in <span className="bg-clip-text text-transparent bg-gradient-to-r from-purple-500 to-pink-600">Touch</span>
            </motion.h2>
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.1 }}
              className="mt-4 text-lg text-foreground/80 max-w-2xl mx-auto"
            >
              Have questions or need support? Reach out to us through our contact channels.
            </motion.p>
          </div>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="flex flex-col sm:flex-row justify-center gap-4"
              >
            <Button 
              size="lg" 
              className="group px-8 py-4 text-lg font-semibold bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-0.5"
              onClick={() => navigate('/contact')}
              ariaLabel="Go to contact us page"
            >
              Contact Us
              <FiMail className="ml-2 h-5 w-5" />
            </Button>
            <Button 
              size="lg" 
              variant="outline"
              className="px-8 py-4 text-lg font-semibold bg-white/10 backdrop-blur-sm hover:bg-white/20 border-2 border-foreground/10 hover:border-purple-300 transition-all duration-300"
              onClick={() => navigate('/faq')}
              ariaLabel="Go to FAQ page"
            >
              Visit FAQ
            </Button>
              </motion.div>
        </div>
      </section>
    </div>
  );
};

export default Home;
