import { motion } from 'framer-motion';
import { FiFileText, FiInfo, FiBook, FiUser, FiEdit, FiKey, FiXCircle, FiLogOut, FiAlertTriangle, FiShield, FiRefreshCw, FiMail } from 'react-icons/fi';

const TermsOfService = () => {
  const lastUpdated = 'July 4, 2025';

  const tableOfContents = [
    { id: 'introduction', title: 'Introduction', icon: FiInfo },
    { id: 'definitions', title: 'Definitions', icon: FiBook },
    { id: 'accounts', title: 'User Accounts', icon: FiUser },
    { id: 'content', title: 'User Content', icon: FiEdit },
    { id: 'licenses', title: 'Licenses & Usage', icon: FiKey },
    { id: 'termination', title: 'Account Termination', icon: FiLogOut },
    { id: 'disclaimer', title: 'Service Disclaimer', icon: FiAlertTriangle },
    { id: 'limitation', title: 'Limitation of Liability', icon: FiShield },
    { id: 'changes', title: 'Changes to Terms', icon: FiRefreshCw },
  ];

  return (
    <div className="min-h-screen pt-16 bg-gradient-to-br from-gray-50 via-blue-50/30 to-purple-50/30 dark:from-gray-900 dark:via-blue-900/20 dark:to-purple-900/20 relative overflow-hidden">
      {/* Background effects */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5 dark:opacity-10"></div>
      <motion.div
        className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-r from-blue-400/20 to-purple-400/20 rounded-full blur-xl"
        animate={{
          y: [0, -20, 0],
          x: [0, 10, 0],
        }}
        transition={{
          duration: 6,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />
      <motion.div
        className="absolute bottom-20 right-10 w-24 h-24 bg-gradient-to-r from-purple-400/20 to-pink-400/20 rounded-full blur-xl"
        animate={{
          y: [0, 15, 0],
          x: [0, -15, 0],
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 1
        }}
      />
      <motion.div
        className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-40 h-40 bg-gradient-to-r from-green-400/10 to-blue-400/10 rounded-full blur-2xl"
        animate={{
          scale: [1, 1.2, 1],
          opacity: [0.3, 0.5, 0.3],
        }}
        transition={{
          duration: 10,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 2
        }}
      />

      <div className="container relative mx-auto px-4 py-12 md:py-16">
        {/* Header */}
        <motion.div
          className="text-center mb-12"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <motion.div
            className="inline-block px-6 py-3 mb-6 rounded-full bg-gradient-to-r from-blue-500/10 to-purple-500/10 backdrop-blur-sm border border-blue-500/20 text-primary text-sm font-semibold shadow-lg"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <span className="flex items-center">
              <FiFileText className="mr-2 h-4 w-4" />
              Legal Document
            </span>
          </motion.div>

          <motion.h1
            className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 bg-clip-text text-transparent leading-tight"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            Terms of Service
          </motion.h1>
          <motion.p
            className="text-lg text-foreground/80 max-w-2xl mx-auto leading-relaxed mb-4"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            Please read these Terms of Service carefully before using Project Madara. By accessing our platform, you agree to be bound by these terms and conditions.
          </motion.p>
          <motion.p
            className="text-sm text-foreground/60"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.5 }}
          >
            Last updated: {lastUpdated}
          </motion.p>
        </motion.div>

        {/* Table of Contents */}
        <motion.div
          className="max-w-4xl mx-auto mb-12 p-6 bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl shadow-2xl"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
        >
          <h2 className="text-2xl font-bold text-foreground mb-6 text-center">Table of Contents</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {tableOfContents.map((item, index) => (
              <motion.a
                key={item.id}
                href={`#${item.id}`}
                className="flex items-center p-4 bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl hover:bg-white/10 hover:border-blue-500/30 transition-all duration-300 group"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.4, delay: 0.7 + index * 0.05 }}
                whileHover={{ scale: 1.02, x: 5 }}
              >
                <div className="p-2 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-lg mr-3 group-hover:from-blue-500/30 group-hover:to-purple-500/30 transition-all duration-300">
                  <item.icon className="h-4 w-4 text-blue-400" />
                </div>
                <span className="text-foreground group-hover:text-blue-400 transition-colors duration-300 font-medium">
                  {item.title}
                </span>
              </motion.a>
            ))}
          </div>
        </motion.div>

        {/* Content Sections */}
        <div className="max-w-4xl mx-auto space-y-8">
          {/* Introduction */}
          <motion.section
            id="introduction"
            className="p-8 bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl shadow-2xl"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <div className="flex items-center mb-6">
              <div className="p-3 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-xl mr-4">
                <FiInfo className="h-6 w-6 text-blue-400" />
              </div>
              <h2 className="text-3xl font-bold text-foreground">1. Introduction</h2>
            </div>
            <div className="prose prose-lg max-w-none text-foreground/80 leading-relaxed">
              <p className="mb-4">
                Welcome to <strong className="text-foreground">Project Madara</strong>! These Terms of Service ("Terms") govern your access to and use of the Project Madara
                website, services, and applications (collectively, the "Service"). By accessing or using our Service, you agree
                to be bound by these Terms and our Privacy Policy.
              </p>
              <p>
                These terms constitute a legally binding agreement between you and Project Madara. If you do not agree to these terms,
                please do not use our Service. We reserve the right to update these terms at any time, and your continued use of the
                Service constitutes acceptance of any changes.
              </p>
            </div>
          </motion.section>

          {/* Definitions */}
          <motion.section
            id="definitions"
            className="p-8 bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl shadow-2xl"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <div className="flex items-center mb-6">
              <div className="p-3 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-xl mr-4">
                <FiBook className="h-6 w-6 text-blue-400" />
              </div>
              <h2 className="text-3xl font-bold text-foreground">2. Definitions</h2>
            </div>
            <div className="prose prose-lg max-w-none text-foreground/80 leading-relaxed">
              <p className="mb-4">For the purposes of these Terms, the following definitions apply:</p>
              <div className="grid gap-4">
                <div className="p-4 bg-white/5 rounded-xl border border-white/10">
                  <strong className="text-foreground">"Service"</strong> refers to the Project Madara website, applications, and related services.
                </div>
                <div className="p-4 bg-white/5 rounded-xl border border-white/10">
                  <strong className="text-foreground">"User," "you," and "your"</strong> refer to individuals who access or use our Service.
                </div>
                <div className="p-4 bg-white/5 rounded-xl border border-white/10">
                  <strong className="text-foreground">"Content"</strong> includes text, graphics, images, code, scripts, and other materials.
                </div>
                <div className="p-4 bg-white/5 rounded-xl border border-white/10">
                  <strong className="text-foreground">"User Content"</strong> means Content that users submit or transmit through the Service.
                </div>
              </div>
            </div>
          </motion.section>

          {/* User Accounts */}
          <motion.section
            id="accounts"
            className="p-8 bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl shadow-2xl"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <div className="flex items-center mb-6">
              <div className="p-3 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-xl mr-4">
                <FiUser className="h-6 w-6 text-blue-400" />
              </div>
              <h2 className="text-3xl font-bold text-foreground">3. User Accounts</h2>
            </div>
            <div className="prose prose-lg max-w-none text-foreground/80 leading-relaxed">
              <p className="mb-4">
                To access certain features of the Service, you may be required to create an account. You are responsible for
                maintaining the confidentiality of your account credentials and for all activities that occur under your account.
              </p>
              <p className="mb-4">
                You must be at least 13 years old to use our Service. By using the Service, you represent that you are at least 13 years old
                and have the legal capacity to enter into these Terms.
              </p>
              <div className="p-4 bg-blue-500/10 border border-blue-500/20 rounded-xl">
                <p className="mb-0 text-blue-200">
                  <strong>Account Security:</strong> You agree to notify us immediately of any unauthorized use of your account or any other breach of security.
                </p>
              </div>
            </div>
          </motion.section>

          {/* User Content */}
          <motion.section
            id="content"
            className="p-8 bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl shadow-2xl"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <div className="flex items-center mb-6">
              <div className="p-3 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-xl mr-4">
                <FiEdit className="h-6 w-6 text-blue-400" />
              </div>
              <h2 className="text-3xl font-bold text-foreground">4. User Content</h2>
            </div>
            <div className="prose prose-lg max-w-none text-foreground/80 leading-relaxed">
              <p className="mb-4">
                You retain ownership of any Content you submit to the Service. By submitting Content, you grant us a worldwide,
                non-exclusive, royalty-free license to use, reproduce, modify, adapt, publish, and display such Content
                in connection with the Service.
              </p>
              <p className="mb-4">
                You are solely responsible for your Content and the consequences of posting or publishing it. We reserve the
                right to remove any Content that violates these Terms or our community guidelines.
              </p>
              <div className="p-4 bg-yellow-500/10 border border-yellow-500/20 rounded-xl">
                <p className="mb-0 text-yellow-200">
                  <strong>Content Guidelines:</strong> All user-submitted content must comply with applicable laws and our community standards.
                </p>
              </div>
            </div>
          </motion.section>

          {/* Licenses & Usage */}
          <motion.section
            id="licenses"
            className="p-8 bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl shadow-2xl"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <div className="flex items-center mb-6">
              <div className="p-3 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-xl mr-4">
                <FiKey className="h-6 w-6 text-blue-400" />
              </div>
              <h2 className="text-3xl font-bold text-foreground">5. Licenses & Usage</h2>
            </div>
            <div className="prose prose-lg max-w-none text-foreground/80 leading-relaxed">
              <p className="mb-4">
                Subject to your compliance with these Terms, we grant you a limited, non-exclusive, non-transferable,
                non-sublicensable license to access and use the Service for your personal, non-commercial use.
              </p>
              <p className="mb-4">
                Scripts and other materials available through the Service may be subject to their own licenses. You are
                responsible for complying with all applicable licenses and usage restrictions.
              </p>
              <div className="p-4 bg-green-500/10 border border-green-500/20 rounded-xl">
                <p className="mb-0 text-green-200">
                  <strong>Usage Rights:</strong> Your license is contingent upon compliance with these Terms and applicable laws.
                </p>
              </div>
            </div>
          </motion.section>

          

          {/* Account Termination */}
          <motion.section
            id="termination"
            className="p-8 bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl shadow-2xl"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <div className="flex items-center mb-6">
              <div className="p-3 bg-gradient-to-r from-orange-500/20 to-red-500/20 rounded-xl mr-4">
                <FiLogOut className="h-6 w-6 text-orange-400" />
              </div>
              <h2 className="text-3xl font-bold text-foreground">6. Account Termination</h2>
            </div>
            <div className="prose prose-lg max-w-none text-foreground/80 leading-relaxed">
              <p className="mb-4">
                We may terminate or suspend your account and access to the Service immediately, without prior notice or liability,
                for any reason, including without limitation if you breach these Terms. Upon termination, your right to use the
                Service will immediately cease.
              </p>
              <div className="p-4 bg-orange-500/10 border border-orange-500/20 rounded-xl">
                <p className="mb-0 text-orange-200">
                  <strong>Effect of Termination:</strong> All provisions of these Terms which by their nature should survive termination shall survive.
                </p>
              </div>
            </div>
          </motion.section>

          {/* Service Disclaimer */}
          <motion.section
            id="disclaimer"
            className="p-8 bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl shadow-2xl"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <div className="flex items-center mb-6">
              <div className="p-3 bg-gradient-to-r from-yellow-500/20 to-orange-500/20 rounded-xl mr-4">
                <FiAlertTriangle className="h-6 w-6 text-yellow-400" />
              </div>
              <h2 className="text-3xl font-bold text-foreground">7. Service Disclaimer</h2>
            </div>
            <div className="prose prose-lg max-w-none text-foreground/80 leading-relaxed">
              <div className="p-6 bg-yellow-500/10 border border-yellow-500/20 rounded-xl mb-4">
                <p className="mb-4 text-yellow-200 font-semibold">
                  THE SERVICE IS PROVIDED "AS IS" AND "AS AVAILABLE" WITHOUT WARRANTIES OF ANY KIND, EITHER EXPRESS OR IMPLIED,
                  INCLUDING BUT NOT LIMITED TO IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE, OR
                  NON-INFRINGEMENT.
                </p>
              </div>
              <p>
                We do not warrant that the Service will be uninterrupted, secure, or error-free, or that any defects will be corrected.
                Your use of the Service is at your own risk.
              </p>
            </div>
          </motion.section>

          {/* Limitation of Liability */}
          <motion.section
            id="limitation"
            className="p-8 bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl shadow-2xl"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <div className="flex items-center mb-6">
              <div className="p-3 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-xl mr-4">
                <FiShield className="h-6 w-6 text-purple-400" />
              </div>
              <h2 className="text-3xl font-bold text-foreground">8. Limitation of Liability</h2>
            </div>
            <div className="prose prose-lg max-w-none text-foreground/80 leading-relaxed">
              <div className="p-6 bg-purple-500/10 border border-purple-500/20 rounded-xl">
                <p className="mb-0 text-purple-200 font-semibold">
                  IN NO EVENT SHALL PROJECT MADARA, ITS OFFICERS, DIRECTORS, EMPLOYEES, OR AGENTS BE LIABLE FOR ANY INDIRECT,
                  INCIDENTAL, SPECIAL, CONSEQUENTIAL, OR PUNITIVE DAMAGES ARISING OUT OF OR IN CONNECTION WITH YOUR USE OF THE SERVICE.
                </p>
              </div>
            </div>
          </motion.section>

          {/* Changes to Terms */}
          <motion.section
            id="changes"
            className="p-8 bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl shadow-2xl"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <div className="flex items-center mb-6">
              <div className="p-3 bg-gradient-to-r from-green-500/20 to-blue-500/20 rounded-xl mr-4">
                <FiRefreshCw className="h-6 w-6 text-green-400" />
              </div>
              <h2 className="text-3xl font-bold text-foreground">9. Changes to Terms</h2>
            </div>
            <div className="prose prose-lg max-w-none text-foreground/80 leading-relaxed">
              <p className="mb-4">
                We reserve the right to modify these Terms at any time. We will provide notice of any material changes by posting
                the new Terms on this page and updating the "Last Updated" date.
              </p>
              <p>
                Your continued use of the Service after such modifications constitutes your acceptance of the revised Terms.
                We encourage you to review these Terms periodically.
              </p>
            </div>
          </motion.section>
          
        </div>
      </div>
    </div>
  );
};

export default TermsOfService;
