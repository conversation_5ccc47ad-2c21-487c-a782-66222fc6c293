import { use<PERSON><PERSON><PERSON>, <PERSON>, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { FiArrowLeft, FiDownload, FiStar, FiClock, FiCode, FiUsers, FiAlertTriangle, FiCheckCircle, FiExternalLink } from 'react-icons/fi';
import { Badge } from '../components/ui/Badge';
import { Button } from '../components/ui/Button';
import { Card } from '../components/ui/Card';
import { cn } from '../lib/utils';

const ScriptDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  
  // In a real app, this would be fetched from an API
  const script = {
    id: 1,
    title: 'Drift Master Pro',
    description: 'Enhance your drifting experience with powerful features like auto drift, speed hacks, and teleportation.',
    longDescription: `This Drift Master Pro script provides a comprehensive set of features to enhance your racing experience. From auto drifting to teleportation, this script has everything you need to dominate the track.
          Key Features:
          - Auto Drift: Automatically maintain perfect drifts around corners
          - Speed Hack: Boost your top speed beyond normal limits
          - Teleport: Instantly teleport to any location on the map
          - Auto Win: Automatically win races with perfect runs
          - Nitro Hack: Unlimited nitro for continuous boosting
          - User-friendly interface with easy-to-use controls
          - Regular updates to ensure compatibility with the latest game version`,
    category: 'drift',
    views: '85,421',
    rating: 4.9,
    ratingCount: 5245,
    updated: '3 days ago',
    version: '2.1.0',
    fileSize: '1.8 MB',
    tags: ['Auto Drift', 'Speed Hack', 'Teleport', 'Racing', 'Script'],
    requirements: ['Windows 7/10/11', 'Roblox installed', 'Executor (Synapse X, KRNL recommended)'],
    features: [
      'Auto Drift: Automatically maintain perfect drifts around corners',
      'Speed Hack: Boost your top speed beyond normal limits',
      'Teleport: Instantly teleport to any location on the map',
      'Auto Win: Automatically win races with perfect runs',
      'Nitro Hack: Unlimited nitro for continuous boosting',
      'User-friendly interface with easy-to-use controls',
      'Regular updates to ensure compatibility with the latest game version'
    ],
    instructions: [
      'Download the script file using the button below',
      'Launch your preferred Roblox executor (Synapse X, KRNL, etc.)',
      'Join a Drift Racing game',
      'Inject the executor and paste the script',
      'Press Execute and configure the settings to your preference',
      'Enjoy enhanced gameplay!'
    ],
    compatibility: [
      { name: 'Synapse X', status: 'fully' },
      { name: 'KRNL', status: 'fully' },
      { name: 'JJsploit', status: 'partial' },
      { name: 'Fluxus', status: 'fully' },
      { name: 'Electron', status: 'fully' },
    ],
    changelog: [
      {
        version: '2.1.0',
        date: '2023-11-20',
        changes: [
          'Added support for the latest game update',
          'Improved auto drift efficiency by 25%',
          'Fixed teleportation glitches',
          'Added new track locations',
        ],
      },
      {
        version: '2.0.5',
        date: '2023-11-10',
        changes: [
          'Added auto win feature',
          'Improved UI/UX',
          'Fixed minor bugs',
          'Optimized performance',
        ],
      },
    ],
  };

  const handleBackToScriptsClick = () => {
    navigate('/scripts');
  };

  const handleDownloadScriptClick = () => {
    // Logic for downloading the script
    console.log('Downloading script...');
  };

  const handleRateScriptClick = () => {
    // Logic for rating the script
    console.log('Rating script...');
  };

  return (
    <div className={cn("container mx-auto px-4 py-8")}>
      <div className={cn("max-w-6xl mx-auto")}>
        {/* Back Button */}
        <Link 
          to="/scripts" 
          className={cn("inline-flex items-center text-primary hover:underline mb-6")}
          aria-label="Back to Scripts page"
          tabIndex={0}
          onKeyDown={(e) => { 
            if (e.key === 'Enter' || e.key === 'Space') {
              e.preventDefault();
              handleBackToScriptsClick();
            }
          }}
        >
          <FiArrowLeft className={cn("mr-2")} aria-hidden="true" />
          Back to Scripts
        </Link>

        {/* Header */}
        <div className={cn("mb-8")}>
          <div className={cn("flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-4")}>
            <div className={cn("")}>
              <div className={cn("flex items-center gap-3")}>
                <h1 className={cn("text-3xl font-bold")}>{script.title}</h1>
                <span className={cn("px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-200")}>
                  Drift Racing
                </span>
              </div>
              <p className={cn("text-foreground/70")}>{script.description}</p>
            </div>
            <div className={cn("flex flex-col sm:flex-row gap-3")}>
              <Button 
                className={cn("gap-2 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white")}
                onClick={handleDownloadScriptClick}
                ariaLabel="Download Script"
              >
                <FiDownload className={cn("h-4 w-4")} aria-hidden="true" />
                Download Script
              </Button>
              <Button 
                variant="outline"
                className={cn("gap-2")}
                onClick={handleRateScriptClick}
                ariaLabel="Rate this script"
              >
                <FiStar className={cn("h-4 w-4")} aria-hidden="true" />
                Rate this script
              </Button>
              <Button 
                className={cn("gap-2")}
                onClick={handleDownloadScriptClick}
                ariaLabel={`Download version ${script.version}`}
              >
                <FiDownload className={cn("h-4 w-4")} aria-hidden="true" />
                Download (v{script.version})
              </Button>
            </div>
          </div>
          
          <div className={cn("flex flex-wrap items-center gap-2 text-sm text-foreground/70")}>
            <div className={cn("flex items-center")}>
              <FiStar className={cn("h-4 w-4 text-yellow-500 mr-1")} aria-hidden="true" />
              <span className={cn("font-medium")}>{script.rating}</span>
              <span className={cn("ml-1")}>({script.ratingCount} ratings)</span>
            </div>
            <span className={cn("")}>•</span>
            <div className={cn("flex items-center")}>
              <FiDownload className={cn("h-4 w-4 mr-1")} aria-hidden="true" />
              {script.downloads} downloads
            </div>
            <span className={cn("")}>•</span>
            <div className={cn("flex items-center")}>
              <FiClock className={cn("h-4 w-4 mr-1")} aria-hidden="true" />
              Updated {script.updated}
            </div>
            <span className={cn("")}>•</span>
            <div className={cn("")}>{script.fileSize}</div>
          </div>
          
          <div className={cn("flex flex-wrap gap-2 mt-4")}>
            {script.tags.map((tag) => (
              <Badge key={tag} variant="secondary" className={cn("")}>
                {tag}
              </Badge>
            ))}
          </div>
        </div>

        <div className={cn("grid grid-cols-1 lg:grid-cols-3 gap-8")}>
          {/* Main Content */}
          <div className={cn("lg:col-span-2 space-y-8")}>
            {/* Description */}
            <Card className={cn("p-6")}>
              <h2 className={cn("text-xl font-semibold mb-4")}>Description</h2>
              <div className={cn("prose prose-sm dark:prose-invert max-w-none")}>
                {script.longDescription.split('\n\n').map((paragraph, i) => (
                  <p key={i} className={cn("mb-4")}>{paragraph}</p>
                ))}
              </div>
            </Card>

            {/* Features */}
            <Card className={cn("p-6")}>
              <h2 className={cn("text-xl font-semibold mb-4")}>Features</h2>
              <div className={cn("grid grid-cols-1 md:grid-cols-2 gap-4")}>
                {script.features.map((feature, i) => (
                  <div key={i} className={cn("flex items-start")}>
                    <FiCheckCircle className={cn("h-5 w-5 text-green-500 mt-0.5 mr-2 flex-shrink-0")} aria-hidden="true" />
                    <span>{feature}</span>
                  </div>
                ))}
              </div>
            </Card>

            {/* Instructions */}
            <Card className={cn("p-6")}>
              <h2 className={cn("text-xl font-semibold mb-4")}>How to Install</h2>
              <ol className={cn("space-y-3")}>
                {script.instructions.map((step, i) => (
                  <li key={i} className={cn("flex")}>
                    <span className={cn("flex items-center justify-center h-6 w-6 rounded-full bg-primary/10 text-primary font-medium text-sm mr-3 flex-shrink-0")}>
                      {i + 1}
                    </span>
                    <span>{step}</span>
                  </li>
                ))}
              </ol>
            </Card>

            {/* Changelog */}
            <Card className={cn("p-6")}>
              <h2 className={cn("text-xl font-semibold mb-4")}>Changelog</h2>
              <div className={cn("space-y-6")}>
                {script.changelog.map((update, i) => (
                  <div key={i} className={cn("border-l-2 border-primary/20 pl-4")}>
                    <div className={cn("flex justify-between items-baseline")}>
                      <h3 className={cn("font-medium")}>Version {update.version}</h3>
                      <span className={cn("text-sm text-foreground/60")}>{update.date}</span>
                    </div>
                    <ul className={cn("mt-2 space-y-1 text-sm")}>
                      {update.changes.map((change, j) => (
                        <li key={j} className={cn("flex items-start")}>
                          <FiCheckCircle className={cn("h-4 w-4 text-primary mr-2 mt-1 flex-shrink-0")} aria-hidden="true" />
                          <span className={cn("")}>{change}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                ))}
              </div>
            </Card>
          </div>

          {/* Sidebar / Details */}
          <div className={cn("lg:col-span-1 space-y-8")}>
            <Card className={cn("p-6")}>
              <h2 className={cn("text-xl font-semibold mb-4")}>Script Details</h2>
              <div className={cn("space-y-3")}>
                <div className={cn("flex items-center text-foreground/70")}>
                  <FiCode className={cn("h-5 w-5 mr-3 text-primary")} aria-hidden="true" />
                  <span className={cn("font-medium")}>Category:</span>
                  <span className={cn("ml-auto text-foreground")}>Drift Racing</span>
                </div>
                <div className={cn("flex items-center text-foreground/70")}>
                  <FiUsers className={cn("h-5 w-5 mr-3 text-primary")} aria-hidden="true" />
                  <span className={cn("font-medium")}>Views:</span>
                  <span className={cn("ml-auto text-foreground")}>{script.views}</span>
                </div>
                <div className={cn("flex items-center text-foreground/70")}>
                  <FiClock className={cn("h-5 w-5 mr-3 text-primary")} aria-hidden="true" />
                  <span className={cn("font-medium")}>Last Updated:</span>
                  <span className={cn("ml-auto text-foreground")}>{script.updated}</span>
                </div>
                <div className={cn("flex items-center text-foreground/70")}>
                  <FiAlertTriangle className={cn("h-5 w-5 mr-3 text-primary")} aria-hidden="true" />
                  <span className={cn("font-medium")}>Version:</span>
                  <span className={cn("ml-auto text-foreground")}>{script.version}</span>
                </div>
                <div className={cn("flex items-center text-foreground/70")}>
                  <FiDownload className={cn("h-5 w-5 mr-3 text-primary")} aria-hidden="true" />
                  <span className={cn("font-medium")}>File Size:</span>
                  <span className={cn("ml-auto text-foreground")}>{script.fileSize}</span>
                </div>
              </div>
            </Card>

            {/* Compatibility */}
            <Card className={cn("p-6")}>
              <h2 className={cn("text-xl font-semibold mb-4")}>Compatibility</h2>
              <div className={cn("space-y-3")}>
                {script.compatibility.map((exe, i) => (
                  <div key={i} className={cn("flex items-center justify-between text-sm")}>
                    <span className={cn("font-medium")}>{exe.name}</span>
                    <Badge 
                      variant={exe.status === 'fully' ? 'success' : 'warning'}
                      className={cn("")}
                    >
                      {exe.status === 'fully' ? 'Fully Compatible' : 'Partial Compatibility'}
                    </Badge>
                  </div>
                ))}
              </div>
            </Card>

            {/* Requirements */}
            <Card className={cn("p-6")}>
              <h2 className={cn("text-xl font-semibold mb-4")}>Requirements</h2>
              <ul className={cn("list-disc pl-5 space-y-2 text-foreground/70")}>
                {script.requirements.map((req, i) => (
                  <li key={i} className={cn("")}>{req}</li>
                ))}
              </ul>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ScriptDetail;
