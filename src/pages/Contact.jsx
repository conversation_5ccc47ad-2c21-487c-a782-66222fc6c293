import { useState } from 'react';
import { motion } from 'framer-motion';
import { FiMessageCircle, FiHelpCircle, FiMail, FiUser, FiEdit3, FiSend, FiClock, FiCheckCircle, FiExternalLink } from 'react-icons/fi';
import { Link } from 'react-router-dom';

const Contact = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const contactMethods = [
    {
      icon: FiMessageCircle,
      title: 'Discord Community',
      description: 'Join our community for real-time help and support',
      value: 'Join Our Discord',
      action: 'https://discord.gg/uh',
      isExternal: true,
      color: 'from-blue-500/20 to-purple-500/20',
      iconColor: 'text-blue-400'
    },
    {
      icon: FiHelpCircle,
      title: 'FAQ Center',
      description: 'Find answers to frequently asked questions',
      value: 'Browse FAQ',
      action: '/faq',
      isExternal: false,
      color: 'from-green-500/20 to-blue-500/20',
      iconColor: 'text-green-400'
    },

  ];


  return (
    <div className="min-h-screen pt-16 bg-gradient-to-br from-gray-50 via-blue-50/30 to-purple-50/30 dark:from-gray-900 dark:via-blue-900/20 dark:to-purple-900/20 relative overflow-hidden">
      {/* Background effects */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5 dark:opacity-10"></div>
      <motion.div
        className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-r from-blue-400/20 to-purple-400/20 rounded-full blur-xl"
        animate={{
          y: [0, -20, 0],
          x: [0, 10, 0],
        }}
        transition={{
          duration: 6,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />
      <motion.div
        className="absolute bottom-20 right-10 w-24 h-24 bg-gradient-to-r from-purple-400/20 to-pink-400/20 rounded-full blur-xl"
        animate={{
          y: [0, 15, 0],
          x: [0, -15, 0],
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 1
        }}
      />
      <motion.div
        className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-40 h-40 bg-gradient-to-r from-green-400/10 to-blue-400/10 rounded-full blur-2xl"
        animate={{
          scale: [1, 1.2, 1],
          opacity: [0.3, 0.5, 0.3],
        }}
        transition={{
          duration: 10,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 2
        }}
      />

      <div className="container relative mx-auto px-4 py-12 md:py-16">
        {/* Header */}
        <motion.div
          className="text-center mb-12"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <motion.div
            className="inline-block px-6 py-3 mb-6 rounded-full bg-gradient-to-r from-blue-500/10 to-purple-500/10 backdrop-blur-sm border border-blue-500/20 text-primary text-sm font-semibold shadow-lg"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <span className="flex items-center">
              <FiMail className="mr-2 h-4 w-4" />
              Get in Touch
            </span>
          </motion.div>

          <motion.h1
            className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 bg-clip-text text-transparent leading-tight"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            Contact Us
          </motion.h1>
          <motion.p
            className="text-lg text-foreground/80 max-w-2xl mx-auto leading-relaxed"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            Have questions or need support? Our team is here to help you with any inquiries about 6FootScripts.
          </motion.p>
        </motion.div>

        {/* Contact Methods */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-16 max-w-5xl mx-auto"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.5 }}
        >
          {contactMethods.map((method, index) => (
            <motion.div
              key={index}
              className="p-6 bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl shadow-2xl hover:bg-white/15 transition-all duration-300 group"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.6 + index * 0.1 }}
              whileHover={{ scale: 1.02, y: -5 }}
            >
              <div className={`p-4 bg-gradient-to-r ${method.color} rounded-xl mb-4 w-fit mx-auto group-hover:scale-110 transition-transform duration-300`}>
                <method.icon className={`h-8 w-8 ${method.iconColor}`} />
              </div>
              <h3 className="text-xl font-bold text-foreground mb-2 text-center">{method.title}</h3>
              <p className="text-foreground/70 mb-4 text-center text-sm leading-relaxed">{method.description}</p>
              {method.isExternal ? (
                <a
                  href={method.action}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center justify-center px-4 py-2 bg-gradient-to-r from-blue-500/20 to-purple-500/20 backdrop-blur-sm border border-blue-500/30 rounded-xl text-blue-400 hover:from-blue-500/30 hover:to-purple-500/30 hover:border-blue-400/50 transition-all duration-300 group/btn"
                >
                  <span className="mr-2 text-sm font-medium">{method.value}</span>
                  <FiExternalLink className="h-4 w-4 group-hover/btn:translate-x-1 transition-transform duration-300" />
                </a>
              ) : (
                <Link
                  to={method.action}
                  className="flex items-center justify-center px-4 py-2 bg-gradient-to-r from-blue-500/20 to-purple-500/20 backdrop-blur-sm border border-blue-500/30 rounded-xl text-blue-400 hover:from-blue-500/30 hover:to-purple-500/30 hover:border-blue-400/50 transition-all duration-300 group/btn"
                >
                  <span className="mr-2 text-sm font-medium">{method.value}</span>
                  <FiExternalLink className="h-4 w-4 group-hover/btn:translate-x-1 transition-transform duration-300" />
                </Link>
              )}
            </motion.div>
          ))}
        </motion.div>

        {/* Contact Form */}
        <motion.div
          className="max-w-2xl mx-auto"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
        >
          <div className="p-8 bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl shadow-2xl">
            <div className="text-center mb-8">
              <h2 className="text-2xl font-bold text-foreground mb-2">Send us a Message</h2>
              <p className="text-foreground/70">Fill out the form below and we'll get back to you as soon as possible.</p>
            </div>

            {isSubmitted && (
              <motion.div
                className="mb-6 p-4 bg-green-500/10 border border-green-500/20 rounded-xl flex items-center"
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.3 }}
              >
                <FiCheckCircle className="h-5 w-5 text-green-400 mr-3" />
                <span className="text-green-200 font-medium">Message sent successfully! We'll get back to you soon.</span>
              </motion.div>
            )}

            <motion.div
              className="mt-8 p-4 bg-blue-500/10 border border-blue-500/20 rounded-xl"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 1.4 }}
            >
              <div className="flex items-center text-blue-200">
                <FiClock className="h-4 w-4 mr-2" />
                <span className="text-sm">
                  <strong>Response Time:</strong> We typically respond within 24-48 hours.
                </span>
              </div>
            </motion.div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default Contact;
