export class MLBehaviorAnalyzer {
  constructor() {
    this.behaviorHistory = [];
    this.isModelLoaded = false; // Always false since we're not using TensorFlow.js
  }

  async loadModel() {
    // No-op since we're using rule-based detection only
    console.log('🔒 Using rule-based behavioral analysis (TensorFlow.js disabled for production)');
    console.log('🛡️ Enhanced security monitoring enabled - Full protection mode active');
    this.isModelLoaded = true; // Mark as loaded for rule-based system
  }

  extractFeatures(behaviorData) {
    return [
      Array.isArray(behaviorData?.mouseMovements) ? behaviorData.mouseMovements.length / 100 : 0,
      Array.isArray(behaviorData?.clickPatterns) ? behaviorData.clickPatterns.length / 50 : 0,
      (behaviorData?.sessionDuration || 0) / 300000, // Normalize to 5 minutes
      (behaviorData?.focusLossCount || 0) / 10,
      behaviorData?.mouseMetrics?.variance || 0,
      behaviorData?.clickMetrics?.variance || 0,
      behaviorData?.typingMetrics?.wpm / 200 || 0,
      behaviorData?.scrollMetrics?.speed / 10 || 0,
      behaviorData?.anomalyScore || 0,
      Date.now() % 1000 / 1000 // Time entropy
    ];
  }

  async predict(behaviorData) {
    // Always use rule-based detection
    return this.fallbackDetection(behaviorData);
  }

  fallbackDetection(behaviorData) {
    // Enhanced rule-based detection with admin-aware protection
    let botScore = 0;
    console.log('🔍 Behavior data sent to analyzer:', behaviorData);

    // Check if user is authenticated admin/owner
    const isAdminUser = this.checkAdminStatus();
    if (isAdminUser) {
      console.log('👑 Admin/Owner detected - allowing normal operation with monitoring');
      return { isBot: false, confidence: 0, method: 'admin-bypass', adminUser: true };
    }

    const isSafari = typeof navigator !== 'undefined' && /Safari/.test(navigator.userAgent) && !/Chrome/.test(navigator.userAgent);
    const isMobile = typeof navigator !== 'undefined' && /iPhone|iPad|Android|Mobile/.test(navigator.userAgent);

    if (isSafari || isMobile) {
      console.log('📱 Mobile/Safari browser detected - reduced security checks');
      return { isBot: false, confidence: 0, method: 'mobile-bypass' };
    }

    const hasActivity = (Array.isArray(behaviorData?.mouseMovements) && behaviorData.mouseMovements.length > 0)
      || (Array.isArray(behaviorData?.clickPatterns) && behaviorData.clickPatterns.length > 0)
      || (Array.isArray(behaviorData?.typingSpeed) && behaviorData.typingSpeed.length > 0);

    if (!hasActivity) {
      console.log('🚨 No user activity detected - potential bot behavior');
      return { isBot: true, confidence: 1, method: 'no-activity-detection' };
    }

    const features = this.extractFeatures(behaviorData);

    // Mouse movement analysis
    if (features[0] < 0.1) botScore += 0.3; // Low mouse movement
    if ((features[4] || 0) < 0.01) botScore += 0.2; // Low mouse variance

    // Click pattern analysis
    if (features[1] < 0.05) botScore += 0.3; // Low click count
    if ((features[5] || 0) < 0.01) botScore += 0.2; // Low click variance

    // Session analysis
    if (features[2] < 0.1) botScore += 0.3; // Very short session
    if (features[3] > 0.5) botScore += 0.2; // High focus loss

    // Typing and scrolling analysis
    if ((features[6] || 0) > 0.8) botScore += 0.2; // Very fast typing
    if ((features[7] || 0) > 0.9) botScore += 0.2; // Very fast scrolling

    // Anomaly score
    if ((features[8] || 0) > 0.8) botScore += 0.4; // High anomaly score

    // Additional behavioral checks
    if ((behaviorData?.anomalyScore || 0) > 0.8) botScore += 0.3;
    if ((behaviorData?.sessionDuration || 0) < 10000) botScore += 0.2;
    if ((behaviorData?.focusLossCount || 0) > 5) botScore += 0.1;
    if (!Array.isArray(behaviorData?.mouseMovements) || behaviorData.mouseMovements.length < 10) botScore += 0.2;

    // Check for suspicious flags
    const suspiciousFlags = behaviorData?.suspiciousFlags || {};
    if (suspiciousFlags.straightLineMovement) botScore += 0.2;
    if (suspiciousFlags.regularClicks) botScore += 0.2;
    if (suspiciousFlags.noMouseMovement) botScore += 0.3;
    if (suspiciousFlags.rapidClicks) botScore += 0.2;

    const result = {
      isBot: botScore > 0.7,
      confidence: Math.min(botScore, 1.0),
      method: 'enhanced-rule-based',
      securityLevel: 'full-protection'
    };

    // Enhanced logging for security monitoring
    if (result.isBot) {
      console.log('🚨 SECURITY ALERT: Bot behavior detected', {
        confidence: result.confidence,
        score: botScore,
        timestamp: new Date().toISOString()
      });
    } else if (result.confidence > 0.3) {
      console.log('⚠️ SECURITY WARNING: Suspicious behavior detected', {
        confidence: result.confidence,
        score: botScore,
        timestamp: new Date().toISOString()
      });
    } else {
      console.log('✅ SECURITY CHECK: Normal user behavior confirmed', {
        confidence: result.confidence,
        timestamp: new Date().toISOString()
      });
    }

    return result;
  }

  // Check if current user is authenticated admin/owner
  checkAdminStatus() {
    try {
      // Check localStorage for admin token
      const adminToken = localStorage.getItem('adminToken');
      if (adminToken) {
        // Basic token validation (in production, this should be more robust)
        try {
          const tokenData = JSON.parse(atob(adminToken.split('.')[1]));
          const isAdmin = tokenData.role === 'admin' || tokenData.role === 'owner';
          if (isAdmin) {
            console.log('👑 Admin privileges confirmed for user:', tokenData.role);
            return true;
          }
        } catch (e) {
          console.log('⚠️ Invalid admin token detected');
        }
      }

      // Check session storage as backup
      const userRole = sessionStorage.getItem('userRole');
      if (userRole === 'admin' || userRole === 'owner') {
        console.log('👑 Admin privileges confirmed via session:', userRole);
        return true;
      }

      return false;
    } catch (error) {
      console.log('❌ Error checking admin status:', error);
      return false;
    }
  }

  analyzeRealtime(behaviorData) {
    // Check admin status first
    const isAdminUser = this.checkAdminStatus();
    if (isAdminUser) {
      console.log('👑 Real-time monitoring: Admin user - normal operation allowed');
      return {
        isBot: false,
        confidence: 0,
        anomalyScore: 0,
        riskLevel: 'LOW',
        adminUser: true,
        method: 'admin-bypass'
      };
    }

    const features = this.extractFeatures(behaviorData);
    const anomalyScore = this.calculateAnomalyScore(features);
    const confidence = this.classifyBehavior(features);

    this.behaviorHistory.push(features);

    const result = {
      isBot: confidence > 0.85,
      confidence: confidence,
      anomalyScore: anomalyScore,
      riskLevel: this.calculateRiskLevel(confidence, anomalyScore),
      method: 'realtime-analysis'
    };

    // Enhanced real-time logging
    if (anomalyScore > 0.8) {
      console.log('🚨 REAL-TIME ALERT: High anomaly score detected', {
        anomalyScore,
        confidence,
        riskLevel: result.riskLevel,
        timestamp: new Date().toISOString()
      });

      this.behaviorHistory.push({
        timestamp: Date.now(),
        score: anomalyScore,
        features: features,
        alertLevel: 'HIGH'
      });
    } else if (confidence > 0.7) {
      console.log('⚠️ REAL-TIME WARNING: Elevated bot confidence', {
        confidence,
        anomalyScore,
        riskLevel: result.riskLevel,
        timestamp: new Date().toISOString()
      });
    } else {
      console.log('✅ REAL-TIME CHECK: Normal behavior patterns', {
        confidence: confidence.toFixed(3),
        anomalyScore: anomalyScore.toFixed(3),
        riskLevel: result.riskLevel
      });
    }

    return result;
  }

  calculateAnomalyScore(features) {
    let score = 0;
    const weights = {
      mouseVelocityVariance: 0.2,
      mouseAccelerationPattern: 0.15,
      clickIntervalRegularity: 0.25,
      keystrokeRhythm: 0.2,
      sessionProgression: 0.2
    };
    
    // Compare against known patterns
    for (const [feature, weight] of Object.entries(weights)) {
      if (features[feature] !== undefined) {
        score += this.compareToBaseline(feature, features[feature]) * weight;
      }
    }
    
    return Math.min(score, 1.0);
  }

  classifyBehavior(features) {
    // Simple classification based on feature thresholds
    let botIndicators = 0;
    
    if (features[0] < 0.1) botIndicators++; // Low mouse movement
    if (features[1] < 0.05) botIndicators++; // Low click count
    if (features[2] < 0.1) botIndicators++; // Very short session
    if (features[3] > 0.5) botIndicators++; // High focus loss
    if (features[4] < 0.01) botIndicators++; // Low mouse variance
    if (features[5] < 0.01) botIndicators++; // Low click variance
    if (features[6] > 0.8) botIndicators++; // Very fast typing
    if (features[7] > 0.9) botIndicators++; // Very fast scrolling
    if (features[8] > 0.8) botIndicators++; // High anomaly score
    
    return botIndicators / 9; // Normalize to 0-1
  }

  calculateRiskLevel(confidence, anomalyScore) {
    const combinedScore = (confidence + anomalyScore) / 2;
    
    if (combinedScore > 0.9) return 'CRITICAL';
    if (combinedScore > 0.7) return 'HIGH';
    if (combinedScore > 0.5) return 'MEDIUM';
    return 'LOW';
  }

  compareToBaseline(feature, value) {
    // Simple baseline comparison - in production, you'd use historical data
    const baselines = {
      mouseVelocityVariance: 0.5,
      mouseAccelerationPattern: 0.3,
      clickIntervalRegularity: 0.4,
      keystrokeRhythm: 0.6,
      sessionProgression: 0.5
    };
    
    const baseline = baselines[feature] || 0.5;
    return Math.abs(value - baseline) / baseline;
  }

  getBehaviorSummary() {
    return {
      totalSessions: this.behaviorHistory.length,
      averageConfidence: this.behaviorHistory.reduce((sum, h) => sum + h.confidence, 0) / this.behaviorHistory.length,
      riskLevel: this.calculateOverallRiskLevel()
    };
  }

  calculateOverallRiskLevel() {
    const highRiskSessions = this.behaviorHistory.filter(h => h.confidence > 0.7).length;
    const riskRatio = highRiskSessions / this.behaviorHistory.length;
    
    if (riskRatio > 0.5) return 'HIGH';
    if (riskRatio > 0.2) return 'MEDIUM';
    return 'LOW';
  }
}

// Export a singleton instance for easy use
export const mlAnalyzer = new MLBehaviorAnalyzer(); 