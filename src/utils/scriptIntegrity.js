// Script Integrity and Anti-Debugging Protection System
export class ScriptIntegrityProtector {
  constructor() {
    this.integrityChecks = [];
    this.debuggingDetected = false;
    this.consoleAccessDetected = false;
    this.tamperingDetected = false;
    this.honeypotTriggers = [];
    this.monitoringActive = false;
  }

  // Initialize all protection systems
  initialize() {
    try {
      // Add safety checks for browser environment
      if (typeof window === 'undefined') {
        console.warn('ScriptIntegrityProtector: Not in browser environment, skipping initialization');
        return;
      }

      this.startIntegrityMonitoring();
      this.startAntiDebugging();
      this.startConsoleMonitoring();
      this.setupHoneypotTraps();
      this.monitoringActive = true;
    } catch (error) {
      console.error('ScriptIntegrityProtector initialization failed:', error);
      // Don't throw to prevent breaking the application
    }
  }

  // Script Integrity Verification
  startIntegrityMonitoring() {
    // Only check for actual script tampering, not normal code changes
    // Monitor for script injection attempts
    this.monitorScriptInjection();
    
    // Check for obvious tampering indicators
    this.checkForTamperingIndicators();
  }

  // Anti-Debugging Detection
  startAntiDebugging() {
    // Check for developer tools
    this.checkDeveloperTools();
    
    // Monitor for debugging patterns
    this.monitorDebuggingPatterns();
    
    // Detect performance timing anomalies
    this.detectTimingAnomalies();
    
    // Check for debugging APIs
    this.checkDebuggingAPIs();
  }

  // Console Access Monitoring
  startConsoleMonitoring() {
    // Override console methods to detect access
    const self = this;
    const originalConsole = {
      log: console.log,
      warn: console.warn,
      error: console.error,
      info: console.info,
      debug: console.debug
    };

    Object.keys(originalConsole).forEach(method => {
      console[method] = function (...args) {
        self.consoleAccessDetected = true;
        // Only log, do not trigger a violation or shutdown for console access
        // Do NOT call console methods here to avoid recursion
        // Args are captured but not used to prevent recursion
        // Optionally, you could send a beacon or log to backend if needed
        // originalConsole[method].apply(console, args); // Uncomment if you want to preserve original logging
      };
    });
  }

  // Honeypot Traps
  setupHoneypotTraps() {
    // Fake sensitive data
    this.createFakeSensitiveData();
    
    // Fake API endpoints
    this.createFakeEndpoints();
    
    // Fake vulnerabilities
    this.createFakeVulnerabilities();
    
    // Monitor for honeypot interactions
    this.monitorHoneypotInteractions();
  }

  // Developer Tools Detection
  checkDeveloperTools() {
    // Method 1: Check window dimensions
    const checkDevTools = () => {
      const threshold = 160;
      const widthThreshold = window.outerWidth - window.innerWidth > threshold;
      const heightThreshold = window.outerHeight - window.innerHeight > threshold;
      
      if (widthThreshold || heightThreshold) {
        this.debuggingDetected = true;
        // Just log, don't trigger critical violation for devtools
        // DevTools detected via dimension check
      }
    };

    // Method 2: Check for debugging properties
    const checkDebugProps = () => {
      const debugProps = [
        'firebug',
        'console.profiles',
        'console.profileEnd',
        'debug',
        'debugger'
      ];

      debugProps.forEach(prop => {
        if (window[prop] || console[prop]) {
          this.debuggingDetected = true;
          // Just log, don't trigger critical violation for debug props
          // Debug property detected
        }
      });
    };

    // Method 3: Check for debugging functions
    const checkDebugFunctions = () => {
      const debugFunctions = [
        'debugger',
        'console.trace',
        'console.profile',
        'console.profileEnd'
      ];

      debugFunctions.forEach(func => {
        try {
          // Safer alternative to eval - use window property access
          const funcRef = func.split('.').reduce((obj, prop) => obj && obj[prop], window);
          if (typeof funcRef === 'function') {
            this.debuggingDetected = true;
            // Just log, don't trigger critical violation for debug functions
            // Debug function detected
          }
        } catch (e) {
          // Expected for some functions
        }
      });
    };

    // Run checks periodically
    setInterval(checkDevTools, 1000);
    setInterval(checkDebugProps, 2000);
    setInterval(checkDebugFunctions, 3000);
  }

  // Monitor Debugging Patterns
  monitorDebuggingPatterns() {
    // Check for breakpoint patterns (less aggressive)
    const checkBreakpoints = () => {
      const start = performance.now();
      debugger;
      const end = performance.now();
      
      if (end - start > 100) {
        this.debuggingDetected = true;
        // Only log, don't trigger critical violation for breakpoints
        // Breakpoint detected via timing check
      }
    };

    // Check for step-through patterns (less aggressive)
    const checkStepThrough = () => {
      let stepCount = 0;
      const originalEval = window.eval;

      // Monitor eval usage without using eval directly
      if (originalEval) {
        window.eval = function(code) {
          stepCount++;
          if (stepCount > 50) { // Increased threshold
            self.debuggingDetected = true;
            // Only log, don't trigger critical violation for eval usage
            // Excessive eval usage detected
          }
          return originalEval.call(this, code);
        };
      }
    };

    // Run pattern checks less frequently
    setInterval(checkBreakpoints, 10000); // Less frequent
    checkStepThrough();
  }

  // Detect Timing Anomalies
  detectTimingAnomalies() {
    try {
      const self = this; // Capture the class instance reference

      // Check for performance timing manipulation (less aggressive)
      const checkPerformanceTiming = () => {
        if (typeof performance !== 'undefined' && performance.now) {
          const originalNow = performance.now;
          let callCount = 0;

          performance.now = function() {
            callCount++;
            if (callCount > 5000) { // Increased threshold
              self.debuggingDetected = true;
              // Only log, don't trigger critical violation for timing manipulation
              // Performance timing manipulation detected
            }
            return originalNow.call(this);
          };
        }
      };

    // Check for setTimeout/setInterval manipulation
    const checkTimerManipulation = () => {
      const originalSetTimeout = window.setTimeout;
      const originalSetInterval = window.setInterval;

      window.setTimeout = function(fn, delay) {
        if (delay < 1) {
          self.debuggingDetected = true;
          self.triggerSecurityViolation('timer_manipulation', 'setTimeout');
        }
        return originalSetTimeout.call(this, fn, delay);
      };

      window.setInterval = function(fn, delay) {
        if (delay < 1) {
          self.debuggingDetected = true;
          self.triggerSecurityViolation('timer_manipulation', 'setInterval');
        }
        return originalSetInterval.call(this, fn, delay);
      };
    };

      checkPerformanceTiming();
      checkTimerManipulation();
    } catch (error) {
      console.error('detectTimingAnomalies failed:', error);
    }
  }

  // Check Debugging APIs
  checkDebuggingAPIs() {
    // Check for Node.js debugging
    if (typeof process !== 'undefined' && process.env && process.env.NODE_ENV === 'development') {
      this.debuggingDetected = true;
      this.triggerSecurityViolation('node_debugging', 'NODE_ENV');
    }

    // Check for React DevTools
    if (window.__REACT_DEVTOOLS_GLOBAL_HOOK__) {
      this.debuggingDetected = true;
      this.triggerSecurityViolation('react_devtools', 'global_hook');
    }

    // Check for Redux DevTools
    if (window.__REDUX_DEVTOOLS_EXTENSION__) {
      this.debuggingDetected = true;
      this.triggerSecurityViolation('redux_devtools', 'extension');
    }
  }

  // Monitor Script Injection
  monitorScriptInjection() {
    const self = this; // Capture the class instance reference

    // Monitor for dynamic script creation
    const originalCreateElement = document.createElement;
    document.createElement = function(tagName) {
      if (tagName.toLowerCase() === 'script') {
        self.triggerSecurityViolation('script_injection', 'createElement');
      }
      return originalCreateElement.call(this, tagName);
    };

    // Monitor for eval usage
    const originalEval = window.eval;
    if (originalEval) {
      window.eval = function(code) {
        self.triggerSecurityViolation('eval_usage', 'eval');
        return originalEval.call(this, code);
      };
    }
  }

  // Create Fake Sensitive Data
  createFakeSensitiveData() {
    const self = this; // Capture the class instance reference

    // Fake API keys
    window.fakeApiKey = 'sk_test_fake_key_123456789';
    window.fakeSecretKey = 'secret_fake_key_987654321';

    // Fake endpoints
    window.fakeEndpoints = {
      admin: '/api/admin/keys',
      debug: '/api/debug/info',
      bypass: '/api/bypass/security'
    };

    // Monitor access to fake data
    Object.keys(window.fakeEndpoints).forEach(key => {
      Object.defineProperty(window.fakeEndpoints, key, {
        get: function() {
          self.triggerSecurityViolation('honeypot_access', `fake_endpoint_${key}`);
          return `/fake/${key}`;
        }
      });
    });
  }

  // Create Fake Endpoints
  createFakeEndpoints() {
    const self = this; // Capture the class instance reference

    // Fake admin endpoint
    if (typeof fetch !== 'undefined') {
      const originalFetch = window.fetch;
      window.fetch = function(url, options) {
        if (typeof url === 'string' && url.includes('/api/admin/')) {
          self.triggerSecurityViolation('honeypot_endpoint', 'admin_api');
        }
        if (typeof url === 'string' && url.includes('/api/debug/')) {
          self.triggerSecurityViolation('honeypot_endpoint', 'debug_api');
        }
        return originalFetch.call(this, url, options);
      };
    }
  }

  // Create Fake Vulnerabilities
  createFakeVulnerabilities() {
    const self = this; // Capture the class instance reference

    // Fake XSS vulnerability
    window.fakeXSS = function(input) {
      self.triggerSecurityViolation('honeypot_vulnerability', 'fake_xss');
      return input;
    };

    // Fake SQL injection vulnerability
    window.fakeSQLInjection = function(query) {
      self.triggerSecurityViolation('honeypot_vulnerability', 'fake_sql');
      return query;
    };
  }

  // Monitor Honeypot Interactions
  monitorHoneypotInteractions() {
    // Track all honeypot triggers
    this.honeypotTriggers = [];
    
    // Log honeypot interactions
    setInterval(() => {
      if (this.honeypotTriggers.length > 0) {
        // Honeypot interactions detected
        this.honeypotTriggers = [];
      }
    }, 10000);
  }

  // Utility Functions
  calculateScriptHash(code) {
    let hash = 0;
    for (let i = 0; i < code.length; i++) {
      const char = code.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return hash.toString(16);
  }

  getDeviceFingerprintCode() {
    // Return a simplified version of the device fingerprint code
    return 'getUnbreakableFingerprint';
  }

  getMLAnalysisCode() {
    // Return a simplified version of the ML analysis code
    return 'MLBehaviorAnalyzer';
  }

  getBehaviorTrackerCode() {
    // Return a simplified version of the behavior tracker code
    return 'getBehaviorData';
  }

  getScriptCode(scriptName) {
    // In a real implementation, you'd get the actual script content
    // For now, return a placeholder
    return scriptName;
  }

  // Security Violation Handler
  triggerSecurityViolation(type, details) {
    const violation = {
      type,
      details,
      timestamp: Date.now(),
      url: window.location.href,
      userAgent: navigator.userAgent
    };

    // Log violation (but do NOT log for console access to avoid recursion)
    if (type !== 'console_access') {
      // Security violation detected - details removed for production
    }
    
    // Add to honeypot triggers if applicable
    if (type.startsWith('honeypot_')) {
      this.honeypotTriggers.push(violation);
    }

    // Send to backend for logging
    this.reportViolation(violation);

    // Only shutdown for actual script tampering
    switch (type) {
      case 'suspicious_script_injection':
      case 'function_modification':
        // Only actual script tampering triggers shutdown
        this.shutdown('Script tampering detected');
        break;
      case 'script_tampering':
      case 'dev_tools_detected':
      case 'debug_props_detected':
      case 'debug_functions_detected':
        // Relax checks for Safari
        if (/Safari/.test(navigator.userAgent) && !/Chrome/.test(navigator.userAgent)) {
          // Safari detected, do not block, just log
          // Safari browser detected, skipping shutdown
          break;
        }
        // Just log these detections, don't shutdown
        // Security detection logged
        break;
      case 'console_access':
        // Only log, do not shutdown for console access
        break;
      default:
        // Log other violations but don't shutdown
        // Other violation detected
        break;
    }
  }

  // Report Violation to Backend
  async reportViolation(violation) {
    try {
      await fetch('/.netlify/functions/security-violation', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(violation)
      });
    } catch (error) {
      // Failed to report security violation
    }
  }

  // Shutdown Function
  shutdown(reason) {
    // Security shutdown initiated for reason: ${reason}
    console.log(`🚨 Security shutdown initiated: ${reason}`);

    // Clear sensitive data
    if (window.fakeApiKey) delete window.fakeApiKey;
    if (window.fakeSecretKey) delete window.fakeSecretKey;
    if (window.fakeEndpoints) delete window.fakeEndpoints;
    
    // Disable key generation
    window.keyGenerationDisabled = true;
    
    // Show error to user
    const errorDiv = document.createElement('div');
    errorDiv.style.cssText = 'position:fixed;top:0;left:0;width:100%;height:100%;background:red;color:white;z-index:9999;display:flex;align-items:center;justify-content:center;font-size:24px;';
    errorDiv.textContent = 'Security violation detected. Access denied.';
    document.body.appendChild(errorDiv);
  }

  // Get Security Status
  // Check for obvious tampering indicators
  checkForTamperingIndicators() {
    // Check for script tag injection
    const checkScriptInjection = () => {
      const scripts = document.querySelectorAll('script');
      const suspiciousScripts = Array.from(scripts).filter(script => {
        return script.src.includes('eval') || 
               script.src.includes('debugger') ||
               script.innerHTML.includes('eval(') ||
               script.innerHTML.includes('debugger');
      });
      
      if (suspiciousScripts.length > 0) {
        this.tamperingDetected = true;
        this.triggerSecurityViolation('suspicious_script_injection', 'suspicious_script_injection');
      }
    };

    // Check for function modification
    const checkFunctionModification = () => {
      const criticalFunctions = [
        'getDeviceFingerprint',
        'validateKey',
        'generateKey'
      ];

      criticalFunctions.forEach(funcName => {
        if (typeof window[funcName] === 'function') {
          const funcString = window[funcName].toString();
          if (funcString.includes('debugger') || funcString.includes('eval(')) {
            this.tamperingDetected = true;
            this.triggerSecurityViolation('function_modification', `function_${funcName}_modified`);
          }
        }
      });
    };

    // Run checks periodically
    setInterval(checkScriptInjection, 5000);
    setInterval(checkFunctionModification, 10000);
  }

  getSecurityStatus() {
    return {
      integrityChecks: this.integrityChecks.length,
      debuggingDetected: this.debuggingDetected,
      consoleAccessDetected: this.consoleAccessDetected,
      tamperingDetected: this.tamperingDetected,
      honeypotTriggers: this.honeypotTriggers.length,
      monitoringActive: this.monitoringActive
    };
  }
}

// Export singleton instance
export const scriptProtector = new ScriptIntegrityProtector(); 