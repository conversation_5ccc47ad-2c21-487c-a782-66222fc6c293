// Behavioral tracking and anomaly detection
let trackerInitialized = false;
class BehaviorTracker {
  constructor() {
    console.log('BehaviorTracker constructor called');
    this.behaviorData = {
      mouseMovements: [],
      clickPatterns: [],
      typingSpeed: [],
      sessionStartTime: Date.now(),
      pageVisits: [],
      scrollBehavior: [],
      focusLost: 0
    };
    if (!trackerInitialized) {
      this.init();
      trackerInitialized = true;
    }
  }
  
  init() {
    console.log('BehaviorTracker init called');
    if (typeof window === 'undefined') return;
    
    // Track mouse movements
    document.addEventListener('mousemove', this.trackMouseMovement.bind(this));
    
    // Track clicks
    document.addEventListener('click', this.trackClick.bind(this));
    
    // Track typing speed
    document.addEventListener('keydown', this.trackKeypress.bind(this));
    
    // Track scroll behavior
    window.addEventListener('scroll', this.trackScroll.bind(this));
    
    // Track focus changes (tab switching)
    window.addEventListener('blur', this.trackFocusLoss.bind(this));
    window.addEventListener('focus', this.trackFocusGain.bind(this));
    
    // Track page visibility changes
    document.addEventListener('visibilitychange', this.trackVisibilityChange.bind(this));
  }
  
  trackMouseMovement(event) {
    const now = Date.now();
    this.behaviorData.mouseMovements.push({
      x: event.clientX,
      y: event.clientY,
      timestamp: now
    });
    
    // Keep only last 100 movements
    if (this.behaviorData.mouseMovements.length > 100) {
      this.behaviorData.mouseMovements.shift();
    }
  }
  
  trackClick(event) {
    const now = Date.now();
    this.behaviorData.clickPatterns.push({
      x: event.clientX,
      y: event.clientY,
      timestamp: now,
      target: event.target.tagName
    });
    
    // Keep only last 50 clicks
    if (this.behaviorData.clickPatterns.length > 50) {
      this.behaviorData.clickPatterns.shift();
    }
  }
  
  trackKeypress(event) {
    const now = Date.now();
    this.behaviorData.typingSpeed.push(now);
    
    // Keep only last 50 keypresses
    if (this.behaviorData.typingSpeed.length > 50) {
      this.behaviorData.typingSpeed.shift();
    }
  }
  
  trackScroll(event) {
    const now = Date.now();
    this.behaviorData.scrollBehavior.push({
      scrollY: window.scrollY,
      timestamp: now
    });
    
    // Keep only last 30 scroll events
    if (this.behaviorData.scrollBehavior.length > 30) {
      this.behaviorData.scrollBehavior.shift();
    }
  }
  
  trackFocusLoss() {
    this.behaviorData.focusLost++;
  }
  
  trackFocusGain() {
    // Reset if too many focus losses (potential bot behavior)
    if (this.behaviorData.focusLost > 10) {
      this.flagSuspiciousBehavior('excessive_focus_loss');
    }
  }
  
  trackVisibilityChange() {
    if (document.hidden) {
      this.behaviorData.pageVisits.push({
        type: 'hidden',
        timestamp: Date.now()
      });
    } else {
      this.behaviorData.pageVisits.push({
        type: 'visible',
        timestamp: Date.now()
      });
    }
  }
  
  // Calculate behavior metrics
  calculateMetrics() {
    const now = Date.now();
    const sessionDuration = now - this.behaviorData.sessionStartTime;
    
    // Mouse movement analysis
    const mouseMetrics = this.analyzeMouseMovements();
    
    // Click analysis
    const clickMetrics = this.analyzeClickPatterns();
    
    // Typing speed analysis
    const typingMetrics = this.analyzeTypingSpeed();
    
    // Scroll behavior analysis
    const scrollMetrics = this.analyzeScrollBehavior();
    
    return {
      sessionDuration,
      mouseMetrics,
      clickMetrics,
      typingMetrics,
      scrollMetrics,
      focusLossCount: this.behaviorData.focusLost,
      anomalyScore: this.calculateAnomalyScore(mouseMetrics, clickMetrics, typingMetrics, scrollMetrics, sessionDuration, this.behaviorData.focusLost)
    };
  }
  
  analyzeMouseMovements() {
    const movements = this.behaviorData.mouseMovements;
    if (movements.length < 2) return { variance: 0, speed: 0, straightLine: false };
    
    let totalDistance = 0;
    let totalTime = 0;
    let deltaX = 0;
    let deltaY = 0;
    
    for (let i = 1; i < movements.length; i++) {
      const prev = movements[i - 1];
      const curr = movements[i];
      
      const dx = curr.x - prev.x;
      const dy = curr.y - prev.y;
      const distance = Math.sqrt(dx * dx + dy * dy);
      const time = curr.timestamp - prev.timestamp;
      
      totalDistance += distance;
      totalTime += time;
      deltaX += Math.abs(dx);
      deltaY += Math.abs(dy);
    }
    
    const avgSpeed = totalTime > 0 ? totalDistance / totalTime : 0;
    const straightLine = (deltaX + deltaY) > 0 ? totalDistance / (deltaX + deltaY) > 0.9 : false;
    
    return {
      variance: this.calculateVariance(movements.map(m => ({ x: m.x, y: m.y }))),
      speed: avgSpeed,
      straightLine: straightLine // Suspicious if too many straight line movements
    };
  }
  
  analyzeClickPatterns() {
    const clicks = this.behaviorData.clickPatterns;
    if (clicks.length < 2) return { interval: 0, precision: 0 };
    
    const intervals = [];
    for (let i = 1; i < clicks.length; i++) {
      intervals.push(clicks[i].timestamp - clicks[i - 1].timestamp);
    }
    
    const avgInterval = intervals.reduce((a, b) => a + b, 0) / intervals.length;
    const intervalVariance = this.calculateVariance(intervals);
    
    return {
      interval: avgInterval,
      variance: intervalVariance,
      tooRegular: intervalVariance < 100 // Suspicious if clicks are too regular
    };
  }
  
  analyzeTypingSpeed() {
    const keypresses = this.behaviorData.typingSpeed;
    if (keypresses.length < 2) return { wpm: 0, variance: 0 };
    
    const intervals = [];
    for (let i = 1; i < keypresses.length; i++) {
      intervals.push(keypresses[i] - keypresses[i - 1]);
    }
    
    const avgInterval = intervals.reduce((a, b) => a + b, 0) / intervals.length;
    const wpm = avgInterval > 0 ? (60000 / avgInterval) / 5 : 0; // Rough WPM calculation
    
    return {
      wpm: wpm,
      variance: this.calculateVariance(intervals),
      tooFast: wpm > 200, // Suspicious if typing too fast
      tooRegular: this.calculateVariance(intervals) < 50
    };
  }
  
  analyzeScrollBehavior() {
    const scrolls = this.behaviorData.scrollBehavior;
    if (scrolls.length < 2) return { speed: 0, variance: 0 };
    
    const speeds = [];
    for (let i = 1; i < scrolls.length; i++) {
      const prev = scrolls[i - 1];
      const curr = scrolls[i];
      const distance = Math.abs(curr.scrollY - prev.scrollY);
      const time = curr.timestamp - prev.timestamp;
      speeds.push(time > 0 ? distance / time : 0);
    }
    
    const avgSpeed = speeds.reduce((a, b) => a + b, 0) / speeds.length;
    
    return {
      speed: avgSpeed,
      variance: this.calculateVariance(speeds),
      tooFast: avgSpeed > 10 // Suspicious if scrolling too fast
    };
  }
  
  calculateVariance(data) {
    if (data.length === 0) return 0;
    
    const mean = data.reduce((a, b) => {
      if (typeof b === 'object') {
        return a + Math.sqrt(b.x * b.x + b.y * b.y);
      }
      return a + b;
    }, 0) / data.length;
    
    const variance = data.reduce((a, b) => {
      const value = typeof b === 'object' ? Math.sqrt(b.x * b.x + b.y * b.y) : b;
      return a + Math.pow(value - mean, 2);
    }, 0) / data.length;
    
    return variance;
  }
  
  calculateAnomalyScore(mouseMetrics, clickMetrics, typingMetrics, scrollMetrics, sessionDuration, focusLossCount) {
    let score = 0;
    
    // Check for bot-like behavior
    if (mouseMetrics.straightLine) score += 0.3;
    if (clickMetrics.tooRegular) score += 0.2;
    if (typingMetrics.tooFast) score += 0.2;
    if (typingMetrics.tooRegular) score += 0.1;
    if (scrollMetrics.tooFast) score += 0.1;
    if (focusLossCount > 5) score += 0.1;
    
    // Session duration checks
    if (sessionDuration < 10000) score += 0.2; // Too fast completion
    
    return Math.min(score, 1.0);
  }
  
  flagSuspiciousBehavior(reason) {
    console.warn(`Suspicious behavior detected: ${reason}`);
    // Could send to analytics or trigger additional security measures
  }
  
  // Get behavior summary for server validation
  getBehaviorSummary() {
    const metrics = this.calculateMetrics();
    return {
      sessionDuration: metrics.sessionDuration,
      mouseMovementCount: Array.isArray(this.behaviorData.mouseMovements) ? this.behaviorData.mouseMovements.length : 0,
      clickCount: Array.isArray(this.behaviorData.clickPatterns) ? this.behaviorData.clickPatterns.length : 0,
      typingSpeedWPM: Math.round(metrics.typingMetrics.wpm),
      focusLossCount: this.behaviorData.focusLost,
      anomalyScore: metrics.anomalyScore,
      suspiciousFlags: {
        straightLineMovement: metrics.mouseMetrics.straightLine,
        regularClicks: metrics.clickMetrics.tooRegular,
        fastTyping: metrics.typingMetrics.tooFast,
        fastScrolling: metrics.scrollMetrics.tooFast
      }
    };
  }
}

// Export singleton instance
export const behaviorTracker = new BehaviorTracker();

// Export function to get behavior data
export const getBehaviorData = () => {
  return behaviorTracker.getBehaviorSummary();
};

// Export function to check if behavior is suspicious
export const isSuspiciousBehavior = () => {
  const summary = behaviorTracker.getBehaviorSummary();
  return summary.anomalyScore > 0.7;
};