import CryptoJS from 'crypto-js';

// Canvas fingerprinting
const getCanvasFingerprint = () => {
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  ctx.textBaseline = 'top';
  ctx.font = '14px Arial';
  ctx.fillText('Device fingerprint canvas test 🔒', 2, 2);
  ctx.fillStyle = 'rgba(255,0,255,0.5)';
  ctx.fillRect(0, 0, 100, 100);
  return canvas.toDataURL();
};

// WebGL fingerprinting
const getWebGLFingerprint = () => {
  const canvas = document.createElement('canvas');
  const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
  if (!gl) return 'no-webgl';
  
  const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
  return {
    vendor: gl.getParameter(gl.VENDOR),
    renderer: gl.getParameter(gl.RENDERER),
    version: gl.getParameter(gl.VERSION),
    shadingLanguageVersion: gl.getParameter(gl.SHADING_LANGUAGE_VERSION),
    unmaskedVendor: debugInfo ? gl.getParameter(debugInfo.UNMASKED_VENDOR_WEBGL) : 'unknown',
    unmaskedRenderer: debugInfo ? gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL) : 'unknown'
  };
};

// Audio fingerprinting
const getAudioFingerprint = () => {
  try {
    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
    const oscillator = audioContext.createOscillator();
    const analyser = audioContext.createAnalyser();
    const gainNode = audioContext.createGain();
    
    oscillator.type = 'triangle';
    oscillator.frequency.value = 10000;
    gainNode.gain.value = 0;
    
    oscillator.connect(analyser);
    analyser.connect(gainNode);
    gainNode.connect(audioContext.destination);
    
    oscillator.start(0);
    
    const frequencyData = new Uint8Array(analyser.frequencyBinCount);
    analyser.getByteFrequencyData(frequencyData);
    
    oscillator.stop();
    audioContext.close();
    
    return Array.from(frequencyData).join(',');
  } catch (e) {
    return 'audio-error';
  }
};

// VM/Sandbox detection
const detectVirtualEnvironment = () => {
  const vmIndicators = {
    // CPU detection
    lowCoreCount: navigator.hardwareConcurrency <= 2,
    lowMemory: navigator.deviceMemory <= 4,
    
    // VM-specific user agents
    vmUserAgent: /VMware|VirtualBox|QEMU|Xen|Hyper-V|Docker|Virtual/i.test(navigator.userAgent),
    
    // Resolution indicators
    commonVMResolutions: ['1024x768', '1280x720', '800x600', '1280x800'].includes(`${screen.width}x${screen.height}`),
    
    // Browser automation detection
    automationFlags: navigator.webdriver || window.phantom || window._phantom || window.callPhantom || window.chrome?.runtime?.onConnect,
    
    // Headless detection
    headlessChrome: /HeadlessChrome/i.test(navigator.userAgent),
    
    // WebGL vendor detection
    suspiciousWebGL: (() => {
      try {
        const canvas = document.createElement('canvas');
        const gl = canvas.getContext('webgl');
        if (!gl) return true;
        const vendor = gl.getParameter(gl.VENDOR);
        return /VMware|VirtualBox|Microsoft Basic|SwiftShader|llvmpipe/i.test(vendor);
      } catch {
        return true;
      }
    })(),
    
    // Language detection (VMs often have default English)
    defaultLanguage: navigator.language === 'en-US' && navigator.languages.length === 1,
    
    // Timezone detection
    suspiciousTimezone: Intl.DateTimeFormat().resolvedOptions().timeZone === 'UTC'
  };
  
  const vmScore = Object.values(vmIndicators).filter(Boolean).length;
  return { isVM: vmScore >= 3, score: vmScore, indicators: vmIndicators };
};

export const getUnbreakableFingerprint = async () => {
  const components = {};
  // 1. Hardware-level fingerprinting
  components.gpuRenderer = await getGPUFingerprint();
  components.cpuFingerprint = getCPUFingerprint();
  components.memoryFingerprint = getMemoryFingerprint();
  components.batteryFingerprint = await getBatteryFingerprint();
  // 2. Browser engine fingerprinting
  components.engineQuirks = getBrowserEngineQuirks();
  components.renderingFingerprint = await getAdvancedRenderingFingerprint();
  components.performanceFingerprint = getPerformanceFingerprint();
  // 3. System-level fingerprinting
  components.filesystemFingerprint = await getFilesystemFingerprint();
  components.networkFingerprint = getNetworkFingerprint();
  components.securityFingerprint = getSecurityFingerprint();
  // 4. Behavioral fingerprinting
  components.timingFingerprint = getTimingFingerprint();
  components.inputDeviceFingerprint = getInputDeviceFingerprint();
  // 5. Generate multiple hash layers
  const fingerprint = await generateMultiLayerHash(components);
  return {
    fingerprint,
    components,
    confidence: calculateFingerprintConfidence(components)
  };
};

const getGPUFingerprint = async () => {
  const canvas = document.createElement('canvas');
  const gl = canvas.getContext('webgl2') || canvas.getContext('webgl');
  if (!gl) return 'no-webgl';
  const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
  const vendor = gl.getParameter(gl.VENDOR);
  const renderer = gl.getParameter(gl.RENDERER);
  const version = gl.getParameter(gl.VERSION);
  const shadingLangVersion = gl.getParameter(gl.SHADING_LANGUAGE_VERSION);
  const unmaskedVendor = debugInfo ? gl.getParameter(debugInfo.UNMASKED_VENDOR_WEBGL) : vendor;
  const unmaskedRenderer = debugInfo ? gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL) : renderer;
  const maxTextureSize = gl.getParameter(gl.MAX_TEXTURE_SIZE);
  const maxVertexAttribs = gl.getParameter(gl.MAX_VERTEX_ATTRIBS);
  const maxRenderBufferSize = gl.getParameter(gl.MAX_RENDERBUFFER_SIZE);
  const extensions = gl.getSupportedExtensions();
  const parameters = getWebGLParameters(gl);
  return {
    vendor, renderer, version, shadingLangVersion,
    unmaskedVendor, unmaskedRenderer,
    maxTextureSize, maxVertexAttribs, maxRenderBufferSize,
    extensions: extensions ? extensions.sort() : [],
    parameters
  };
};

const getCPUFingerprint = () => {
  const start = performance.now();
  let result = 0;
  for (let i = 0; i < 100000; i++) {
    result += Math.sqrt(i) * Math.sin(i);
  }
  const calculationTime = performance.now() - start;
  return {
    hardwareConcurrency: navigator.hardwareConcurrency,
    calculationTime: Math.round(calculationTime * 1000) / 1000,
    result: result.toString().slice(0, 10)
  };
};

const getMemoryFingerprint = () => {
  return {
    deviceMemory: navigator.deviceMemory || 'unknown',
    jsHeapSizeLimit: window.performance && window.performance.memory ? window.performance.memory.jsHeapSizeLimit : 'unknown',
  };
};

const getBatteryFingerprint = async () => {
  try {
    if ('getBattery' in navigator) {
      const battery = await navigator.getBattery();
      return {
        charging: battery.charging,
        level: Math.round(battery.level * 100),
        chargingTime: battery.chargingTime,
        dischargingTime: battery.dischargingTime
      };
    }
  } catch (e) {}
  return 'no-battery-api';
};

const getBrowserEngineQuirks = () => {
  return {
      userAgent: navigator.userAgent,
      platform: navigator.platform,
    product: navigator.product,
    vendor: navigator.vendor,
    languages: navigator.languages,
      cookieEnabled: navigator.cookieEnabled,
    doNotTrack: navigator.doNotTrack
  };
};

const getAdvancedRenderingFingerprint = async () => {
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  canvas.width = 280;
  canvas.height = 60;
  const fonts = ['Arial', 'Times New Roman', 'Courier New', 'Helvetica', 'Georgia'];
  const texts = ['Security Test 🔒', 'Fingerprint ABC123', '日本語テスト'];
  let fingerprint = '';
  fonts.forEach(font => {
    texts.forEach(text => {
      ctx.font = `14px ${font}`;
      ctx.fillText(text, 10, 20);
      fingerprint += ctx.getImageData(0, 0, canvas.width, canvas.height).data.slice(0, 100).join('');
      ctx.clearRect(0, 0, canvas.width, canvas.height);
    });
  });
  return CryptoJS.SHA256(fingerprint).toString();
};

const getFilesystemFingerprint = async () => {
  try {
    if ('storage' in navigator && 'estimate' in navigator.storage) {
      const estimate = await navigator.storage.estimate();
      return {
        quota: estimate.quota,
        usage: estimate.usage,
        quotaRatio: estimate.usage / estimate.quota
      };
    }
  } catch (e) {}
  return 'no-storage-api';
};

const getWebGLParameters = (gl) => {
  const params = {};
  [
    'ALIASED_LINE_WIDTH_RANGE',
    'ALIASED_POINT_SIZE_RANGE',
    'MAX_COMBINED_TEXTURE_IMAGE_UNITS',
    'MAX_CUBE_MAP_TEXTURE_SIZE',
    'MAX_FRAGMENT_UNIFORM_VECTORS',
    'MAX_RENDERBUFFER_SIZE',
    'MAX_TEXTURE_IMAGE_UNITS',
    'MAX_TEXTURE_SIZE',
    'MAX_VARYING_VECTORS',
    'MAX_VERTEX_ATTRIBS',
    'MAX_VERTEX_TEXTURE_IMAGE_UNITS',
    'MAX_VERTEX_UNIFORM_VECTORS',
    'MAX_VIEWPORT_DIMS',
    'RED_BITS',
    'GREEN_BITS',
    'BLUE_BITS',
    'ALPHA_BITS',
    'DEPTH_BITS',
    'STENCIL_BITS'
  ].forEach(param => {
    try {
      params[param] = gl.getParameter(gl[param]);
    } catch (e) {}
  });
  return params;
};

const getPerformanceFingerprint = () => {
  return {
    navigation: window.performance && window.performance.navigation ? window.performance.navigation : {},
    timing: window.performance && window.performance.timing ? window.performance.timing : {},
  };
};

const getNetworkFingerprint = () => {
  return {
    connection: navigator.connection ? {
      downlink: navigator.connection.downlink,
      effectiveType: navigator.connection.effectiveType,
      rtt: navigator.connection.rtt,
      saveData: navigator.connection.saveData
    } : 'no-connection-api',
  };
};

const getSecurityFingerprint = () => {
  return {
    isSecureContext: window.isSecureContext,
    cookieStore: 'cookieStore' in window,
    serviceWorker: 'serviceWorker' in navigator,
    deviceMemory: navigator.deviceMemory || 'unknown',
  };
};

const getTimingFingerprint = () => {
    return {
    now: performance.now(),
    timeOrigin: performance.timeOrigin || 0,
    date: Date.now(),
  };
};

const getInputDeviceFingerprint = () => {
    return {
    maxTouchPoints: navigator.maxTouchPoints,
    pointerEnabled: window.PointerEvent !== undefined,
    touchEvent: 'ontouchstart' in window,
    keyboard: 'onkeydown' in window,
  };
};

const generateMultiLayerHash = async (components) => {
  const layer1 = CryptoJS.SHA256(JSON.stringify(components)).toString();
  const layer2 = CryptoJS.SHA256(layer1 + JSON.stringify(components.cpuFingerprint)).toString();
  const layer3 = CryptoJS.SHA256(layer2 + JSON.stringify(components.gpuRenderer)).toString();
  return layer3;
};

const calculateFingerprintConfidence = (components) => {
  let score = 1.0;
  if (!components.gpuRenderer || components.gpuRenderer === 'no-webgl') score -= 0.2;
  if (!components.cpuFingerprint) score -= 0.1;
  if (!components.memoryFingerprint) score -= 0.1;
  if (!components.batteryFingerprint || components.batteryFingerprint === 'no-battery-api') score -= 0.05;
  if (!components.renderingFingerprint) score -= 0.1;
  if (!components.filesystemFingerprint || components.filesystemFingerprint === 'no-storage-api') score -= 0.05;
  if (!components.networkFingerprint) score -= 0.05;
  if (!components.securityFingerprint) score -= 0.05;
  if (!components.timingFingerprint) score -= 0.05;
  if (!components.inputDeviceFingerprint) score -= 0.05;
  return Math.max(score, 0);
}; 