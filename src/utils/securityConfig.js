/**
 * Enhanced Security Configuration System
 * Provides centralized security settings and admin-aware protection
 */

export class SecurityConfig {
  constructor() {
    this.config = {
      // Security levels
      securityLevel: 'MAXIMUM', // LOW, MEDIUM, HIGH, MAXIMUM
      
      // Admin protection settings
      adminBypass: true, // Allow admin users to bypass security checks
      ownerBypass: true, // Allow owner to bypass all security
      
      // Detection thresholds
      botDetectionThreshold: 0.7, // Confidence threshold for bot detection
      anomalyThreshold: 0.8, // Anomaly score threshold
      suspiciousThreshold: 0.3, // Threshold for suspicious behavior warnings
      
      // Behavioral analysis settings
      enableRealTimeMonitoring: true,
      enableBehaviorLogging: true,
      enableSecurityAlerts: true,
      
      // Ban system settings
      enableTemporaryBans: true,
      consoleAccessBanDuration: 60000, // 1 minute
      codeInjectionBanDuration: 10800000, // 3 hours
      
      // Advanced protection features
      enableDevToolsDetection: true,
      enableConsoleProtection: true,
      enableScriptInjectionProtection: true,
      enableHoneypotTraps: true,
      enablePerformanceMonitoring: true,
      
      // Logging and monitoring
      logLevel: 'DETAILED', // MINIMAL, NORMAL, DETAILED, VERBOSE
      enableSecurityDashboard: true,
      enableViolationReporting: true
    };
    
    this.adminRoles = ['admin', 'owner', 'moderator'];
    this.ownerRoles = ['owner'];
  }

  // Get current security configuration
  getConfig() {
    return { ...this.config };
  }

  // Update security configuration
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
    console.log('🔧 Security configuration updated:', newConfig);
  }

  // Check if user has admin privileges
  isAdminUser() {
    try {
      // Check if we're in a browser environment
      if (typeof window === 'undefined' || typeof localStorage === 'undefined') {
        return false;
      }

      // Check localStorage for admin token
      const adminToken = localStorage.getItem('adminToken');
      if (adminToken) {
        try {
          const tokenData = JSON.parse(atob(adminToken.split('.')[1]));
          return this.adminRoles.includes(tokenData.role);
        } catch (e) {
          return false;
        }
      }

      // Check session storage
      const userRole = sessionStorage.getItem('userRole');
      return this.adminRoles.includes(userRole);
    } catch (error) {
      return false;
    }
  }

  // Check if user is owner
  isOwnerUser() {
    try {
      // Check if we're in a browser environment
      if (typeof window === 'undefined' || typeof localStorage === 'undefined') {
        return false;
      }

      const adminToken = localStorage.getItem('adminToken');
      if (adminToken) {
        try {
          const tokenData = JSON.parse(atob(adminToken.split('.')[1]));
          return this.ownerRoles.includes(tokenData.role);
        } catch (e) {
          return false;
        }
      }

      const userRole = sessionStorage.getItem('userRole');
      return this.ownerRoles.includes(userRole);
    } catch (error) {
      return false;
    }
  }

  // Determine if security check should be bypassed
  shouldBypassSecurity(checkType = 'general') {
    try {
      // Ensure config is available
      if (!this.config) {
        return { bypass: false, reason: 'config-unavailable', userType: 'user' };
      }

      const isOwner = this.isOwnerUser();
      const isAdmin = this.isAdminUser();

      // Owner bypass (if enabled)
      if (isOwner && this.config.ownerBypass) {
        console.log('👑 Security bypass: Owner privileges detected');
        return { bypass: true, reason: 'owner-privileges', userType: 'owner' };
      }

      // Admin bypass (if enabled)
      if (isAdmin && this.config.adminBypass) {
        console.log('🛡️ Security bypass: Admin privileges detected');
        return { bypass: true, reason: 'admin-privileges', userType: 'admin' };
      }

      return { bypass: false, reason: 'no-privileges', userType: 'user' };
    } catch (error) {
      console.warn('Error in shouldBypassSecurity:', error);
      return { bypass: false, reason: 'error', userType: 'user' };
    }
  }

  // Get security level configuration
  getSecurityLevelConfig(level = null) {
    const levels = {
      LOW: {
        botDetectionThreshold: 0.9,
        anomalyThreshold: 0.95,
        enableRealTimeMonitoring: false,
        enableDevToolsDetection: false,
        logLevel: 'MINIMAL'
      },
      MEDIUM: {
        botDetectionThreshold: 0.8,
        anomalyThreshold: 0.85,
        enableRealTimeMonitoring: true,
        enableDevToolsDetection: true,
        logLevel: 'NORMAL'
      },
      HIGH: {
        botDetectionThreshold: 0.7,
        anomalyThreshold: 0.8,
        enableRealTimeMonitoring: true,
        enableDevToolsDetection: true,
        logLevel: 'DETAILED'
      },
      MAXIMUM: {
        botDetectionThreshold: 0.6,
        anomalyThreshold: 0.7,
        enableRealTimeMonitoring: true,
        enableDevToolsDetection: true,
        enableConsoleProtection: true,
        enableScriptInjectionProtection: true,
        enableHoneypotTraps: true,
        enablePerformanceMonitoring: true,
        logLevel: 'VERBOSE'
      }
    };

    const targetLevel = level || this.config.securityLevel;
    return levels[targetLevel] || levels.HIGH;
  }

  // Apply security level settings
  applySecurityLevel(level) {
    const levelConfig = this.getSecurityLevelConfig(level);
    this.updateConfig({ ...levelConfig, securityLevel: level });
    console.log(`🔒 Security level set to: ${level}`);
  }

  // Log security event
  logSecurityEvent(event, data = {}) {
    try {
      // Ensure config is available
      if (!this.config) {
        console.warn('Security config not available for logging event:', event);
        return;
      }

      const logLevels = ['MINIMAL', 'NORMAL', 'DETAILED', 'VERBOSE'];
      const currentLevelIndex = logLevels.indexOf(this.config.logLevel);
      const eventLevelIndex = logLevels.indexOf(data.level || 'NORMAL');

      if (eventLevelIndex <= currentLevelIndex) {
        const timestamp = new Date().toISOString();
        const logData = {
          timestamp,
          event,
          securityLevel: this.config.securityLevel,
          userType: this.isOwnerUser() ? 'owner' : this.isAdminUser() ? 'admin' : 'user',
          ...data
        };

        console.log(`🔍 SECURITY LOG [${event}]:`, logData);

        // Store in session for security dashboard
        if (this.config.enableSecurityDashboard) {
          this.storeSecurityLog(logData);
        }
      }
    } catch (error) {
      console.warn('Error logging security event:', error);
    }
  }

  // Store security log for dashboard
  storeSecurityLog(logData) {
    try {
      // Check if we're in a browser environment
      if (typeof window === 'undefined' || typeof sessionStorage === 'undefined') {
        return;
      }

      const logs = JSON.parse(sessionStorage.getItem('securityLogs') || '[]');
      logs.push(logData);

      // Keep only last 100 logs
      if (logs.length > 100) {
        logs.splice(0, logs.length - 100);
      }

      sessionStorage.setItem('securityLogs', JSON.stringify(logs));
    } catch (error) {
      console.warn('Failed to store security log:', error);
    }
  }

  // Get security logs for dashboard
  getSecurityLogs() {
    try {
      // Check if we're in a browser environment
      if (typeof window === 'undefined' || typeof sessionStorage === 'undefined') {
        return [];
      }

      return JSON.parse(sessionStorage.getItem('securityLogs') || '[]');
    } catch (error) {
      return [];
    }
  }

  // Clear security logs
  clearSecurityLogs() {
    try {
      // Check if we're in a browser environment
      if (typeof window === 'undefined' || typeof sessionStorage === 'undefined') {
        return;
      }

      sessionStorage.removeItem('securityLogs');
      console.log('🗑️ Security logs cleared');
    } catch (error) {
      console.warn('Failed to clear security logs:', error);
    }
  }

  // Initialize security system
  initialize() {
    console.log('🚀 Enhanced Security System Initializing...');
    console.log('🔒 Security Level:', this.config.securityLevel);
    console.log('👑 Admin Bypass:', this.config.adminBypass ? 'ENABLED' : 'DISABLED');
    console.log('🛡️ Owner Bypass:', this.config.ownerBypass ? 'ENABLED' : 'DISABLED');
    console.log('📊 Real-time Monitoring:', this.config.enableRealTimeMonitoring ? 'ACTIVE' : 'INACTIVE');
    console.log('📝 Logging Level:', this.config.logLevel);
    
    const userStatus = this.shouldBypassSecurity();
    if (userStatus.bypass) {
      console.log(`👑 Welcome ${userStatus.userType.toUpperCase()} - Enhanced privileges active`);
    } else {
      console.log('🔒 Full security protection active for standard user');
    }
    
    this.logSecurityEvent('SYSTEM_INITIALIZED', {
      level: 'NORMAL',
      securityLevel: this.config.securityLevel,
      userPrivileges: userStatus
    });
  }
}

// Create singleton instance with lazy initialization
let _securityConfigInstance = null;
let _initializationPromise = null;

// Export function to get singleton instance with lazy initialization
export function getSecurityConfig() {
  if (!_securityConfigInstance) {
    _securityConfigInstance = new SecurityConfig();
  }
  return _securityConfigInstance;
}

// Safe initialization function that can be called multiple times
export function initializeSecurityConfig() {
  if (_initializationPromise) {
    return _initializationPromise;
  }

  _initializationPromise = new Promise((resolve) => {
    // Ensure we have an instance
    const instance = getSecurityConfig();

    // Initialize only in browser environment
    if (typeof window !== 'undefined') {
      // Use setTimeout to ensure all modules are loaded before initialization
      setTimeout(() => {
        try {
          instance.initialize();
          resolve(instance);
        } catch (error) {
          console.warn('Security config initialization failed:', error);
          resolve(instance);
        }
      }, 0);
    } else {
      // In server environment, just resolve immediately
      resolve(instance);
    }
  });

  return _initializationPromise;
}

// Create a proxy object that safely handles method calls before initialization
const createSecurityConfigProxy = () => {
  const instance = getSecurityConfig();

  return new Proxy(instance, {
    get(target, prop) {
      // For method calls, ensure safe execution
      if (typeof target[prop] === 'function') {
        return function(...args) {
          try {
            return target[prop].apply(target, args);
          } catch (error) {
            console.warn(`Security config method ${prop} failed:`, error);
            // Return safe defaults for common methods
            if (prop === 'shouldBypassSecurity') {
              return { bypass: false, reason: 'error', userType: 'user' };
            }
            if (prop === 'getConfig') {
              return {};
            }
            if (prop === 'getSecurityLogs') {
              return [];
            }
            return undefined;
          }
        };
      }

      // For property access, return the property value
      return target[prop];
    }
  });
};

// Export singleton instance with safe proxy
export const securityConfig = createSecurityConfigProxy();
