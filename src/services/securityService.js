// Placeholder for securityService to resolve import errors during design
const securityService = {
  apiKeys: {
    list: async () => {
      // Return a static list of mock API keys for design purposes
      return [
        { id: 'key1', name: 'Development Key', key: 'mock_key_dev_12345abcdef', permissions: ['read', 'write'], isActive: true, createdAt: '2023-01-01T12:00:00Z' },
        { id: 'key2', name: 'Testing Key', key: 'mock_key_test_67890ghijkl', permissions: ['read'], isActive: true, createdAt: '2023-02-15T12:00:00Z' },
        { id: 'key3', name: 'Expired Key', key: 'mock_key_exp_mno345pqrst', permissions: ['read', 'delete'], isActive: false, createdAt: '2022-10-20T12:00:00Z' },
      ];
    },
    generate: async (name, permissions) => {
      // Simulate API key generation
      const newKey = {
        id: `mock_key_${Date.now()}`,
        name,
        key: `mock_key_generated_${Math.random().toString(36).substring(2, 15)}`,
        permissions,
        isActive: true,
        createdAt: new Date().toISOString(),
      };
      // In a real app, this would add to a persistent state or database
      return newKey;
    },
    revoke: async (keyId) => {
      // Simulate API key revocation
      // In a real app, this would update a persistent state or database
      return { success: true, id: keyId };
    },
  },
  // Add other security service mocks as needed
};
export default securityService; 