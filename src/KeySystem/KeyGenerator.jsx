import React, { useState, useEffect, useCallback, useRef } from 'react';
import { motion } from 'framer-motion';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>lock, FiCheckCircle, FiAlertCircle, FiExternalLink, FiCopy } from 'react-icons/fi';
import { Button } from '../components/ui/Button';
import { Card } from '../components/ui/Card';
import { cn } from '../lib/utils';
import { getUnbreakableFingerprint } from '../utils/deviceFingerprint';
import { getBehaviorData, isSuspiciousBehavior } from '../utils/behaviorTracker';
import { mlAnalyzer } from '../utils/mlBehaviorAnalysis';
import { scriptProtector } from '../utils/scriptIntegrity';
import { securityConfig, initializeSecurityConfig } from '../utils/securityConfig';
import { useLocation, useNavigate } from 'react-router-dom';
import { GET_KEY_URL, GENERATE_KEY_URL } from '../config/keys';

// TODO: Move these URLs to a config file or .env for future flexibility
// const GET_KEY_URL = process.env.REACT_APP_GET_KEY_URL || '/get-key?step=1';
// const GENERATE_KEY_URL = process.env.REACT_APP_GENERATE_KEY_URL || '/get-key?step=2';

// TODO: Ensure tailwind.config.js darkMode is set to 'media' or 'class' for dark mode support
// TODO: Collect behavior data as early as possible (root/layout hook)
// TODO: Ensure backend checks for token replay/reuse (nonce/HMAC)

// Centralized error messages
const ERROR_MESSAGES = {
  vm: 'Virtual machines and automated environments are not supported. Please use a real device.',
  suspicious: 'Your environment appears to be suspicious. Please try from a regular device.',
  behavior: 'Suspicious activity detected. Please try again with normal user behavior.',
  tooFast: 'Please take your time to complete the process naturally.',
  campaign: 'Campaign data not loaded. Please try again in a moment.',
  fetchCampaigns: 'Failed to load campaigns',
  copy: 'Failed to copy key. Please copy manually.'
};

const KeyGenerator = () => {
  const location = useLocation();
  const navigate = useNavigate();

  // Move debugInfo state above sessionId state and setSessionId callback
  const [debugInfo, setDebugInfo] = useState({
    sessionId: '',
    lastToken: '',
    stepResponse: null,
    keyResponse: null,
    deviceData: null,
    behaviorData: null,
    mlAnalysis: null,
    securityStatus: null,
  });

  // Using a ref for sessionId to manage its value without causing direct effect re-runs
  const sessionIdRef = useRef(null);
  // A state variable to trigger re-renders when sessionId changes for UI and dependent effects
  const [sessionId, _setSessionId] = useState(null);

  // Wrapper function to update both ref and state, and debugInfo
  const setSessionId = useCallback((newId) => {
    sessionIdRef.current = newId;
    _setSessionId(newId);
    setDebugInfo((prev) => ({ ...prev, sessionId: newId || '' }));
  }, [setDebugInfo]);

  const [currentStep, setCurrentStep] = useState(1);
  const [campaigns, setCampaigns] = useState([]);
  const [selectedCampaign, setSelectedCampaign] = useState(null);
  const [step1Completed, setStep1Completed] = useState(false);
  const [step2Completed, setStep2Completed] = useState(false);
  const [generatedKey, setGeneratedKey] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [copied, setCopied] = useState(false);

  // Ref to prevent re-fetching session if already attempted within the component's lifecycle
  const [isSessionFetching, setIsSessionFetching] = useState(false);
  const [hasCheckedSession, setHasCheckedSession] = useState(false);

  // Ref to prevent duplicate key generation on refresh
  const keyGeneratedRef = useRef(false);

  // Debug info toggle
  const [showDebug, setShowDebug] = useState(false);

  // Ref to prevent duplicate API calls for the same step param and token
  const isProcessingStepParam = useRef(false);
  const currentProcessedUrlIdentifier = useRef(null); // New ref

  // New: State for IP and existing key check
  const [userIp, setUserIp] = useState(null);
  const [existingKey, setExistingKey] = useState(null);
  const [checkingIp, setCheckingIp] = useState(true);

  // Memoized functions to prevent re-creation on every render
  const fetchCampaigns = useCallback(async () => {
    try {
      const response = await fetch('/.netlify/functions/get-campaigns');
      if (!response.ok) {
        throw new Error('Failed to fetch campaigns');
      }
      const data = await response.json();
      
      if (data && data.length > 0) {
        setCampaigns(data);
        setSelectedCampaign(data[0]);
        setError(null); // Clear any previous error
      } else {
        setError('No active campaigns found.');
        setCampaigns([]);
        setSelectedCampaign(null);
      }
    } catch (err) {
      setError(ERROR_MESSAGES.fetchCampaigns); // Use the existing error message constant
      setCampaigns([]);
      setSelectedCampaign(null);
    }
  }, [setCampaigns, setSelectedCampaign, setError]);

  const markStepComplete = useCallback(async (step, token) => {
    // Use sessionIdRef.current here instead of sessionId state directly
    if (!sessionIdRef.current) {
      return;
    }
    if (!token) {
      setError('Missing Linkvertise verification token. Please complete the ad step.');
      return;
    }
    setDebugInfo((prev) => ({ ...prev, lastToken: token }));
    try {
      const res = await fetch('/.netlify/functions/step-session', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'complete', step, sessionId: sessionIdRef.current, token })
      });
      const data = await res.json();
      setDebugInfo((prev) => ({ ...prev, stepResponse: data }));
      if (!res.ok) {
        setError(data.error || 'Failed to verify Linkvertise step.');
        throw new Error(data.error || 'Failed to verify Linkvertise step.'); // Throw to trigger catch block
      }
    } catch (err) {
      setError('Failed to verify Linkvertise step due to network error.');
      throw err; // Re-throw to allow .catch() in useEffect
    }
  }, [setError, setDebugInfo]);

  // Split useEffect: Session Management - Refactored for robust session ID initialization
  useEffect(() => {
    if (hasCheckedSession || sessionId) {
      // If we've already checked, or session is already established, do nothing
      return;
    }

    const params = new URLSearchParams(location.search);
    const sessionFromUrl = params.get('session');
    const storedSession = sessionStorage.getItem('sessionId');

    if (sessionFromUrl) {
      setSessionId(sessionFromUrl);
      sessionStorage.setItem('sessionId', sessionFromUrl);
      setHasCheckedSession(true);
      return;
    }

    if (storedSession) {
      setSessionId(storedSession);
      // Ensure URL also reflects stored session if not already
      if (!params.get('session')) {
        params.set('session', storedSession);
        navigate(`${location.pathname}?${params.toString()}`, { replace: true });
      }
      setHasCheckedSession(true);
      return;
    }

    // If no session found in URL or sessionStorage, and not currently fetching, initiate a new session
    if (!isSessionFetching) {
      setIsSessionFetching(true);
      fetch('/.netlify/functions/step-session', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'start' })
      })
        .then(res => res.json())
        .then(data => {
          if (data.sessionId) {
            setSessionId(data.sessionId);
            sessionStorage.setItem('sessionId', data.sessionId);
            params.set('session', data.sessionId);
            navigate(`${location.pathname}?${params.toString()}`, { replace: true });
          } else {
            setError('Failed to start session: No session ID returned from backend.');
          }
          setIsSessionFetching(false);
          setHasCheckedSession(true); // Mark as checked even if fetch failed
        })
        .catch(err => {
          setError('Failed to start session. Please try again.');
          setIsSessionFetching(false);
          setHasCheckedSession(true); // Mark as checked even if fetch failed
        });
    }
  }, [location.search, location.pathname, navigate, setSessionId, setError, sessionId, isSessionFetching, hasCheckedSession]);

  // New useEffect for fetching campaigns based on sessionId
  useEffect(() => {
    if (sessionId) {
      fetchCampaigns();

      // Initialize enhanced security system safely
      initializeSecurityConfig().then(() => {
        securityConfig.logSecurityEvent('KEY_SYSTEM_ACCESS', {
          level: 'NORMAL',
          sessionId: sessionId,
          userAgent: navigator.userAgent
        });

        // Update debug info with security data
        setDebugInfo((prev) => ({
          ...prev,
          securityConfig: securityConfig.getConfig(),
          userPrivileges: securityConfig.shouldBypassSecurity('key-generation')
        }));
      });

      // Load ML model for behavioral analysis
      mlAnalyzer.loadModel().catch(err => {
        console.warn('ML model loading failed, using fallback detection:', err);
      });

      // Initialize script integrity protection
      scriptProtector.initialize();
      setDebugInfo((prev) => ({
        ...prev,
        securityStatus: scriptProtector.getSecurityStatus()
      }));
    }
  }, [sessionId, fetchCampaigns]);

  // New useEffect to fetch session status when sessionId changes
  const fetchSessionStatus = useCallback(async () => {
    if (!sessionIdRef.current) {
      return;
    }
    try {
      const res = await fetch('/.netlify/functions/step-session', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'status', sessionId: sessionIdRef.current })
      });
      const data = await res.json();
      if (res.ok && data.status) {
        setStep1Completed(data.status.step1Completed || false);
        setStep2Completed(data.status.step2Completed || false);
        setError(null); // Clear any previous errors on successful status fetch
      } else {
        // Optionally, handle error state for UI
      }
    } catch (err) {
      // Optionally, handle network error state for UI
    }
  }, [setStep1Completed, setStep2Completed, sessionIdRef, setError]);

  useEffect(() => {
    if (sessionId) {
      fetchSessionStatus();
    }
  }, [sessionId, fetchSessionStatus]);

  // Split useEffect: Step Param Handling
  useEffect(() => {
    // Only proceed if a session ID exists and is stable
    if (!sessionId) {
      return;
    }

    const params = new URLSearchParams(location.search);
    const stepParam = params.get('step');
    const token = params.get('hash'); // Use 'hash' as the token

    // Handle direct Linkvertise redirects with token
    if (stepParam && token) {
      const currentUrlIdentifier = `${stepParam}-${token}`; // Create a unique identifier

      if (isProcessingStepParam.current && currentUrlIdentifier === currentProcessedUrlIdentifier.current) {
        return;
      }
      isProcessingStepParam.current = true; // Set flag to true when starting processing
      currentProcessedUrlIdentifier.current = currentUrlIdentifier; // Store the identifier

      const stepNum = parseInt(stepParam);
      if (stepNum === 1 && !step1Completed) {
        markStepComplete(1, token)
          .then(() => {
            setStep1Completed(true);
            const newParams = new URLSearchParams(location.search);
            newParams.delete('step');
            newParams.delete('hash');
            navigate(`${location.pathname}?${newParams.toString()}`, { replace: true });
          })
          .catch(err => {
            if (err.message === 'Token already used. Please complete the step again.') {
              fetchSessionStatus(); // Re-fetch status to update UI based on backend
            } else {
              setError(err.message || 'Failed to complete step 1.');
            }
          });
      } else if (stepNum === 2 && !step2Completed) {
        markStepComplete(2, token)
          .then(() => {
            setStep2Completed(true);
            const newParams = new URLSearchParams(location.search);
            newParams.delete('step');
            newParams.delete('hash');
            navigate(`${location.pathname}?${newParams.toString()}`, { replace: true });
          })
          .catch(err => {
            if (err.message === 'Token already used. Please complete the step again.') {
              fetchSessionStatus(); // Re-fetch status to update UI based on backend
            } else {
              setError(err.message || 'Failed to complete step 2.');
            }
          });
      } else {
        // If step already completed or invalid, ensure the URL is clean
        const newParams = new URLSearchParams(location.search);
        if (newParams.has('step') || newParams.has('hash')) {
          newParams.delete('step');
          newParams.delete('hash');
          navigate(`${location.pathname}?${newParams.toString()}`, { replace: true });
        }
      }
    } else {
      // If no stepParam or token, reset the processing flags
      if (isProcessingStepParam.current || currentProcessedUrlIdentifier.current) {
        isProcessingStepParam.current = false;
        currentProcessedUrlIdentifier.current = null;
      }
    }
  }, [sessionId, location.search, fetchCampaigns, markStepComplete, step1Completed, step2Completed, navigate, sessionIdRef, currentStep, fetchSessionStatus]);

  // New useEffect to synchronize currentStep and URL based on session status
  useEffect(() => {
    if (!sessionId) {
      return;
    }

    const params = new URLSearchParams(location.search);
    const stepParam = params.get('step');
    let newCurrentStep = 1; // Default to step 1

    if (step2Completed) {
      newCurrentStep = 3;
    } else if (step1Completed) {
      newCurrentStep = 2;
    } else if (stepParam) {
      const parsedStepParam = parseInt(stepParam);
      if (!isNaN(parsedStepParam) && parsedStepParam >= 1 && parsedStepParam <= 3) {
        newCurrentStep = parsedStepParam;
      }
    }

    if (currentStep !== newCurrentStep) {
      setCurrentStep(newCurrentStep);
    }

    // Ensure URL parameters are consistent with the current step and session ID
    const newParams = new URLSearchParams();
    newParams.set('session', sessionId);
    if (newCurrentStep === 2) {
      newParams.set('step', '2');
    } else if (newCurrentStep === 3) {
      newParams.set('step', '3');
    }

    const currentUrlSearch = location.search;
    const desiredUrlSearch = newParams.toString() ? `?${newParams.toString()}` : '';

    if (currentUrlSearch !== desiredUrlSearch) {
      navigate(`${location.pathname}${desiredUrlSearch}`, { replace: true });
    }

  }, [sessionId, step1Completed, step2Completed, location.search, location.pathname, navigate, currentStep]);

  // Fetch user IP and check for existing key on mount
  useEffect(() => {
    const fetchIpAndCheckKey = async () => {
      setCheckingIp(true);
      try {
        // Get public IP
        const ipRes = await fetch('https://api.ipify.org?format=json');
        const ipData = await ipRes.json();
        setUserIp(ipData.ip);
        // Check backend for existing key for this IP
        const checkRes = await fetch(`/.netlify/functions/validate-key?action=check-ip&ip=${ipData.ip}`);
        if (checkRes.ok) {
          const data = await checkRes.json();
          if (data && data.key) {
            setExistingKey(data.key);
          }
        }
      } catch (err) {
        // Ignore IP check errors, allow normal flow
      } finally {
        setCheckingIp(false);
      }
    };
    fetchIpAndCheckKey();
  }, []);

  const handleStep1Complete = () => {
    setStep1Completed(true);
    setCurrentStep(2);
  };

  const handleStep2Complete = async () => {
    setLoading(true);
    setError(null);

    // Check for security violations before proceeding
    const securityStatus = scriptProtector.getSecurityStatus();
    if (securityStatus.debuggingDetected || securityStatus.tamperingDetected) {
      setError('Security violation detected. Please use a standard browser without debugging tools.');
      setLoading(false);
      return;
    }

    try {
      const deviceData = await getUnbreakableFingerprint();
      setDebugInfo((prev) => ({ ...prev, deviceData }));
      // Advanced security checks
      if (deviceData.vmDetection && deviceData.vmDetection.confidence > 0.75) {
        setError(`Virtual machine detected with ${Math.round(deviceData.vmDetection.confidence * 100)}% confidence`);
        setLoading(false);
        return;
      }
      if (deviceData.integrity < 0.7) {
        setError('Device integrity check failed. Please use a standard browser.');
        setLoading(false);
        return;
      }
      if (deviceData.confidence < 0.8) {
        setError('Device fingerprint confidence too low. Please enable JavaScript fully.');
        setLoading(false);
        return;
      }
      const behaviorData = getBehaviorData();
      setDebugInfo((prev) => ({ ...prev, behaviorData }));
      
      // Enhanced ML-powered behavioral analysis with admin awareness
      const userPrivileges = securityConfig.shouldBypassSecurity('key-generation');
      const mlAnalysis = await mlAnalyzer.predict(behaviorData);
      setDebugInfo((prev) => ({ ...prev, mlAnalysis, userPrivileges }));

      // Log security analysis
      securityConfig.logSecurityEvent('BEHAVIORAL_ANALYSIS', {
        level: 'DETAILED',
        confidence: mlAnalysis.confidence,
        isBot: mlAnalysis.isBot,
        method: mlAnalysis.method,
        userType: userPrivileges.userType,
        bypassed: userPrivileges.bypass
      });

      // Apply security checks based on user privileges
      if (mlAnalysis.isBot && !userPrivileges.bypass) {
        const errorMsg = `🚨 Bot behavior detected with ${Math.round(mlAnalysis.confidence * 100)}% confidence (${mlAnalysis.method})`;
        setError(errorMsg);
        securityConfig.logSecurityEvent('BOT_DETECTION_BLOCK', {
          level: 'NORMAL',
          confidence: mlAnalysis.confidence,
          method: mlAnalysis.method
        });
        setLoading(false);
        return;
      } else if (mlAnalysis.isBot && userPrivileges.bypass) {
        console.log(`👑 Bot behavior detected but bypassed for ${userPrivileges.userType}: ${Math.round(mlAnalysis.confidence * 100)}% confidence`);
        securityConfig.logSecurityEvent('BOT_DETECTION_BYPASSED', {
          level: 'NORMAL',
          confidence: mlAnalysis.confidence,
          userType: userPrivileges.userType,
          reason: userPrivileges.reason
        });
      }
      
      // Update security status before sending to backend
      const updatedSecurityStatus = scriptProtector.getSecurityStatus();
      setDebugInfo((prev) => ({ ...prev, securityStatus: updatedSecurityStatus }));
      
      // Send enhanced data to backend
      const response = await fetch('/.netlify/functions/generate-key', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          campaignId: selectedCampaign.id,
          deviceData: deviceData,        // Enhanced fingerprint
          behaviorData: behaviorData,
          mlAnalysis: mlAnalysis,        // Add ML analysis results
          securityStatus: updatedSecurityStatus, // Add security status
          sessionId,
          ipAddress: userIp // NEW: send user IP
        }),
      });
      const data = await response.json();
      if (!response.ok) {
        setError(data.error || 'Failed to generate key.');
        setLoading(false);
        return;
      }
      setGeneratedKey({ key: data.key, expires_at: data.expires_at });
      setStep2Completed(true);
      setDebugInfo((prev) => ({ ...prev, keyResponse: data }));
      setLoading(false);
    } catch (err) {
      setError(err.message);
      setLoading(false);
    }
  };

  const handleRetryChecks = () => {
    setError(null);
    setLoading(false);
    setTimeout(() => {
      keyGeneratedRef.current = false;
    }, 500);
  };

  // Clipboard copy with fallback
  const handleCopyKey = async () => {
    if (generatedKey?.key) {
      try {
        if (navigator.clipboard) {
          await navigator.clipboard.writeText(generatedKey.key);
        } else {
          // Fallback for older browsers
          const textarea = document.createElement('textarea');
          textarea.value = generatedKey.key;
          document.body.appendChild(textarea);
          textarea.select();
          document.execCommand('copy');
          document.body.removeChild(textarea);
        }
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
      } catch (err) {
        setCopied(false);
        setError('Failed to copy key. Please copy manually.');
      }
    }
  };

  const handleReset = () => {
    setCurrentStep(1);
    setStep1Completed(false);
    setStep2Completed(false);
    setGeneratedKey(null);
    setError(null);
    setCopied(false);
  };

  const formatExpiryTime = (expiresAt) => {
    const expiry = new Date(expiresAt);
    const now = new Date();
    const diff = expiry - now;
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    return `${hours}h ${minutes}m`;
  };

  // Helper to build Linkvertise URL with correct query param format
  const buildLinkvertiseUrl = (baseUrl, params = {}) => {
    const url = new URL(baseUrl);
    Object.entries(params).forEach(([key, value]) => {
      url.searchParams.set(key, value);
    });
    return url.toString();
  };

  const handleStep1Continue = () => {
    if (!selectedCampaign) return;
    const url = buildLinkvertiseUrl(selectedCampaign.step1_url, {
      session: sessionIdRef.current,
      step: 1,
    });
    window.location.href = url;
  };

  const handleStep2Continue = () => {
    if (!selectedCampaign) return;
    const url = buildLinkvertiseUrl(selectedCampaign.step2_url, {
      session: sessionIdRef.current,
      step: 2,
    });
    window.location.href = url;
  };

  // Add a restart handler for unrecoverable/session errors
  const handleRestart = () => {
    sessionStorage.removeItem('sessionId');
    setCurrentStep(1);
    setStep1Completed(false);
    setStep2Completed(false);
    setGeneratedKey(null);
    setError(null);
    setCopied(false);
    setSessionId(null);
    setIsSessionFetching(false);
    setHasCheckedSession(false);
    keyGeneratedRef.current = false;
    navigate(location.pathname, { replace: true });
  };

  const handleKeyDown = (e, handler) => {
    if (e.key === 'Enter') {
      handler();
    }
  };

  const handleContinueKeyDown = (e, handler) => {
    if (e.key === 'Enter') {
      handler();
    }
  };

  // Early return if checking IP
  if (checkingIp) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-400">Checking your eligibility...</div>
      </div>
    );
  }

  // If key exists for this IP, show it and skip generation
  if (existingKey) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[300px]">
        <Card className="max-w-lg w-full p-6 bg-gray-800 border border-gray-700 rounded-lg text-center">
          <FiKey className="mx-auto mb-2 h-8 w-8 text-blue-400" />
          <div className="text-lg font-bold text-white mb-2">Your Key for Today</div>
          <div className="text-base font-mono text-yellow-300 break-all mb-4">{existingKey}</div>
          <div className="text-sm text-gray-400 mb-2">You can only generate one key every 24 hours per IP address.</div>
          <Button onClick={() => navigator.clipboard.writeText(existingKey)} className="mt-2">Copy Key</Button>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen pt-16 bg-gradient-to-br from-gray-50 via-blue-50/30 to-purple-50/30 dark:from-gray-900 dark:via-blue-900/20 dark:to-purple-900/20 relative overflow-hidden">
      {/* Background effects */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5 dark:opacity-10"></div>
      <motion.div
        className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-r from-blue-400/20 to-purple-400/20 rounded-full blur-xl"
        animate={{
          y: [0, -20, 0],
          x: [0, 10, 0],
        }}
        transition={{
          duration: 6,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />
      <motion.div
        className="absolute bottom-20 right-10 w-24 h-24 bg-gradient-to-r from-purple-400/20 to-pink-400/20 rounded-full blur-xl"
        animate={{
          y: [0, 15, 0],
          x: [0, -15, 0],
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 1
        }}
      />

      <div className="container relative mx-auto px-4 py-12">
        <div className="max-w-3xl mx-auto">
          <motion.div
            className="text-center mb-12"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <motion.div
              className="inline-block px-6 py-3 mb-6 rounded-full bg-gradient-to-r from-blue-500/10 to-purple-500/10 backdrop-blur-sm border border-blue-500/20 text-primary text-sm font-semibold shadow-lg"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <span className="flex items-center">
                <FiKey className="mr-2 h-4 w-4" />
                License Key System
              </span>
            </motion.div>

            <motion.h1
              className="text-5xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 bg-clip-text text-transparent leading-tight"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
              Get Your License Key
            </motion.h1>
            <motion.p
              className="text-xl text-foreground/80 max-w-2xl mx-auto leading-relaxed"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
              Complete the verification steps below to receive your secure 24-hour access key
            </motion.p>
          </motion.div>

          {/* Enhanced Progress Steps */}
          <motion.div
            className="flex items-center justify-center mb-12"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.5 }}
          >
            {[1, 2, 3].map((step, index) => (
              <div key={step} className="flex items-center">
                <motion.div
                  className={cn(
                    "relative flex items-center justify-center w-14 h-14 rounded-2xl font-bold text-lg transition-all duration-500",
                    currentStep >= step
                      ? "bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg shadow-blue-500/30"
                      : "bg-white/10 backdrop-blur-sm border-2 border-gray-300/30 dark:border-gray-600/30 text-gray-500"
                  )}
                  whileHover={{ scale: 1.05 }}
                  animate={currentStep >= step ? {
                    boxShadow: ["0 0 0 0 rgba(59, 130, 246, 0.4)", "0 0 0 10px rgba(59, 130, 246, 0)", "0 0 0 0 rgba(59, 130, 246, 0)"]
                  } : {}}
                  transition={{ duration: 1.5, repeat: currentStep === step ? Infinity : 0 }}
                >
                  {currentStep > step ? (
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ duration: 0.3 }}
                    >
                      <FiCheckCircle className="w-6 h-6" />
                    </motion.div>
                  ) : (
                    step
                  )}

                  {/* Active step indicator */}
                  {currentStep === step && (
                    <motion.div
                      className="absolute inset-0 rounded-2xl bg-gradient-to-r from-blue-500 to-purple-600 opacity-20"
                      animate={{ scale: [1, 1.2, 1] }}
                      transition={{ duration: 2, repeat: Infinity }}
                    />
                  )}
                </motion.div>

                {/* Connector line */}
                {index < 2 && (
                  <div className={cn(
                    "w-16 h-1 mx-4 rounded-full transition-all duration-500",
                    currentStep > step + 1
                      ? "bg-gradient-to-r from-blue-500 to-purple-600"
                      : "bg-gray-300/30 dark:bg-gray-600/30"
                  )} />
                )}
              </div>
            ))}
          </motion.div>

          {/* Step 1: First Linkvertise */}
          {currentStep === 1 && (
            <motion.div
              initial={{ opacity: 0, y: 30, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              transition={{ duration: 0.6 }}
            >
              <Card className="relative overflow-hidden p-8 bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl shadow-2xl" role="region" aria-label="Step 1: Complete First Link">
                {/* Background gradient */}
                <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 via-purple-500/5 to-pink-500/5" />

                <div className="relative z-10 text-center mb-8">
                  <motion.div
                    className="w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg"
                    whileHover={{ scale: 1.05, rotate: 5 }}
                    animate={{
                      y: [0, -5, 0],
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                  >
                    <FiExternalLink className="w-8 h-8 text-white" aria-hidden="true" />
                  </motion.div>
                  <h2 className="text-3xl font-bold mb-4 text-foreground dark:text-white">Step 1: Complete First Link</h2>
                  <p className="text-lg text-foreground/80 max-w-md mx-auto leading-relaxed">
                    Click the button below to start Step 1. You will be redirected to a Linkvertise page. Complete the action, and you will be sent back here to continue.
                  </p>
                </div>

                <div className="relative z-10">
                  <motion.div
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Button
                      onClick={handleStep1Continue}
                      className="w-full bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 hover:from-blue-700 hover:via-purple-700 hover:to-indigo-700 text-white py-4 text-lg font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 relative overflow-hidden"
                      ariaLabel="Go to Step 1 Linkvertise"
                      aria-label="Continue to Step 1 Linkvertise"
                      tabIndex={0}
                      onKeyDown={(e) => handleContinueKeyDown(e, handleStep1Continue)}
                    >
                      <motion.div
                        className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0"
                        initial={{ x: "-100%" }}
                        whileHover={{ x: "100%" }}
                        transition={{ duration: 0.6 }}
                      />
                      <span className="relative z-10 flex items-center justify-center">
                        <FiExternalLink className="mr-3 h-5 w-5" aria-hidden="true" />
                        Continue to Step 1
                        <motion.div
                          className="ml-2"
                          animate={{ x: [0, 4, 0] }}
                          transition={{ duration: 1.5, repeat: Infinity }}
                        >
                          →
                        </motion.div>
                      </span>
                    </Button>
                  </motion.div>
                </div>
              </Card>
            </motion.div>
          )}

          {/* Step 2: Second Linkvertise */}
          {currentStep === 2 && (
            <Card className="p-6" role="region" aria-label="Step 2: Complete Second Link">
              <div className="text-center mb-6">
                <FiExternalLink className="w-12 h-12 text-purple-500 mx-auto mb-4" aria-hidden="true" />
                <h2 className="text-2xl font-bold mb-2">Step 2: Complete Second Link</h2>
                <p className="text-gray-600 dark:text-gray-400">
                  Click the button below to start Step 2. You will be redirected to a Linkvertise page. Complete the action, and you will be sent back here to generate your key.
                </p>
              </div>
              <div className="space-y-4">
                <Button
                  onClick={handleStep2Continue}
                  className="w-full bg-purple-500 hover:bg-purple-600 text-white py-3"
                  ariaLabel="Go to Step 2 Linkvertise"
                  aria-label="Continue to Step 2 Linkvertise"
                  tabIndex={0}
                  onKeyDown={(e) => handleContinueKeyDown(e, handleStep2Continue)}
                >
                  <FiExternalLink className="mr-2" aria-hidden="true" />
                  Continue to Step 2
                </Button>
              </div>
            </Card>
          )}

          {/* Step 3: Key Generated */}
          {currentStep === 3 && (
            generatedKey ? (
              <Card className="p-6" role="region" aria-label="Key Generated Successfully">
                <div className="text-center mb-6">
                  <FiKey className="w-12 h-12 text-green-500 mx-auto mb-4" aria-hidden="true" />
                  <h2 className="text-2xl font-bold mb-2 text-green-600">Key Generated Successfully!</h2>
                  <p className="text-gray-600 dark:text-gray-400">
                    Your key is valid for 24 hours from now
                  </p>
                </div>
                <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 mb-4">
                  <div className="flex items-center justify-between">
                    <code className="text-lg font-mono text-gray-900 dark:text-white" aria-label="Your generated key">
                      {generatedKey.key}
                    </code>
                    <Button
                      onClick={handleCopyKey}
                      variant="outline"
                      size="sm"
                      className="ml-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      ariaLabel="Copy key to clipboard"
                      aria-label="Copy key to clipboard"
                      tabIndex={0}
                      onKeyDown={(e) => handleKeyDown(e, handleCopyKey)}
                    >
                      <FiCopy className="w-4 h-4" aria-hidden="true" />
                      {copied ? 'Copied!' : 'Copy'}
                    </Button>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4 mb-6">
                  <div className="text-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                    <FiClock className="w-6 h-6 text-blue-500 mx-auto mb-2" aria-hidden="true" />
                    <p className="text-sm text-gray-600 dark:text-gray-400">Expires In</p>
                    <p className="font-semibold text-blue-600 dark:text-blue-400">
                      {formatExpiryTime(generatedKey.expires_at)}
                    </p>
                  </div>
                  <div className="text-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                    <FiCheckCircle className="w-6 h-6 text-green-500 mx-auto mb-2" aria-hidden="true" />
                    <p className="text-sm text-gray-600 dark:text-gray-400">Status</p>
                    <p className="font-semibold text-green-600 dark:text-green-400">Active</p>
                  </div>
                </div>
                <div className="text-center space-y-3">
                  <p className="text-sm text-gray-500">
                    Use this key in your Roblox script to unlock features
                  </p>
                </div>
              </Card>
            ) : (
              step1Completed && step2Completed && (
                <Card className="p-6 text-center" role="region" aria-label="Final Step: Generate Your Key">
                  <FiKey className="w-12 h-12 text-blue-500 mx-auto mb-4" aria-hidden="true" />
                  <h2 className="text-2xl font-bold mb-2">Final Step: Generate Your Key</h2>
                  <p className="text-gray-600 dark:text-gray-400 mb-6">
                    Click the button below to generate your 24-hour key.
                  </p>
                  <Button
                    onClick={handleStep2Complete}
                    className="w-full bg-blue-500 hover:bg-blue-600 text-white py-3 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    ariaLabel="Generate key after completing both steps"
                    aria-label="Generate key after completing both steps"
                    tabIndex={0}
                    onKeyDown={(e) => handleKeyDown(e, handleStep2Complete)}
                    disabled={loading}
                  >
                    {loading ? 'Generating Key...' : 'Generate My Key'}
                  </Button>
                  {/* Loading indicator during final fetch */}
                  {loading && (
                    <div className="text-center mt-6">
                      <span className="text-gray-500">Generating your key, please wait...</span>
                    </div>
                  )}
                  {/* Error alert box */}
                  {error && (
                    <div className="bg-red-100 text-red-700 p-4 rounded mb-4 mt-4 flex items-center justify-between" role="alert">
                      <div>
                        <FiAlertCircle className="inline mr-2" />
                        {error}
                      </div>
                      {/* Show restart button if session expired or unrecoverable error */}
                      <Button
                        onClick={handleRestart}
                        variant="outline"
                        aria-label="Restart key generation"
                        className="ml-4 focus:outline-none focus:ring-2 focus:ring-blue-500"
                        tabIndex={0}
                        onKeyDown={(e) => handleKeyDown(e, handleRestart)}
                      >
                        Restart
                      </Button>
                    </div>
                  )}
                </Card>
              )
            )
          )}

          {/* Error Display */}
          {error && (
            <Card className="p-4 mt-4 border-red-200 bg-red-50 dark:bg-red-900/20" role="alert" aria-live="assertive">
              <div className="flex items-center">
                <FiAlertCircle className="w-5 h-5 text-red-500 mr-2" aria-hidden="true" />
                <span className="text-red-700 dark:text-red-300">{error}</span>
              </div>
              {/* Retry pathway for device/behavior check errors */}
              {(error.includes('Virtual machines') || error.includes('suspicious') || error.includes('activity') || error.includes('Please take your time')) && (
                <div className="mt-3 flex flex-col items-start gap-2">
                  <Button onClick={handleRetryChecks} variant="outline" aria-label="Retry device and behavior checks"
                    tabIndex={0}
                    onKeyDown={(e) => handleKeyDown(e, handleRetryChecks)}
                  >Retry check</Button>
                  <a
                    href="mailto:<EMAIL>?subject=Key%20System%20Support%20Request"
                    className="text-blue-600 underline text-sm"
                    tabIndex={0}
                    aria-label="Contact support via email"
                  >
                    Contact Support
                  </a>
                </div>
              )}
              {/* Campaign fetch failure retry */}
              {error.includes('Failed to load campaigns') && (
                <div className="mt-3">
                  <Button onClick={fetchCampaigns} variant="outline" aria-label="Retry loading campaigns"
                    tabIndex={0}
                    onKeyDown={(e) => handleKeyDown(e, fetchCampaigns)}
                  >Retry Loading Campaigns</Button>
                </div>
              )}
            </Card>
          )}
        </div>
      </div>
    </div>
  );
};

export default KeyGenerator; 