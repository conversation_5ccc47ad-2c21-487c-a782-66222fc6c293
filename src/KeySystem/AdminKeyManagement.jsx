import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, FiTrash2, <PERSON>R<PERSON>reshCw, FiSearch, FiFilter, FiPlus, FiEye, FiX } from 'react-icons/fi';
import { Button } from '../components/ui/Button';
import { Card } from '../components/ui/Card';
import { Input } from '../components/ui/Input';
import { Badge } from '../components/ui/Badge';
import { Table, TableHeader, TableBody, TableRow, TableHead, TableCell } from '../components/ui/Table';
import { useAuth } from '../context/AuthContext';
import { cn } from '../lib/utils';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '../components/ui/Dialog';

const AdminKeyManagement = () => {
  const { admin } = useAuth();
  const [keys, setKeys] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [newKeyExpiry, setNewKeyExpiry] = useState(24);
  const [creatingKey, setCreatingKey] = useState(false);
  const [showHWIDModal, setShowHWIDModal] = useState(false);
  const [selectedHWID, setSelectedHWID] = useState(null);

  useEffect(() => {
    fetchKeys();
  }, [currentPage, statusFilter]);

  const getAdminHeaders = () => ({
    'x-admin-username': localStorage.getItem('adminUsername') || '',
    'x-admin-password': localStorage.getItem('adminPassword') || '',
  });

  const fetchKeys = async () => {
    setLoading(true);
    try {
      const params = new URLSearchParams({
        page: currentPage,
        limit: 50,
        status: statusFilter,
        ...(searchTerm && { search: searchTerm })
      });

      const response = await fetch(`/.netlify/functions/security?action=keys&${params}`, {
        headers: {
          ...getAdminHeaders(),
          'Content-Type': 'application/json',
        }
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch keys');
      }

      setKeys(data.keys || []);
      setTotalPages(data.pagination?.pages || 1);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateKey = async () => {
    setCreatingKey(true);
    try {
      const response = await fetch('/.netlify/functions/security?action=create-key', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...getAdminHeaders(),
        },
        body: JSON.stringify({
          expiresInHours: parseInt(newKeyExpiry)
        })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to create key');
      }

      setShowCreateModal(false);
      setNewKeyExpiry(24);
      fetchKeys();
    } catch (err) {
      setError(err.message);
    } finally {
      setCreatingKey(false);
    }
  };

  const handleRevokeKey = async (keyId) => {
    if (!confirm('Are you sure you want to revoke this key?')) return;

    try {
      const response = await fetch('/.netlify/functions/security?action=update-key', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          ...getAdminHeaders(),
        },
        body: JSON.stringify({
          id: keyId,
          is_revoked: true
        })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to revoke key');
      }

      fetchKeys();
    } catch (err) {
      setError(err.message);
    }
  };

  const handleDeleteKey = async (keyId) => {
    if (!confirm('Are you sure you want to delete this key? This action cannot be undone.')) return;

    try {
      const response = await fetch(`/.netlify/functions/security?action=delete-key&id=${keyId}`, {
        method: 'DELETE',
        headers: {
          ...getAdminHeaders(),
          'Content-Type': 'application/json',
        }
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to delete key');
      }

      fetchKeys();
    } catch (err) {
      setError(err.message);
    }
  };

  const getKeyStatus = (key) => {
    if (key.is_revoked) return { label: 'Revoked', color: 'red' };
    if (new Date(key.expires_at) < new Date()) return { label: 'Expired', color: 'yellow' };
    return { label: 'Active', color: 'green' };
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString();
  };

  const formatExpiryTime = (expiresAt) => {
    const expiry = new Date(expiresAt);
    const now = new Date();
    const diff = expiry - now;
    
    if (diff <= 0) return 'Expired';
    
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    return `${hours}h ${minutes}m`;
  };

  const handleSearch = () => {
    setCurrentPage(1);
    fetchKeys();
  };

  const handleFilterChange = (newFilter) => {
    setStatusFilter(newFilter);
    setCurrentPage(1);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Key Management</h1>
          <p className="text-gray-600 dark:text-gray-400">Manage license keys and monitor usage</p>
        </div>
        <Button
          onClick={() => setShowCreateModal(true)}
          className="bg-blue-500 hover:bg-blue-600 text-white"
          ariaLabel="Create new key"
        >
          <FiPlus className="mr-2" />
          Create Key
        </Button>
      </div>

      {/* Filters */}
      <Card className="p-4">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <Input
                placeholder="Search keys..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                className="pl-10"
              />
            </div>
          </div>
          <div className="flex gap-2">
            <select
              value={statusFilter}
              onChange={(e) => handleFilterChange(e.target.value)}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
            >
              <option value="all">All Keys</option>
              <option value="active">Active</option>
              <option value="expired">Expired</option>
              <option value="revoked">Revoked</option>
            </select>
            <Button
              onClick={fetchKeys}
              variant="outline"
              disabled={loading}
              ariaLabel="Refresh keys"
            >
              <FiRefreshCw className={cn("w-4 h-4", loading && "animate-spin")} />
            </Button>
          </div>
        </div>
      </Card>

      {/* Keys Table */}
      <Card>
        {loading ? (
          <div className="p-8 text-center">
            <FiRefreshCw className="w-8 h-8 animate-spin text-blue-500 mx-auto mb-4" />
            <p className="text-gray-600 dark:text-gray-400">Loading keys...</p>
          </div>
        ) : error ? (
          <div className="p-8 text-center">
            <p className="text-red-600 dark:text-red-400 mb-4">{error}</p>
            <Button onClick={fetchKeys} variant="outline">
              Try Again
            </Button>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Key Code</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead>Expires</TableHead>
                  <TableHead>Usage</TableHead>
                  <TableHead>HWID</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {keys.map((key) => {
                  const status = getKeyStatus(key);
                  const hwidBinding = key.hwid_bindings?.[0];
                  
                  return (
                    <TableRow key={key.id}>
                      <TableCell>
                        <code className="text-sm font-mono bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded">
                          {key.key_code}
                        </code>
                      </TableCell>
                      <TableCell>
                        <Badge 
                          className={cn(
                            status.color === 'green' && 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
                            status.color === 'yellow' && 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
                            status.color === 'red' && 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                          )}
                        >
                          {status.label}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-sm text-gray-600 dark:text-gray-400">
                        {formatDate(key.created_at)}
                      </TableCell>
                      <TableCell className="text-sm text-gray-600 dark:text-gray-400">
                        {formatExpiryTime(key.expires_at)}
                      </TableCell>
                      <TableCell className="text-sm text-gray-600 dark:text-gray-400">
                        {key.usage_count || 0}
                      </TableCell>
                      <TableCell>
                        {Array.isArray(key.hwid_bindings) && key.hwid_bindings.length > 0 ? (
                          <button
                            className="text-blue-500 underline hover:text-blue-700 focus:outline-none"
                            onClick={() => { setSelectedHWID(key.hwid_bindings); setShowHWIDModal(true); }}
                            aria-label="View HWID details"
                            tabIndex={0}
                          >
                            {key.hwid_bindings[0].hwid_hash.slice(0, 8)}... ({key.hwid_bindings.length})
                          </button>
                        ) : (
                          <span className="text-gray-400">Not redeemed</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          {!key.is_revoked && (
                            <Button
                              onClick={() => handleRevokeKey(key.id)}
                              variant="outline"
                              size="sm"
                              className="text-red-600 hover:text-red-700"
                              ariaLabel="Revoke key"
                            >
                              Revoke
                            </Button>
                          )}
                          <Button
                            onClick={() => handleDeleteKey(key.id)}
                            variant="outline"
                            size="sm"
                            className="text-red-600 hover:text-red-700"
                            ariaLabel="Delete key"
                          >
                            <FiTrash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </div>
        )}

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex items-center justify-between p-4 border-t">
            <div className="text-sm text-gray-600 dark:text-gray-400">
              Page {currentPage} of {totalPages}
            </div>
            <div className="flex gap-2">
              <Button
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
                variant="outline"
                size="sm"
              >
                Previous
              </Button>
              <Button
                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                disabled={currentPage === totalPages}
                variant="outline"
                size="sm"
              >
                Next
              </Button>
            </div>
          </div>
        )}
      </Card>

      {/* Create Key Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <Card className="p-6 w-full max-w-md">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-bold">Create New Key</h2>
              <Button
                onClick={() => setShowCreateModal(false)}
                variant="outline"
                size="sm"
                ariaLabel="Close modal"
              >
                <FiX className="w-4 h-4" />
              </Button>
            </div>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">
                  Expiry Time (hours)
                </label>
                <Input
                  type="number"
                  value={newKeyExpiry}
                  onChange={(e) => setNewKeyExpiry(e.target.value)}
                  min="1"
                  max="168"
                  className="w-full"
                />
              </div>
              
              <div className="flex gap-2">
                <Button
                  onClick={handleCreateKey}
                  disabled={creatingKey}
                  className="flex-1"
                >
                  {creatingKey ? 'Creating...' : 'Create Key'}
                </Button>
                <Button
                  onClick={() => setShowCreateModal(false)}
                  variant="outline"
                  className="flex-1"
                >
                  Cancel
                </Button>
              </div>
            </div>
          </Card>
        </div>
      )}

      {/* HWID Details Modal */}
      {showHWIDModal && (
        <Dialog open={showHWIDModal} onOpenChange={setShowHWIDModal}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>HWID Details</DialogTitle>
            </DialogHeader>
            <div className="space-y-2">
              {selectedHWID && selectedHWID.map((hwid, idx) => (
                <div key={idx} className="border-b border-gray-700 pb-2 mb-2">
                  <div><span className="font-semibold">HWID:</span> <span className="break-all">{hwid.hwid_hash}</span></div>
                  {hwid.roblox_username && <div><span className="font-semibold">Roblox Username:</span> {hwid.roblox_username}</div>}
                  {hwid.bound_at && <div><span className="font-semibold">Bound At:</span> {new Date(hwid.bound_at).toLocaleString()}</div>}
                </div>
              ))}
            </div>
            <DialogFooter>
              <Button onClick={() => setShowHWIDModal(false)} className="bg-gray-700 text-white">Close</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};

export default AdminKeyManagement; 