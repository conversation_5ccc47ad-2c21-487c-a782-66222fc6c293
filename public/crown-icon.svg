<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none">
  <defs>
    <linearGradient id="crownGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#fbbf24;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#f59e0b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#d97706;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="gemGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ef4444;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#dc2626;stop-opacity:1" />
    </linearGradient>
    <filter id="crownGlow">
      <feGaussianBlur stdDeviation="1" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Crown base -->
  <path d="M5 16h14l-1.5-8L15 10l-3-4-3 4-2.5-2L5 16z" fill="url(#crownGradient)" filter="url(#crownGlow)"/>
  
  <!-- Crown band -->
  <rect x="4" y="16" width="16" height="3" rx="1" fill="url(#crownGradient)"/>
  
  <!-- Crown gems -->
  <circle cx="8" cy="12" r="1" fill="url(#gemGradient)"/>
  <circle cx="12" cy="9" r="1.2" fill="url(#gemGradient)"/>
  <circle cx="16" cy="12" r="1" fill="url(#gemGradient)"/>
  
  <!-- Highlights for depth -->
  <path d="M6 16h12l-1-6L15 11l-3-3.5-3 3.5-2-1L6 16z" fill="rgba(255,255,255,0.2)"/>
  <rect x="5" y="16" width="14" height="1" fill="rgba(255,255,255,0.3)"/>
</svg>
