<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 64 64">
  <!-- Background circle with gradient -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#8b5cf6;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="textGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e2e8f0;stop-opacity:1" />
    </linearGradient>
    <!-- Subtle glow effect -->
    <filter id="glow">
      <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Main background circle -->
  <circle cx="32" cy="32" r="30" fill="url(#bgGradient)" filter="url(#glow)"/>
  
  <!-- Inner shadow circle for depth -->
  <circle cx="32" cy="32" r="28" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/>
  
  <!-- PM Text -->
  <text x="32" y="40" font-family="Arial, sans-serif" font-size="24" font-weight="bold" 
        text-anchor="middle" fill="url(#textGradient)">PM</text>
  
  <!-- Subtle highlight on top -->
  <ellipse cx="32" cy="20" rx="20" ry="8" fill="rgba(255,255,255,0.1)"/>
</svg>
