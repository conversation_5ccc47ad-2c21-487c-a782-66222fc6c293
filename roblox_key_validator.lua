å
return function(correctFunction)
    local HttpService = game:GetService("HttpService")
    local Players = game:GetService("Players")
    local RbxAnalyticsService = game:GetService("RbxAnalyticsService")
    local RunService = game:GetService("RunService")

    -- CONFIG
    local FINGERPRINT_API = "https://checkingbefore.netlify.app/api/fingerprint"
    local VALIDATE_API = "https://projectmadara.com/.netlify/functions/validate-key"

    -- Fetch HWID
    local function getHWID()
        return RbxAnalyticsService:GetClientId()
    end

    -- Fetch device fingerprint from your website
    local function fetchFingerprint(hwid)
        local url = FINGERPRINT_API .. "?hwid=" .. hwid
        local success, response = pcall(function()
            return game:HttpGet(url)
        end)
        if not success then
            warn("Failed to fetch fingerprint: " .. tostring(response))
            return nil
        end
        local data = HttpService:JSONDecode(response)
        return data.fingerprint
    end

    -- Validate key with backend
    local function validateKey(key, hwid, fingerprint)
        local payload = HttpService:JSONEncode({
            keyCode = key,
            hwidHash = hwid,
            fingerprint = fingerprint
        })
        local success, response = pcall(function()
            return game:HttpPost(VALIDATE_API, payload, Enum.HttpContentType.ApplicationJson)
        end)
        if not success then
            warn("Network error: " .. tostring(response))
            return false, "Network error"
        end
        local data
        success, data = pcall(function()
            return HttpService:JSONDecode(response)
        end)
        if not success then
            warn("Invalid server response: " .. tostring(data))
            return false, "Invalid server response"
        end
        if data.valid == true or data.success == true then
            return true, "Key valid"
        else
            return false, data.error or data.message or "Invalid key"
        end
    end

    -- UI Setup (same as your code, but with fingerprint auto-fetch)
    local function showKeyUI()
        local hwid = getHWID()
        local fingerprint = fetchFingerprint(hwid)
        if not fingerprint then
            error("Could not fetch device fingerprint. Please try again later.")
            return
        end

        local ScreenGui = Instance.new("ScreenGui")
        local Frame = Instance.new("Frame")
        local Title = Instance.new("TextLabel")
        local KeyBox = Instance.new("TextBox")
        local SubmitButton = Instance.new("TextButton")
        local StatusLabel = Instance.new("TextLabel")
        local CloseButton = Instance.new("TextButton")

        ScreenGui.Name = "KeySystem"
        ScreenGui.Parent = Players.LocalPlayer:WaitForChild("PlayerGui")
        ScreenGui.ResetOnSpawn = false

        Frame.Parent = ScreenGui
        Frame.BackgroundColor3 = Color3.fromRGB(35, 35, 35)
        Frame.BorderSizePixel = 0
        Frame.Position = UDim2.new(0.5, -200, 0.5, -150)
        Frame.Size = UDim2.new(0, 400, 0, 300)
        Frame.Active = true
        Frame.Draggable = true

        local Corner = Instance.new("UICorner")
        Corner.CornerRadius = UDim.new(0, 10)
        Corner.Parent = Frame

        Title.Parent = Frame
        Title.BackgroundTransparency = 1
        Title.Position = UDim2.new(0, 0, 0, 10)
        Title.Size = UDim2.new(1, 0, 0, 50)
        Title.Font = Enum.Font.GothamBold
        Title.Text = "Script Authorization Required"
        Title.TextColor3 = Color3.fromRGB(255, 255, 255)
        Title.TextSize = 18

        KeyBox.Parent = Frame
        KeyBox.BackgroundColor3 = Color3.fromRGB(50, 50, 50)
        KeyBox.BorderSizePixel = 0
        KeyBox.Position = UDim2.new(0.1, 0, 0.3, 0)
        KeyBox.Size = UDim2.new(0.8, 0, 0, 40)
        KeyBox.Font = Enum.Font.Gotham
        KeyBox.PlaceholderText = "Enter your license key..."
        KeyBox.Text = ""
        KeyBox.TextColor3 = Color3.fromRGB(255, 255, 255)
        KeyBox.TextSize = 14

        local KeyCorner = Instance.new("UICorner")
        KeyCorner.CornerRadius = UDim.new(0, 5)
        KeyCorner.Parent = KeyBox

        SubmitButton.Parent = Frame
        SubmitButton.BackgroundColor3 = Color3.fromRGB(0, 162, 255)
        SubmitButton.BorderSizePixel = 0
        SubmitButton.Position = UDim2.new(0.1, 0, 0.55, 0)
        SubmitButton.Size = UDim2.new(0.35, 0, 0, 40)
        SubmitButton.Font = Enum.Font.GothamBold
        SubmitButton.Text = "Verify Key"
        SubmitButton.TextColor3 = Color3.fromRGB(255, 255, 255)
        SubmitButton.TextSize = 14

        local SubmitCorner = Instance.new("UICorner")
        SubmitCorner.CornerRadius = UDim.new(0, 5)
        SubmitCorner.Parent = SubmitButton

        CloseButton.Parent = Frame
        CloseButton.BackgroundColor3 = Color3.fromRGB(255, 60, 60)
        CloseButton.BorderSizePixel = 0
        CloseButton.Position = UDim2.new(0.55, 0, 0.55, 0)
        CloseButton.Size = UDim2.new(0.35, 0, 0, 40)
        CloseButton.Font = Enum.Font.GothamBold
        CloseButton.Text = "Close"
        CloseButton.TextColor3 = Color3.fromRGB(255, 255, 255)
        CloseButton.TextSize = 14

        local CloseCorner = Instance.new("UICorner")
        CloseCorner.CornerRadius = UDim.new(0, 5)
        CloseCorner.Parent = CloseButton

        StatusLabel.Parent = Frame
        StatusLabel.BackgroundTransparency = 1
        StatusLabel.Position = UDim2.new(0.1, 0, 0.75, 0)
        StatusLabel.Size = UDim2.new(0.8, 0, 0, 60)
        StatusLabel.Font = Enum.Font.Gotham
        StatusLabel.Text = "Please enter your license key to continue."
        StatusLabel.TextColor3 = Color3.fromRGB(200, 200, 200)
        StatusLabel.TextSize = 12
        StatusLabel.TextWrapped = true
        StatusLabel.TextYAlignment = Enum.TextYAlignment.Top

        -- Button animations
        local function animateButton(button, hoverColor, normalColor)
            button.MouseEnter:Connect(function()
                button:TweenSize(UDim2.new(button.Size.X.Scale, button.Size.X.Offset, button.Size.Y.Scale, button.Size.Y.Offset + 2), "Out", "Quad", 0.1, true)
                button.BackgroundColor3 = hoverColor
            end)
            button.MouseLeave:Connect(function()
                button:TweenSize(UDim2.new(button.Size.X.Scale, button.Size.X.Offset, button.Size.Y.Scale, button.Size.Y.Offset - 2), "Out", "Quad", 0.1, true)
                button.BackgroundColor3 = normalColor
            end)
        end

        animateButton(SubmitButton, Color3.fromRGB(0, 140, 220), Color3.fromRGB(0, 162, 255))
        animateButton(CloseButton, Color3.fromRGB(220, 50, 50), Color3.fromRGB(255, 60, 60))

        -- Key validation logic
        local function validateKey()
            local key = KeyBox.Text:gsub("%s+", "")
            if #key == 0 then
                StatusLabel.Text = "Please enter a valid license key."
                StatusLabel.TextColor3 = Color3.fromRGB(255, 100, 100)
                return
            end
            StatusLabel.Text = "Validating key... Please wait."
            StatusLabel.TextColor3 = Color3.fromRGB(255, 255, 100)
            SubmitButton.Text = "Validating..."
            SubmitButton.BackgroundColor3 = Color3.fromRGB(100, 100, 100)

            local isValid, message = validateKey(key, hwid, fingerprint)
            if isValid then
                StatusLabel.Text = "Key validated successfully! Loading script..."
                StatusLabel.TextColor3 = Color3.fromRGB(100, 255, 100)
                wait(1)
                ScreenGui:Destroy()
                if correctFunction and type(correctFunction) == "function" then
                    local scriptId = "main-script-001"
                    local protectedScript = createKillSwitchProtectedScript(scriptId, correctFunction)
                    protectedScript()
                end
            else
                StatusLabel.Text = "Invalid license key: " .. tostring(message)
                StatusLabel.TextColor3 = Color3.fromRGB(255, 100, 100)
                SubmitButton.Text = "Verify Key"
                SubmitButton.BackgroundColor3 = Color3.fromRGB(0, 162, 255)
            end
        end

        SubmitButton.MouseButton1Click:Connect(validateKey)
        KeyBox.FocusLost:Connect(function(enterPressed)
            if enterPressed then
                validateKey()
            end
        end)
        CloseButton.MouseButton1Click:Connect(function()
            ScreenGui:Destroy()
        end)

        -- Anti-tamper protection
        RunService.Heartbeat:Connect(function()
            if not ScreenGui.Parent and not getgenv().scriptRunning then
                warn("Key system tampered with!")
                return
            end
        end)
    end

    showKeyUI()
end 